{% extends 'config/base_config.html' %}
{% load crispy_forms_tags %}

{% block title %}Testar {{ config.name }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="h3 mb-1">
                <i class="fas fa-vial text-primary me-2"></i>
                Testar Configuração: {{ config.name }}
            </h2>
            <p class="text-muted mb-0">Teste a conectividade e envio de emails</p>
        </div>
        <div>
            <a href="{% url 'config:email_configs' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Voltar
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Informações da Configuração -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Detalhes da Configuração
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Nome:</strong> {{ config.name }}</p>
                            <p><strong>Servidor:</strong> {{ config.email_host }}</p>
                            <p><strong>Porta:</strong> {{ config.email_port }}</p>
                            <p><strong>Usuário:</strong> {{ config.email_host_user }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>TLS:</strong> 
                                {% if config.email_use_tls %}
                                    <span class="badge bg-success">Ativado</span>
                                {% else %}
                                    <span class="badge bg-secondary">Desativado</span>
                                {% endif %}
                            </p>
                            <p><strong>SSL:</strong> 
                                {% if config.email_use_ssl %}
                                    <span class="badge bg-success">Ativado</span>
                                {% else %}
                                    <span class="badge bg-secondary">Desativado</span>
                                {% endif %}
                            </p>
                            <p><strong>Email Remetente:</strong> {{ config.default_from_email }}</p>
                            <p><strong>Status:</strong> 
                                {% if config.is_active %}
                                    <span class="badge bg-success">Ativo</span>
                                {% else %}
                                    <span class="badge bg-secondary">Inativo</span>
                                {% endif %}
                                {% if config.is_default %}
                                    <span class="badge bg-warning text-dark">Padrão</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    
                    {% if config.description %}
                    <hr>
                    <p><strong>Descrição:</strong> {{ config.description }}</p>
                    {% endif %}
                </div>
            </div>

            <!-- Formulário de Teste -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-play me-2"></i>Executar Testes
                    </h5>
                </div>
                <div class="card-body">
                    {% crispy form %}
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Histórico de Testes -->
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-history me-2"></i>Último Teste
                    </h6>
                </div>
                <div class="card-body">
                    {% if config.last_tested_at %}
                        <div class="d-flex align-items-center mb-3">
                            {% if config.last_test_result.success %}
                                <i class="fas fa-check-circle text-success fa-2x me-3"></i>
                                <div>
                                    <h6 class="mb-0 text-success">Sucesso</h6>
                                    <small class="text-muted">{{ config.last_tested_at|date:"d/m/Y H:i" }}</small>
                                </div>
                            {% else %}
                                <i class="fas fa-times-circle text-danger fa-2x me-3"></i>
                                <div>
                                    <h6 class="mb-0 text-danger">Falha</h6>
                                    <small class="text-muted">{{ config.last_tested_at|date:"d/m/Y H:i" }}</small>
                                </div>
                            {% endif %}
                        </div>
                        
                        {% if config.last_test_result.message %}
                        <div class="alert {% if config.last_test_result.success %}alert-success{% else %}alert-danger{% endif %}">
                            {{ config.last_test_result.message }}
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center text-muted">
                            <i class="fas fa-clock fa-2x mb-2"></i>
                            <p>Nenhum teste realizado ainda</p>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Estatísticas -->
            <div class="card mt-3">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Estatísticas
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12 mb-3">
                            <h4 class="text-primary">{{ config.emails_sent_count }}</h4>
                            <small class="text-muted">Emails Enviados</small>
                        </div>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <h6 class="{% if config.is_active %}text-success{% else %}text-secondary{% endif %}">
                                {% if config.is_active %}Ativo{% else %}Inativo{% endif %}
                            </h6>
                            <small class="text-muted">Status</small>
                        </div>
                        <div class="col-6">
                            <h6 class="{% if config.is_default %}text-warning{% else %}text-muted{% endif %}">
                                {% if config.is_default %}Padrão{% else %}Backup{% endif %}
                            </h6>
                            <small class="text-muted">Tipo</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Ações Rápidas -->
            <div class="card mt-3">
                <div class="card-header bg-secondary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-tools me-2"></i>Ações
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'config:email_config_update' config.pk %}" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-2"></i>Editar Configuração
                        </a>
                        
                        {% if not config.is_default %}
                        <button type="button" class="btn btn-outline-warning btn-sm" 
                                onclick="setAsDefault()">
                            <i class="fas fa-star me-2"></i>Definir como Padrão
                        </button>
                        {% endif %}
                        
                        <a href="{% url 'config:email_configs' %}" 
                           class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-list me-2"></i>Ver Todas
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function setAsDefault() {
    if (confirm('Definir "{{ config.name }}" como configuração padrão?')) {
        fetch('{% url "config:email_config_set_default" config.pk %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erro: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao definir configuração padrão');
        });
    }
}

// Feedback visual nos botões de teste
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            const submitBtn = e.submitter;
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                const action = submitBtn.value;
                
                if (action === 'test_connection') {
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Testando Conexão...';
                } else if (action === 'send_test') {
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enviando Email...';
                }
                
                submitBtn.disabled = true;
                
                // Restaurar após 10 segundos (fallback)
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 10000);
            }
        });
    }
});
</script>
{% endblock %}
