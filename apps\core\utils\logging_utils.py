"""
Utilitários para logging estruturado
"""

import time
import logging
import functools
from typing import Any, Dict, Optional
from django.contrib.auth import get_user_model

User = get_user_model()


class ServiceLogger:
    """Logger especializado para services"""
    
    def __init__(self, service_name: str):
        self.logger = logging.getLogger(f'apps.{service_name}')
        self.service_name = service_name
    
    def log_operation_start(self, operation: str, **context):
        """Log do início de uma operação"""
        self.logger.info(
            f"[{self.service_name}] Iniciando operação: {operation}",
            extra={
                'service': self.service_name,
                'operation': operation,
                'operation_status': 'started',
                **context
            }
        )
    
    def log_operation_success(self, operation: str, execution_time: float = None, **context):
        """Log de operação bem-sucedida"""
        message = f"[{self.service_name}] Operação concluída com sucesso: {operation}"
        if execution_time:
            message += f" ({execution_time:.3f}s)"
        
        self.logger.info(
            message,
            extra={
                'service': self.service_name,
                'operation': operation,
                'operation_status': 'success',
                'execution_time': execution_time,
                **context
            }
        )
    
    def log_operation_error(self, operation: str, error: Exception, execution_time: float = None, **context):
        """Log de erro em operação"""
        message = f"[{self.service_name}] Erro na operação: {operation} - {str(error)}"
        if execution_time:
            message += f" ({execution_time:.3f}s)"
        
        self.logger.error(
            message,
            extra={
                'service': self.service_name,
                'operation': operation,
                'operation_status': 'error',
                'error_type': type(error).__name__,
                'error_message': str(error),
                'execution_time': execution_time,
                **context
            },
            exc_info=True
        )
    
    def log_validation_error(self, operation: str, validation_errors: Dict, **context):
        """Log de erro de validação"""
        self.logger.warning(
            f"[{self.service_name}] Erro de validação: {operation}",
            extra={
                'service': self.service_name,
                'operation': operation,
                'operation_status': 'validation_error',
                'validation_errors': validation_errors,
                **context
            }
        )
    
    def log_business_rule_violation(self, operation: str, rule: str, **context):
        """Log de violação de regra de negócio"""
        self.logger.warning(
            f"[{self.service_name}] Violação de regra de negócio: {operation} - {rule}",
            extra={
                'service': self.service_name,
                'operation': operation,
                'operation_status': 'business_rule_violation',
                'business_rule': rule,
                **context
            }
        )


def log_service_operation(operation_name: str = None, log_args: bool = False, log_result: bool = False):
    """
    Decorator para logging automático de operações de service
    
    Args:
        operation_name: Nome da operação (usa nome da função se não especificado)
        log_args: Se deve logar argumentos da função
        log_result: Se deve logar resultado da função
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            # Determina nome da operação
            op_name = operation_name or func.__name__
            
            # Cria logger para o service
            service_name = self.__class__.__module__.replace('apps.', '').replace('.services.', '.')
            logger = ServiceLogger(service_name)
            
            # Prepara contexto
            context = {}
            
            # Adiciona argumentos se solicitado
            if log_args and args:
                context['args'] = str(args)[:200]  # Limita tamanho
            if log_args and kwargs:
                context['kwargs'] = {k: str(v)[:100] for k, v in kwargs.items()}
            
            # Adiciona informações do usuário se disponível
            if hasattr(self, 'user') and self.user:
                context['user_id'] = self.user.id
                context['username'] = self.user.username
            
            # Log início da operação
            start_time = time.time()
            logger.log_operation_start(op_name, **context)
            
            try:
                # Executa função
                result = func(self, *args, **kwargs)
                
                # Calcula tempo de execução
                execution_time = time.time() - start_time
                
                # Adiciona resultado se solicitado
                if log_result and result is not None:
                    context['result'] = str(result)[:200]  # Limita tamanho
                
                # Log sucesso
                logger.log_operation_success(op_name, execution_time, **context)
                
                return result
                
            except Exception as e:
                # Calcula tempo de execução
                execution_time = time.time() - start_time
                
                # Log erro
                logger.log_operation_error(op_name, e, execution_time, **context)
                
                # Re-levanta exceção
                raise
        
        return wrapper
    return decorator


def log_database_operation(operation_type: str = None):
    """
    Decorator para logging de operações de database
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            from django.db import connection
            
            # Conta queries antes
            queries_before = len(connection.queries)
            start_time = time.time()
            
            # Determina tipo de operação
            op_type = operation_type or func.__name__
            
            # Logger específico para database
            logger = logging.getLogger('havoc.database')
            
            try:
                # Executa função
                result = func(self, *args, **kwargs)
                
                # Calcula métricas
                execution_time = time.time() - start_time
                queries_count = len(connection.queries) - queries_before
                
                # Log da operação
                logger.info(
                    f"Database operation: {op_type}",
                    extra={
                        'operation_type': op_type,
                        'execution_time': round(execution_time, 3),
                        'queries_count': queries_count,
                        'repository': self.__class__.__name__,
                    }
                )
                
                # Warning para operações lentas
                if execution_time > 1.0:
                    logger.warning(
                        f"Slow database operation: {op_type} ({execution_time:.3f}s)",
                        extra={
                            'operation_type': op_type,
                            'execution_time': execution_time,
                            'queries_count': queries_count,
                            'performance_issue': 'slow_operation'
                        }
                    )
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                queries_count = len(connection.queries) - queries_before
                
                logger.error(
                    f"Database operation failed: {op_type}",
                    extra={
                        'operation_type': op_type,
                        'execution_time': round(execution_time, 3),
                        'queries_count': queries_count,
                        'error_type': type(e).__name__,
                        'error_message': str(e),
                    },
                    exc_info=True
                )
                
                raise
        
        return wrapper
    return decorator


class SecurityLogger:
    """Logger especializado para eventos de segurança"""
    
    def __init__(self):
        self.logger = logging.getLogger('havoc.security')
    
    def log_login_attempt(self, email: str, success: bool, ip_address: str, user_agent: str):
        """Log de tentativa de login"""
        status = 'success' if success else 'failed'
        
        self.logger.info(
            f"Login attempt: {email} - {status}",
            extra={
                'event_type': 'login_attempt',
                'email': email,
                'success': success,
                'ip_address': ip_address,
                'user_agent': user_agent,
            }
        )
        
        if not success:
            self.logger.warning(
                f"Failed login attempt for: {email}",
                extra={
                    'event_type': 'failed_login',
                    'email': email,
                    'ip_address': ip_address,
                    'user_agent': user_agent,
                    'security_event': 'authentication_failure'
                }
            )
    
    def log_password_reset_request(self, email: str, ip_address: str):
        """Log de solicitação de reset de senha"""
        self.logger.info(
            f"Password reset requested for: {email}",
            extra={
                'event_type': 'password_reset_request',
                'email': email,
                'ip_address': ip_address,
            }
        )
    
    def log_account_lockout(self, email: str, ip_address: str, attempts: int):
        """Log de bloqueio de conta"""
        self.logger.warning(
            f"Account locked: {email} after {attempts} failed attempts",
            extra={
                'event_type': 'account_lockout',
                'email': email,
                'ip_address': ip_address,
                'failed_attempts': attempts,
                'security_event': 'account_lockout'
            }
        )
    
    def log_permission_denied(self, user_id: int, resource: str, action: str, ip_address: str):
        """Log de acesso negado"""
        self.logger.warning(
            f"Permission denied: User {user_id} tried to {action} {resource}",
            extra={
                'event_type': 'permission_denied',
                'user_id': user_id,
                'resource': resource,
                'action': action,
                'ip_address': ip_address,
                'security_event': 'unauthorized_access_attempt'
            }
        )
    
    def log_suspicious_activity(self, description: str, ip_address: str, **context):
        """Log de atividade suspeita"""
        self.logger.warning(
            f"Suspicious activity detected: {description}",
            extra={
                'event_type': 'suspicious_activity',
                'description': description,
                'ip_address': ip_address,
                'security_event': 'suspicious_activity',
                **context
            }
        )


# Instância global do security logger
security_logger = SecurityLogger()


def log_security_event(event_type: str):
    """Decorator para logging automático de eventos de segurança"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                result = func(*args, **kwargs)
                
                # Log evento de segurança bem-sucedido
                security_logger.logger.info(
                    f"Security event: {event_type}",
                    extra={
                        'event_type': event_type,
                        'function': func.__name__,
                        'success': True,
                    }
                )
                
                return result
                
            except Exception as e:
                # Log evento de segurança com falha
                security_logger.logger.error(
                    f"Security event failed: {event_type}",
                    extra={
                        'event_type': event_type,
                        'function': func.__name__,
                        'success': False,
                        'error_type': type(e).__name__,
                        'error_message': str(e),
                    },
                    exc_info=True
                )
                
                raise
        
        return wrapper
    return decorator
