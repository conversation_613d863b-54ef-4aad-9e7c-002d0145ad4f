{% extends "base.html" %}
{% load static %}

{% block title %}Configurações do Sistema - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
.config-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.config-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px 15px 0 0;
}

.info-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    border-left: 4px solid #007bff;
}

.info-card.system { border-left-color: #28a745; }
.info-card.django { border-left-color: #17a2b8; }
.info-card.database { border-left-color: #ffc107; }
.info-card.stats { border-left-color: #dc3545; }

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.info-label {
    font-weight: 600;
    color: #495057;
}

.info-value {
    color: #6c757d;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.progress-bar-custom {
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
    overflow: hidden;
    margin-top: 0.5rem;
}

.progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-fill.success { background: linear-gradient(90deg, #28a745, #20c997); }
.progress-fill.warning { background: linear-gradient(90deg, #ffc107, #fd7e14); }
.progress-fill.danger { background: linear-gradient(90deg, #dc3545, #e83e8c); }

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-badge.success {
    background: #d4edda;
    color: #155724;
}

.status-badge.warning {
    background: #fff3cd;
    color: #856404;
}

.status-badge.danger {
    background: #f8d7da;
    color: #721c24;
}

.nav-tabs-custom {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 2rem;
}

.nav-tabs-custom .nav-link {
    border: none;
    border-radius: 0;
    color: #6c757d;
    font-weight: 600;
    padding: 1rem 1.5rem;
    transition: all 0.2s ease;
}

.nav-tabs-custom .nav-link:hover {
    color: #007bff;
    border-bottom: 2px solid #007bff;
}

.nav-tabs-custom .nav-link.active {
    color: #007bff;
    background: none;
    border-bottom: 2px solid #007bff;
}

.table-stats {
    font-size: 0.9rem;
}

.table-stats th {
    background: #f8f9fa;
    border: none;
    font-weight: 600;
    color: #495057;
}

.table-stats td {
    border: none;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
}

@media (max-width: 768px) {
    .config-header {
        padding: 1.5rem;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="config-container">
        <div class="config-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-1">
                        <i class="fas fa-server me-2"></i>Configurações do Sistema
                    </h1>
                    <p class="mb-0 opacity-75">
                        Informações detalhadas sobre o sistema, Django e banco de dados
                    </p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex gap-2 justify-content-md-end">
                        <a href="{% url 'config:dashboard' %}" class="btn btn-light">
                            <i class="fas fa-arrow-left me-1"></i>Dashboard
                        </a>
                        <button class="btn btn-light" onclick="location.reload()">
                            <i class="fas fa-sync-alt me-1"></i>Atualizar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navegação por Abas -->
    <ul class="nav nav-tabs nav-tabs-custom" id="configTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                <i class="fas fa-desktop me-2"></i>Sistema
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="django-tab" data-bs-toggle="tab" data-bs-target="#django" type="button" role="tab">
                <i class="fab fa-python me-2"></i>Django
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="database-tab" data-bs-toggle="tab" data-bs-target="#database" type="button" role="tab">
                <i class="fas fa-database me-2"></i>Banco de Dados
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="stats-tab" data-bs-toggle="tab" data-bs-target="#stats" type="button" role="tab">
                <i class="fas fa-chart-bar me-2"></i>Estatísticas
            </button>
        </li>
    </ul>

    <!-- Conteúdo das Abas -->
    <div class="tab-content" id="configTabsContent">
        <!-- Aba Sistema -->
        <div class="tab-pane fade show active" id="system" role="tabpanel">
            <div class="info-card system">
                <h4><i class="fas fa-desktop me-2"></i>Informações do Sistema</h4>
                
                {% if system_info.error %}
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Erro ao obter informações do sistema: {{ system_info.error }}
                </div>
                {% else %}
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Plataforma:</span>
                        <span class="info-value">{{ system_info.platform }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Python:</span>
                        <span class="info-value">{{ system_info.python_version }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Arquitetura:</span>
                        <span class="info-value">{{ system_info.architecture }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Hostname:</span>
                        <span class="info-value">{{ system_info.hostname }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">CPUs:</span>
                        <span class="info-value">{{ system_info.cpu_count }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Processador:</span>
                        <span class="info-value">{{ system_info.processor|truncatechars:30 }}</span>
                    </div>
                </div>

                <!-- Métricas de Performance -->
                <div class="row mt-4">
                    <div class="col-md-4">
                        <h6>CPU</h6>
                        <div class="d-flex justify-content-between">
                            <span>{{ system_info.cpu_percent }}%</span>
                            <span class="status-badge {% if system_info.cpu_percent < 70 %}success{% elif system_info.cpu_percent < 90 %}warning{% else %}danger{% endif %}">
                                {% if system_info.cpu_percent < 70 %}Normal{% elif system_info.cpu_percent < 90 %}Alto{% else %}Crítico{% endif %}
                            </span>
                        </div>
                        <div class="progress-bar-custom">
                            <div class="progress-fill {% if system_info.cpu_percent < 70 %}success{% elif system_info.cpu_percent < 90 %}warning{% else %}danger{% endif %}" 
                                 style="width: {{ system_info.cpu_percent }}%"></div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <h6>Memória</h6>
                        <div class="d-flex justify-content-between">
                            <span>{{ system_info.memory_used }} / {{ system_info.memory_total }}</span>
                            <span class="status-badge {% if system_info.memory_percent < 70 %}success{% elif system_info.memory_percent < 90 %}warning{% else %}danger{% endif %}">
                                {{ system_info.memory_percent }}%
                            </span>
                        </div>
                        <div class="progress-bar-custom">
                            <div class="progress-fill {% if system_info.memory_percent < 70 %}success{% elif system_info.memory_percent < 90 %}warning{% else %}danger{% endif %}" 
                                 style="width: {{ system_info.memory_percent }}%"></div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <h6>Disco</h6>
                        <div class="d-flex justify-content-between">
                            <span>{{ system_info.disk_used }} / {{ system_info.disk_total }}</span>
                            <span class="status-badge {% if system_info.disk_percent < 70 %}success{% elif system_info.disk_percent < 90 %}warning{% else %}danger{% endif %}">
                                {{ system_info.disk_percent|floatformat:1 }}%
                            </span>
                        </div>
                        <div class="progress-bar-custom">
                            <div class="progress-fill {% if system_info.disk_percent < 70 %}success{% elif system_info.disk_percent < 90 %}warning{% else %}danger{% endif %}" 
                                 style="width: {{ system_info.disk_percent }}%"></div>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <div class="info-item">
                        <span class="info-label">Uptime:</span>
                        <span class="info-value">{{ system_info.uptime }}</span>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Aba Django -->
        <div class="tab-pane fade" id="django" role="tabpanel">
            <div class="info-card django">
                <h4><i class="fab fa-python me-2"></i>Configurações do Django</h4>
                
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Versão:</span>
                        <span class="info-value">{{ django_info.version }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Debug:</span>
                        <span class="status-badge {% if django_info.debug %}warning{% else %}success{% endif %}">
                            {% if django_info.debug %}Ativo{% else %}Inativo{% endif %}
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Secret Key:</span>
                        <span class="status-badge {% if django_info.secret_key_set %}success{% else %}danger{% endif %}">
                            {% if django_info.secret_key_set %}Configurada{% else %}Não Configurada{% endif %}
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Time Zone:</span>
                        <span class="info-value">{{ django_info.time_zone }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Idioma:</span>
                        <span class="info-value">{{ django_info.language_code }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Apps Instalados:</span>
                        <span class="info-value">{{ django_info.installed_apps_count }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Middlewares:</span>
                        <span class="info-value">{{ django_info.middleware_count }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Static URL:</span>
                        <span class="info-value">{{ django_info.static_url }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Media URL:</span>
                        <span class="info-value">{{ django_info.media_url }}</span>
                    </div>
                </div>

                <div class="mt-4">
                    <h6>Hosts Permitidos:</h6>
                    <div class="bg-light p-3 rounded">
                        {% for host in django_info.allowed_hosts %}
                            <span class="badge bg-secondary me-1">{{ host }}</span>
                        {% empty %}
                            <span class="text-muted">Nenhum host configurado</span>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Aba Banco de Dados -->
        <div class="tab-pane fade" id="database" role="tabpanel">
            <div class="info-card database">
                <h4><i class="fas fa-database me-2"></i>Informações do Banco de Dados</h4>
                
                {% if database_info.error %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Erro ao obter informações do banco: {{ database_info.error }}
                </div>
                {% endif %}

                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Engine:</span>
                        <span class="info-value">{{ database_info.engine|title }}</span>
                    </div>
                    {% if database_info.version %}
                    <div class="info-item">
                        <span class="info-label">Versão:</span>
                        <span class="info-value">{{ database_info.version|truncatechars:50 }}</span>
                    </div>
                    {% endif %}
                    <div class="info-item">
                        <span class="info-label">Nome:</span>
                        <span class="info-value">{{ database_info.name }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Host:</span>
                        <span class="info-value">{{ database_info.host }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Porta:</span>
                        <span class="info-value">{{ database_info.port }}</span>
                    </div>
                </div>

                {% if database_info.table_stats %}
                <div class="mt-4">
                    <h6>Estatísticas das Tabelas (Top 10)</h6>
                    <div class="table-responsive">
                        <table class="table table-stats">
                            <thead>
                                <tr>
                                    <th>Schema</th>
                                    <th>Tabela</th>
                                    <th>Inserções</th>
                                    <th>Atualizações</th>
                                    <th>Exclusões</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stat in database_info.table_stats %}
                                <tr>
                                    <td>{{ stat.0 }}</td>
                                    <td>{{ stat.1 }}</td>
                                    <td>{{ stat.2|default:0 }}</td>
                                    <td>{{ stat.3|default:0 }}</td>
                                    <td>{{ stat.4|default:0 }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Aba Estatísticas -->
        <div class="tab-pane fade" id="stats" role="tabpanel">
            <div class="info-card stats">
                <h4><i class="fas fa-chart-bar me-2"></i>Estatísticas do Sistema</h4>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>Usuários</h6>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="info-label">Total:</span>
                                <span class="info-value">{{ stats.total_users }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Ativos:</span>
                                <span class="info-value">{{ stats.active_users }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Staff:</span>
                                <span class="info-value">{{ stats.staff_users }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Superusuários:</span>
                                <span class="info-value">{{ stats.superusers }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>Sistema</h6>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="info-label">Grupos:</span>
                                <span class="info-value">{{ stats.total_groups }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Configs Avançadas:</span>
                                <span class="info-value">{{ stats.advanced_configs }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Logs Totais:</span>
                                <span class="info-value">{{ stats.total_logs }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Logs Hoje:</span>
                                <span class="info-value">{{ stats.recent_logs }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animação de entrada para os cards
    const cards = document.querySelectorAll('.info-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });

    // Animação das barras de progresso
    const progressBars = document.querySelectorAll('.progress-fill');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        
        setTimeout(() => {
            bar.style.width = width;
        }, 1000);
    });

    // Auto-refresh a cada 30 segundos para métricas em tempo real
    setInterval(() => {
        // Atualiza apenas as métricas de performance
        fetch(window.location.href)
            .then(response => response.text())
            .then(html => {
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                
                // Atualiza barras de progresso
                const currentBars = document.querySelectorAll('.progress-fill');
                const newBars = doc.querySelectorAll('.progress-fill');
                
                currentBars.forEach((bar, index) => {
                    if (newBars[index]) {
                        bar.style.width = newBars[index].style.width;
                    }
                });
            })
            .catch(error => console.log('Erro ao atualizar métricas:', error));
    }, 30000);
});
</script>
{% endblock %}
