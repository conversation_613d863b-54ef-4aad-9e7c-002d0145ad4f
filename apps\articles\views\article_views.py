from django.shortcuts import render, get_object_or_404, redirect
from django.views import View
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib.auth.decorators import login_required
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.db.models import Q, Count
from django.http import JsonResponse, Http404
from django.core.paginator import Paginator
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from apps.articles.models import Article, Category, Comment, Tag
from apps.articles.services.article_service import ArticleService
from apps.articles.repositories.article_repository import DjangoArticleRepository
from apps.core.utils.logging_utils import log_service_operation
import logging

logger = logging.getLogger('apps.articles.views')

class ArticleListView(ListView):
    """View para listar artigos"""
    model = Article
    template_name = 'articles/article_list.html'
    context_object_name = 'articles'
    paginate_by = 12

    def get_queryset(self):
        """Retorna queryset filtrado"""
        queryset = Article.objects.published().select_related(
            'author', 'category'
        ).prefetch_related('tags')

        # Filtro por categoria
        category_slug = self.request.GET.get('category')
        if category_slug:
            try:
                category = Category.objects.get(slug=category_slug, is_active=True)
                queryset = queryset.filter(category=category)
                logger.info(f"Filtrando artigos por categoria: {category.name}")
            except Category.DoesNotExist:
                logger.warning(f"Categoria não encontrada: {category_slug}")
                queryset = queryset.none()

        # Filtro por tag
        tag_slug = self.request.GET.get('tag')
        if tag_slug:
            queryset = queryset.filter(tags__slug=tag_slug)
            logger.info(f"Filtrando artigos por tag: {tag_slug}")

        # Filtro por busca
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(content__icontains=search) |
                Q(excerpt__icontains=search) |
                Q(tags__name__icontains=search)
            ).distinct()
            logger.info(f"Busca realizada: {search}")

        # Ordenação
        order_by = self.request.GET.get('order', '-created_at')
        valid_orders = ['-created_at', 'created_at', '-updated_at', 'title', '-title']
        if order_by in valid_orders:
            queryset = queryset.order_by(order_by)
        else:
            queryset = queryset.order_by('-created_at')

        return queryset

    def get_context_data(self, **kwargs):
        """Adiciona contexto extra"""
        context = super().get_context_data(**kwargs)

        # Categorias ativas
        context['categories'] = Category.objects.filter(is_active=True).annotate(
            article_count=Count('articles', filter=Q(articles__status='published'))
        )

        # Tags populares
        context['popular_tags'] = Tag.objects.annotate(
            article_count=Count('articles', filter=Q(articles__status='published'))
        ).filter(article_count__gt=0).order_by('-article_count')[:10]

        # Filtros atuais
        context['current_category'] = self.request.GET.get('category')
        context['current_tag'] = self.request.GET.get('tag')
        context['search_query'] = self.request.GET.get('search', '')
        context['current_order'] = self.request.GET.get('order', '-created_at')

        # Estatísticas
        context['total_articles'] = Article.objects.published().count()

        return context


@method_decorator(cache_page(60 * 15), name='dispatch')  # Cache por 15 minutos
class ArticleDetailView(DetailView):
    """View para visualizar artigo"""
    model = Article
    template_name = 'articles/article_detail.html'
    context_object_name = 'article'
    slug_field = 'slug'
    slug_url_kwarg = 'slug'

    def get_queryset(self):
        """Retorna apenas artigos publicados"""
        return Article.objects.published().select_related(
            'author', 'category'
        ).prefetch_related('tags', 'comments__author')

    def get_object(self, queryset=None):
        """Incrementa visualizações do artigo"""
        obj = super().get_object(queryset)

        # Incrementa views (apenas se não for o autor)
        if not (self.request.user.is_authenticated and self.request.user == obj.author):
            obj.view_count += 1
            obj.save(update_fields=['view_count'])
            logger.info(f"Artigo visualizado: {obj.title} (Views: {obj.view_count})")

        return obj

    def get_context_data(self, **kwargs):
        """Adiciona contexto extra"""
        context = super().get_context_data(**kwargs)
        article = self.object

        # Artigos relacionados (mesma categoria, excluindo o atual)
        context['related_articles'] = Article.objects.published().filter(
            category=article.category
        ).exclude(id=article.id).select_related('author')[:4]

        # Comentários aprovados
        context['comments'] = article.comments.approved().select_related('author')
        context['comments_count'] = context['comments'].count()

        # Pode comentar?
        context['can_comment'] = self.request.user.is_authenticated

        # Pode editar?
        context['can_edit'] = (
            self.request.user.is_authenticated and
            (self.request.user == article.author or self.request.user.is_staff)
        )

        return context


class ArticleCreateView(LoginRequiredMixin, CreateView):
    """View para criar artigo"""
    model = Article
    template_name = 'articles/article_form.html'
    fields = ['title', 'content', 'excerpt', 'category', 'tags', 'featured_image', 'status']

    def get_context_data(self, **kwargs):
        """Adiciona contexto extra"""
        context = super().get_context_data(**kwargs)
        context['form_title'] = 'Criar Novo Artigo'
        context['submit_text'] = 'Criar Artigo'
        context['categories'] = Category.objects.filter(is_active=True)
        context['tags'] = Tag.objects.all()
        return context

    def form_valid(self, form):
        """Define o autor como usuário logado"""
        form.instance.author = self.request.user

        # Log da criação
        logger.info(f"Novo artigo criado: {form.instance.title} por {self.request.user.username}")

        messages.success(self.request, f'Artigo "{form.instance.title}" criado com sucesso!')
        return super().form_valid(form)

    def get_success_url(self):
        """Redireciona para o artigo criado"""
        return reverse('articles:detail', kwargs={'slug': self.object.slug})


class ArticleUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """View para editar artigo"""
    model = Article
    template_name = 'articles/article_form.html'
    fields = ['title', 'content', 'excerpt', 'category', 'tags', 'featured_image', 'status']
    slug_field = 'slug'
    slug_url_kwarg = 'slug'

    def test_func(self):
        """Verifica se o usuário pode editar o artigo"""
        article = self.get_object()
        return self.request.user == article.author or self.request.user.is_staff

    def get_context_data(self, **kwargs):
        """Adiciona contexto extra"""
        context = super().get_context_data(**kwargs)
        context['form_title'] = f'Editar: {self.object.title}'
        context['submit_text'] = 'Salvar Alterações'
        context['categories'] = Category.objects.filter(is_active=True)
        context['tags'] = Tag.objects.all()
        context['is_edit'] = True
        return context

    def form_valid(self, form):
        """Mensagem de sucesso"""
        logger.info(f"Artigo editado: {form.instance.title} por {self.request.user.username}")
        messages.success(self.request, f'Artigo "{form.instance.title}" atualizado com sucesso!')
        return super().form_valid(form)

    def get_success_url(self):
        """Redireciona para o artigo editado"""
        return reverse('articles:detail', kwargs={'slug': self.object.slug})


class ArticleDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    """View para deletar artigo"""
    model = Article
    template_name = 'articles/article_confirm_delete.html'
    success_url = reverse_lazy('articles:list')
    slug_field = 'slug'
    slug_url_kwarg = 'slug'

    def test_func(self):
        """Verifica se o usuário pode deletar o artigo"""
        article = self.get_object()
        return self.request.user == article.author or self.request.user.is_staff

    def delete(self, request, *args, **kwargs):
        """Mensagem de sucesso"""
        article = self.get_object()
        logger.info(f"Artigo deletado: {article.title} por {request.user.username}")
        messages.success(request, f'Artigo "{article.title}" deletado com sucesso!')
        return super().delete(request, *args, **kwargs)


class ArticleSearchView(View):
    """View para busca de artigos"""
    template_name = 'articles/search_results.html'

    def get(self, request):
        """Busca artigos"""
        query = request.GET.get('q', '').strip()
        category_slug = request.GET.get('category')
        tag_slug = request.GET.get('tag')

        articles = Article.objects.none()

        if query or category_slug or tag_slug:
            articles = Article.objects.published().select_related('author', 'category')

            if query:
                articles = articles.filter(
                    Q(title__icontains=query) |
                    Q(content__icontains=query) |
                    Q(excerpt__icontains=query) |
                    Q(tags__name__icontains=query)
                ).distinct()

            if category_slug:
                articles = articles.filter(category__slug=category_slug)

            if tag_slug:
                articles = articles.filter(tags__slug=tag_slug)

            articles = articles.order_by('-created_at')

        # Paginação
        paginator = Paginator(articles, 12)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        context = {
            'query': query,
            'articles': page_obj.object_list,
            'page_obj': page_obj,
            'total_results': articles.count(),
            'categories': Category.objects.filter(is_active=True),
            'popular_tags': Tag.objects.annotate(
                article_count=Count('articles', filter=Q(articles__status='published'))
            ).filter(article_count__gt=0).order_by('-article_count')[:10],
        }

        return render(request, self.template_name, context)


class CategoryListView(ListView):
    """View para listar categorias"""
    model = Category
    template_name = 'articles/category_list.html'
    context_object_name = 'categories'

    def get_queryset(self):
        """Retorna categorias ativas com contagem de artigos"""
        return Category.objects.filter(is_active=True).annotate(
            article_count=Count('articles', filter=Q(articles__status='published'))
        ).order_by('name')


class CategoryDetailView(DetailView):
    """View para visualizar artigos de uma categoria"""
    model = Category
    template_name = 'articles/category_detail.html'
    context_object_name = 'category'
    slug_field = 'slug'
    slug_url_kwarg = 'slug'

    def get_queryset(self):
        """Retorna apenas categorias ativas"""
        return Category.objects.filter(is_active=True)

    def get_context_data(self, **kwargs):
        """Adiciona artigos da categoria"""
        context = super().get_context_data(**kwargs)
        category = self.object

        # Artigos da categoria
        articles = Article.objects.published().filter(
            category=category
        ).select_related('author').order_by('-created_at')

        # Paginação
        paginator = Paginator(articles, 12)
        page_number = self.request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        context['articles'] = page_obj.object_list
        context['page_obj'] = page_obj
        context['total_articles'] = articles.count()

        return context


@login_required
def toggle_article_status(request, slug):
    """Toggle do status do artigo (publicado/rascunho)"""
    article = get_object_or_404(Article, slug=slug)

    # Verifica permissão
    if request.user != article.author and not request.user.is_staff:
        messages.error(request, 'Você não tem permissão para alterar este artigo.')
        return redirect('articles:detail', slug=slug)

    # Toggle status
    if article.status == 'published':
        article.status = 'draft'
        message = f'Artigo "{article.title}" movido para rascunho.'
    else:
        article.status = 'published'
        message = f'Artigo "{article.title}" publicado com sucesso!'

    article.save(update_fields=['status'])
    logger.info(f"Status do artigo alterado: {article.title} -> {article.status}")
    messages.success(request, message)

    return redirect('articles:detail', slug=slug)


def article_preview(request, slug):
    """Preview do artigo (para rascunhos)"""
    article = get_object_or_404(Article, slug=slug)

    # Verifica permissão para visualizar rascunho
    if article.status != 'published':
        if not request.user.is_authenticated or (
            request.user != article.author and not request.user.is_staff
        ):
            raise Http404("Artigo não encontrado")

    # Usa o mesmo template do detail, mas com contexto de preview
    context = {
        'article': article,
        'is_preview': True,
        'can_edit': (
            request.user.is_authenticated and
            (request.user == article.author or request.user.is_staff)
        ),
    }

    return render(request, 'articles/article_detail.html', context)
