{% extends "base.html" %}
{% load static %}

{% block extra_css %}
<style>
.article-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.article-image {
    height: 200px;
    object-fit: cover;
    width: 100%;
}

.article-meta {
    font-size: 0.875rem;
    color: #6c757d;
}

.article-excerpt {
    color: #495057;
    line-height: 1.6;
}

.category-badge {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    text-decoration: none;
    display: inline-block;
    margin-bottom: 0.5rem;
}

.category-badge:hover {
    color: white;
    transform: scale(1.05);
}

.tag-badge {
    background-color: #f8f9fa;
    color: #495057;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    text-decoration: none;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
    display: inline-block;
    border: 1px solid #dee2e6;
}

.tag-badge:hover {
    background-color: #007bff;
    color: white;
    text-decoration: none;
}

.search-form {
    background: white;
    border-radius: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 0.5rem;
}

.search-input {
    border: none;
    outline: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.search-btn {
    border-radius: 20px;
    padding: 0.5rem 1.5rem;
}

.sidebar-widget {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.sidebar-widget h5 {
    color: #343a40;
    margin-bottom: 1rem;
    font-weight: 600;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.pagination .page-link {
    border-radius: 10px;
    margin: 0 0.2rem;
    border: none;
    color: #007bff;
}

.pagination .page-item.active .page-link {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.filter-section {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.filter-section h6 {
    color: #495057;
    margin-bottom: 1rem;
    font-weight: 600;
}

.btn-outline-primary {
    border-radius: 20px;
    padding: 0.375rem 1rem;
    margin: 0.2rem;
}

.article-actions {
    position: absolute;
    top: 1rem;
    right: 1rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.article-card:hover .article-actions {
    opacity: 1;
}

.action-btn {
    background: rgba(255,255,255,0.9);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    margin-left: 0.25rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: white;
    transform: scale(1.1);
}

@media (max-width: 768px) {
    .article-card {
        margin-bottom: 1.5rem;
    }
    
    .sidebar-widget {
        margin-bottom: 1rem;
    }
    
    .filter-section {
        padding: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Conteúdo Principal -->
        <div class="col-lg-8">
            {% block articles_content %}
            {% endblock %}
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            {% block sidebar %}
            <!-- Busca -->
            <div class="sidebar-widget">
                <h5><i class="fas fa-search me-2"></i>Buscar Artigos</h5>
                <form method="get" action="{% url 'articles:search' %}" class="search-form">
                    <div class="input-group">
                        <input type="text" 
                               name="q" 
                               class="form-control search-input" 
                               placeholder="Digite sua busca..."
                               value="{{ search_query|default:'' }}">
                        <button class="btn btn-primary search-btn" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Estatísticas -->
            {% if total_articles %}
            <div class="stats-card">
                <div class="stats-number">{{ total_articles }}</div>
                <div>Artigos Publicados</div>
            </div>
            {% endif %}

            <!-- Categorias -->
            {% if categories %}
            <div class="sidebar-widget">
                <h5><i class="fas fa-folder me-2"></i>Categorias</h5>
                {% for category in categories %}
                <a href="{% url 'articles:category_detail' category.slug %}" 
                   class="category-badge">
                    {{ category.name }}
                    {% if category.article_count %}
                        ({{ category.article_count }})
                    {% endif %}
                </a>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Tags Populares -->
            {% if popular_tags %}
            <div class="sidebar-widget">
                <h5><i class="fas fa-tags me-2"></i>Tags Populares</h5>
                {% for tag in popular_tags %}
                <a href="{% url 'articles:search' %}?tag={{ tag.slug }}" 
                   class="tag-badge">
                    {{ tag.name }}
                    {% if tag.article_count %}
                        ({{ tag.article_count }})
                    {% endif %}
                </a>
                {% endfor %}
            </div>
            {% endif %}
            {% endblock %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animação suave para cards
    const cards = document.querySelectorAll('.article-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Busca com Enter
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                this.closest('form').submit();
            }
        });
    }

    // Tooltip para ações
    const actionBtns = document.querySelectorAll('.action-btn');
    actionBtns.forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            const title = this.getAttribute('title');
            if (title) {
                // Implementar tooltip customizado se necessário
            }
        });
    });
});
</script>
{% endblock %}
