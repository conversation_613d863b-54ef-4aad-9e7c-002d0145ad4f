from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist
from unittest.mock import Mock, patch
from apps.accounts.services.auth_service import AuthService
from apps.accounts.services.registration_service import RegistrationService
from apps.accounts.services.password_service import PasswordService
from apps.accounts.services.image_service import ImageService
from apps.accounts.models.verification import VerificationCode
from django.core.files.uploadedfile import SimpleUploadedFile
from PIL import Image as PILImage
import io
import tempfile
import os

User = get_user_model()


class AuthServiceTest(TestCase):
    """Testes para AuthService"""

    def setUp(self):
        """Configuração inicial para os testes"""
        self.mock_user_repository = Mock()
        self.service = AuthService(self.mock_user_repository)

        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123',
            is_verified=True
        )

    def test_authenticate_success(self):
        """Testa autenticação bem-sucedida"""
        with patch('apps.accounts.services.auth_service.authenticate') as mock_auth:
            mock_auth.return_value = self.user

            result = self.service.authenticate_user('<EMAIL>', 'testpass123')

            self.assertEqual(result, self.user)

    def test_authenticate_user_not_found(self):
        """Testa autenticação com usuário não encontrado"""
        with patch('apps.accounts.services.auth_service.authenticate') as mock_auth:
            mock_auth.return_value = None

            result = self.service.authenticate_user('<EMAIL>', 'password')

            self.assertIsNone(result)

    def test_authenticate_wrong_password(self):
        """Testa autenticação com senha incorreta"""
        with patch('apps.accounts.services.auth_service.authenticate') as mock_auth:
            mock_auth.return_value = None

            result = self.service.authenticate_user('<EMAIL>', 'wrongpassword')

            self.assertIsNone(result)

    def test_authenticate_unverified_user(self):
        """Testa autenticação com usuário não verificado"""
        self.user.is_verified = False
        self.user.save()

        with patch('apps.accounts.services.auth_service.authenticate') as mock_auth:
            mock_auth.return_value = self.user

            with self.assertRaises(ValueError):
                self.service.authenticate_user('<EMAIL>', 'testpass123')


class RegistrationServiceTest(TestCase):
    """Testes para RegistrationService"""

    def setUp(self):
        """Configuração inicial para os testes"""
        self.mock_user_repository = Mock()
        self.mock_verification_repository = Mock()
        self.mock_notification_service = Mock()

        self.service = RegistrationService(
            user_repository=self.mock_user_repository,
            verification_repository=self.mock_verification_repository,
            notification_service=self.mock_notification_service
        )

        self.registration_data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'testpass123'
        }

    def test_register_user_success(self):
        """Testa registro bem-sucedido"""
        mock_user = Mock()
        mock_user.email = self.registration_data['email']

        self.mock_user_repository.get_user_by_email.side_effect = ObjectDoesNotExist()
        self.mock_user_repository.create_user.return_value = mock_user
        self.mock_verification_repository.create_verification_code.return_value = '123456'
        self.mock_notification_service.send_registration_confirmation.return_value = True

        result = self.service.register_user(
            email=self.registration_data['email'],
            password=self.registration_data['password'],
            username=self.registration_data['username'],
            first_name=self.registration_data['first_name'],
            last_name=self.registration_data['last_name']
        )

        self.assertEqual(result, mock_user)

        # Verifica se os métodos foram chamados
        self.mock_user_repository.create_user.assert_called_once()
        self.mock_verification_repository.create_verification_code.assert_called_once()
        self.mock_notification_service.send_registration_confirmation.assert_called_once()

    def test_register_user_repository_error(self):
        """Testa erro no repository durante registro"""
        self.mock_user_repository.get_user_by_email.side_effect = ObjectDoesNotExist()
        self.mock_user_repository.create_user.side_effect = ValueError('Email já em uso')

        with self.assertRaises(ValueError):
            self.service.register_user(
                email=self.registration_data['email'],
                password=self.registration_data['password'],
                username=self.registration_data['username']
            )

    def test_verify_email_success(self):
        """Testa verificação de email bem-sucedida"""
        mock_user = Mock()
        mock_user.email = '<EMAIL>'
        mock_user.is_verified = False

        self.mock_user_repository.get_user_by_email.return_value = mock_user
        self.mock_verification_repository.verify_code.return_value = True
        self.mock_user_repository.update_user.return_value = mock_user

        result = self.service.confirm_registration('<EMAIL>', '123456')

        self.assertTrue(result)

        # Verifica se o usuário foi atualizado como verificado
        self.mock_user_repository.update_user.assert_called_once_with(
            mock_user, is_verified=True
        )

    def test_verify_email_invalid_code(self):
        """Testa verificação com código inválido"""
        mock_user = Mock()
        mock_user.is_verified = False

        self.mock_user_repository.get_user_by_email.return_value = mock_user
        self.mock_verification_repository.verify_code.return_value = False

        result = self.service.confirm_registration('<EMAIL>', '999999')

        self.assertFalse(result)


class ImageServiceTest(TestCase):
    """Testes para ImageService"""

    def setUp(self):
        """Configuração inicial para os testes"""
        # Cria uma imagem temporária para testes
        self.temp_dir = tempfile.mkdtemp()
        self.test_image_path = os.path.join(self.temp_dir, 'test_image.jpg')

        # Cria uma imagem de teste
        img = PILImage.new('RGB', (500, 500), color='red')
        img.save(self.test_image_path, 'JPEG')

    def tearDown(self):
        """Limpeza após os testes"""
        if os.path.exists(self.test_image_path):
            os.remove(self.test_image_path)
        os.rmdir(self.temp_dir)

    def test_resize_avatar_success(self):
        """Testa redimensionamento bem-sucedido"""
        result = ImageService.resize_avatar(self.test_image_path, (300, 300))

        self.assertTrue(result)

        # Verifica se a imagem foi redimensionada
        with PILImage.open(self.test_image_path) as img:
            self.assertLessEqual(img.width, 300)
            self.assertLessEqual(img.height, 300)

    def test_resize_avatar_file_not_exists(self):
        """Testa redimensionamento com arquivo inexistente"""
        result = ImageService.resize_avatar('/path/to/nonexistent/file.jpg')

        self.assertFalse(result)

    def test_validate_image_file_valid(self):
        """Testa validação de arquivo válido"""
        # Cria arquivo de upload simulado
        img_buffer = io.BytesIO()
        img = PILImage.new('RGB', (100, 100), color='blue')
        img.save(img_buffer, format='JPEG')
        img_buffer.seek(0)

        uploaded_file = SimpleUploadedFile(
            'test.jpg',
            img_buffer.getvalue(),
            content_type='image/jpeg'
        )

        is_valid, error = ImageService.validate_image_file(uploaded_file)

        self.assertTrue(is_valid)
        self.assertEqual(error, '')

    def test_validate_image_file_too_large(self):
        """Testa validação de arquivo muito grande"""
        # Simula arquivo grande
        large_file = SimpleUploadedFile(
            'large.jpg',
            b'x' * (6 * 1024 * 1024),  # 6MB
            content_type='image/jpeg'
        )

        is_valid, error = ImageService.validate_image_file(large_file)

        self.assertFalse(is_valid)
        self.assertIn('muito grande', error)

    def test_validate_image_file_wrong_type(self):
        """Testa validação de tipo de arquivo incorreto"""
        text_file = SimpleUploadedFile(
            'test.txt',
            b'This is not an image',
            content_type='text/plain'
        )

        is_valid, error = ImageService.validate_image_file(text_file)

        self.assertFalse(is_valid)
        self.assertIn('não permitido', error)