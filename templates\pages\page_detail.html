{% extends "base.html" %}
{% load static %}

{% block title %}{{ page.seo_title|default:page.title }} - {{ block.super }}{% endblock %}

{% block meta_description %}{{ page.seo_description|default:page.excerpt|truncatewords:30 }}{% endblock %}
{% block meta_keywords %}{{ page.meta_keywords }}{% endblock %}

{% block extra_css %}
<style>
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0 2rem;
    margin-bottom: 3rem;
}

.page-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #333;
}

.page-content h1, .page-content h2, .page-content h3 {
    color: #2c3e50;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.page-content h1 { font-size: 2.2rem; }
.page-content h2 { font-size: 1.8rem; }
.page-content h3 { font-size: 1.5rem; }

.page-content p {
    margin-bottom: 1.5rem;
}

.page-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin: 1.5rem 0;
}

.page-content blockquote {
    border-left: 4px solid #007bff;
    padding-left: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0 8px 8px 0;
}

.page-content code {
    background: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    color: #e83e8c;
}

.page-content pre {
    background: #2d3748;
    color: #e2e8f0;
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 1.5rem 0;
}

.page-content pre code {
    background: none;
    color: inherit;
    padding: 0;
}

.breadcrumb-custom {
    background: none;
    padding: 0;
    margin-bottom: 2rem;
}

.breadcrumb-custom .breadcrumb-item {
    color: rgba(255,255,255,0.8);
}

.breadcrumb-custom .breadcrumb-item.active {
    color: white;
}

.breadcrumb-custom .breadcrumb-item a {
    color: rgba(255,255,255,0.9);
    text-decoration: none;
}

.breadcrumb-custom .breadcrumb-item a:hover {
    color: white;
    text-decoration: underline;
}

.page-meta {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    border-left: 4px solid #007bff;
}

.page-meta .meta-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.page-meta .meta-item:last-child {
    margin-bottom: 0;
}

.page-meta .meta-item i {
    width: 20px;
    color: #6c757d;
    margin-right: 0.5rem;
}

.related-pages {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-top: 3rem;
}

.related-page-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
    text-decoration: none;
    color: inherit;
    display: block;
}

.related-page-card:hover {
    background: #e9ecef;
    transform: translateY(-2px);
    text-decoration: none;
    color: inherit;
}

.related-page-card h6 {
    color: #007bff;
    margin-bottom: 0.5rem;
}

.children-pages {
    margin-top: 3rem;
}

.child-page-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
    text-decoration: none;
    color: inherit;
    display: block;
}

.child-page-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 15px rgba(0,123,255,0.1);
    transform: translateY(-2px);
    text-decoration: none;
    color: inherit;
}

.child-page-card h5 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.child-page-card p {
    color: #6c757d;
    margin-bottom: 0;
}

.page-actions {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

.page-actions .btn {
    margin-left: 0.5rem;
    border-radius: 50px;
    padding: 0.75rem 1.5rem;
}

@media (max-width: 768px) {
    .page-header {
        padding: 2rem 0 1rem;
        margin-bottom: 2rem;
    }
    
    .page-content {
        font-size: 1rem;
    }
    
    .page-actions {
        position: static;
        margin-top: 2rem;
        text-align: center;
    }
    
    .page-actions .btn {
        margin: 0.25rem;
        display: inline-block;
    }
}

.preview-banner {
    background: #ffc107;
    color: #212529;
    padding: 0.75rem;
    text-align: center;
    font-weight: bold;
    margin-bottom: 0;
}
</style>
{% endblock %}

{% block content %}
{% if is_preview %}
<div class="preview-banner">
    <i class="fas fa-eye me-2"></i>MODO PREVIEW - Esta página não está publicada
</div>
{% endif %}

<!-- Header da Página -->
<div class="page-header">
    <div class="container">
        <!-- Breadcrumbs -->
        {% if breadcrumbs %}
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb breadcrumb-custom">
                <li class="breadcrumb-item">
                    <a href="{% url 'pages:home' %}">
                        <i class="fas fa-home"></i> Início
                    </a>
                </li>
                {% for crumb in breadcrumbs %}
                    {% if crumb.url %}
                    <li class="breadcrumb-item">
                        <a href="{{ crumb.url }}">{{ crumb.title }}</a>
                    </li>
                    {% else %}
                    <li class="breadcrumb-item active" aria-current="page">
                        {{ crumb.title }}
                    </li>
                    {% endif %}
                {% endfor %}
            </ol>
        </nav>
        {% endif %}

        <!-- Título da Página -->
        <h1 class="display-4 mb-3">{{ page.title }}</h1>
        
        {% if page.excerpt %}
        <p class="lead mb-0">{{ page.excerpt }}</p>
        {% endif %}
    </div>
</div>

<div class="container">
    <div class="row">
        <!-- Conteúdo Principal -->
        <div class="col-lg-8">
            <!-- Meta Informações -->
            <div class="page-meta">
                <div class="meta-item">
                    <i class="fas fa-user"></i>
                    <span>Por {{ page.author.get_full_name|default:page.author.username }}</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-calendar"></i>
                    <span>{{ page.created_at|date:"d/m/Y H:i" }}</span>
                </div>
                {% if page.updated_at != page.created_at %}
                <div class="meta-item">
                    <i class="fas fa-edit"></i>
                    <span>Atualizado em {{ page.updated_at|date:"d/m/Y H:i" }}</span>
                </div>
                {% endif %}
                <div class="meta-item">
                    <i class="fas fa-eye"></i>
                    <span>{{ page.views_count|default:0 }} visualizações</span>
                </div>
                {% if page.is_dynamic %}
                <div class="meta-item">
                    <i class="fas fa-magic"></i>
                    <span>Página Dinâmica</span>
                </div>
                {% endif %}
            </div>

            <!-- Imagem Destacada -->
            {% if page.featured_image %}
            <div class="text-center mb-4">
                <img src="{{ page.featured_image.url }}" alt="{{ page.title }}" class="img-fluid">
            </div>
            {% endif %}

            <!-- Conteúdo da Página -->
            <div class="page-content">
                {% if page.rendered_content %}
                    {{ page.rendered_content|safe }}
                {% else %}
                    {{ page.content|safe }}
                {% endif %}
            </div>

            <!-- Páginas Filhas -->
            {% if children %}
            <div class="children-pages">
                <h3><i class="fas fa-sitemap me-2"></i>Subpáginas</h3>
                <div class="row">
                    {% for child in children %}
                    <div class="col-md-6">
                        <a href="{{ child.get_absolute_url }}" class="child-page-card">
                            <h5>{{ child.title }}</h5>
                            {% if child.excerpt %}
                            <p>{{ child.excerpt|truncatewords:15 }}</p>
                            {% endif %}
                        </a>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Páginas Relacionadas -->
            {% if related_pages %}
            <div class="related-pages">
                <h4><i class="fas fa-link me-2"></i>Páginas Relacionadas</h4>
                {% for related in related_pages %}
                <a href="{{ related.get_absolute_url }}" class="related-page-card">
                    <h6>{{ related.title }}</h6>
                    {% if related.excerpt %}
                    <p class="text-muted small mb-0">{{ related.excerpt|truncatewords:10 }}</p>
                    {% endif %}
                </a>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Navegação Rápida -->
            <div class="related-pages">
                <h4><i class="fas fa-compass me-2"></i>Navegação</h4>
                <a href="{% url 'pages:list' %}" class="related-page-card">
                    <h6><i class="fas fa-list me-2"></i>Todas as Páginas</h6>
                    <p class="text-muted small mb-0">Ver lista completa de páginas</p>
                </a>
                <a href="{% url 'pages:home' %}" class="related-page-card">
                    <h6><i class="fas fa-home me-2"></i>Página Inicial</h6>
                    <p class="text-muted small mb-0">Voltar ao início</p>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Ações da Página (para admins) -->
{% if user.is_staff %}
<div class="page-actions">
    <a href="{% url 'pages:admin_list' %}" class="btn btn-secondary" title="Gerenciar Páginas">
        <i class="fas fa-cog"></i>
    </a>
    <a href="{% url 'pages:edit' page.pk %}" class="btn btn-primary" title="Editar Página">
        <i class="fas fa-edit"></i>
    </a>
    {% if not is_preview %}
    <a href="{% url 'pages:preview' page.pk %}" class="btn btn-info" title="Preview">
        <i class="fas fa-eye"></i>
    </a>
    {% endif %}
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scroll para links internos
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Highlight de código (se Prism.js estiver disponível)
    if (typeof Prism !== 'undefined') {
        Prism.highlightAll();
    }

    // Lazy loading para imagens
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }

    // Animação de entrada para cards
    const cards = document.querySelectorAll('.related-page-card, .child-page-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
