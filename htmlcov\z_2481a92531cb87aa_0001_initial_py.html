<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for apps\pages\migrations\0001_initial.py: 100%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>apps\pages\migrations\0001_initial.py</b>:
            <span class="pc_cov">100%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">8 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">8<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">0<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_73977dc7d293fed5_services_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_2481a92531cb87aa___init___py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 21:05 -0300
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="com"># Generated by Django 5.2.2 on 2025-06-06 14:51</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="key">import</span> <span class="nam">django</span><span class="op">.</span><span class="nam">core</span><span class="op">.</span><span class="nam">validators</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="key">import</span> <span class="nam">django</span><span class="op">.</span><span class="nam">db</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">deletion</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="key">from</span> <span class="nam">django</span><span class="op">.</span><span class="nam">conf</span> <span class="key">import</span> <span class="nam">settings</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="key">from</span> <span class="nam">django</span><span class="op">.</span><span class="nam">db</span> <span class="key">import</span> <span class="nam">migrations</span><span class="op">,</span> <span class="nam">models</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">class</span> <span class="nam">Migration</span><span class="op">(</span><span class="nam">migrations</span><span class="op">.</span><span class="nam">Migration</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t">    <span class="nam">initial</span> <span class="op">=</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t">    <span class="nam">dependencies</span> <span class="op">=</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">swappable_dependency</span><span class="op">(</span><span class="nam">settings</span><span class="op">.</span><span class="nam">AUTH_USER_MODEL</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t">    <span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">    <span class="nam">operations</span> <span class="op">=</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">CreateModel</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t">            <span class="nam">name</span><span class="op">=</span><span class="str">'SEOSettings'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">            <span class="nam">fields</span><span class="op">=</span><span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">                <span class="op">(</span><span class="str">'id'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BigAutoField</span><span class="op">(</span><span class="nam">auto_created</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">primary_key</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">serialize</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'ID'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">                <span class="op">(</span><span class="str">'site_name'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">help_text</span><span class="op">=</span><span class="str">'Nome do site para SEO'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">100</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'nome do site'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">                <span class="op">(</span><span class="str">'site_description'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">TextField</span><span class="op">(</span><span class="nam">help_text</span><span class="op">=</span><span class="str">'Descri&#231;&#227;o geral do site (m&#225;ximo 160 caracteres)'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">160</span><span class="op">,</span> <span class="nam">validators</span><span class="op">=</span><span class="op">[</span><span class="nam">django</span><span class="op">.</span><span class="nam">core</span><span class="op">.</span><span class="nam">validators</span><span class="op">.</span><span class="nam">MaxLengthValidator</span><span class="op">(</span><span class="num">160</span><span class="op">)</span><span class="op">]</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'descri&#231;&#227;o do site'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">                <span class="op">(</span><span class="str">'site_keywords'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">TextField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Palavras-chave gerais separadas por v&#237;rgula'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">255</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'palavras-chave do site'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">                <span class="op">(</span><span class="str">'default_og_image'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">ImageField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Imagem padr&#227;o para compartilhamento em redes sociais'</span><span class="op">,</span> <span class="nam">upload_to</span><span class="op">=</span><span class="str">'seo/og_images/'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'imagem padr&#227;o Open Graph'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">                <span class="op">(</span><span class="str">'favicon'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">ImageField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'&#205;cone do site (favicon)'</span><span class="op">,</span> <span class="nam">upload_to</span><span class="op">=</span><span class="str">'seo/favicons/'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'favicon'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">                <span class="op">(</span><span class="str">'google_analytics_id'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'ID do Google Analytics (ex: GA_MEASUREMENT_ID)'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">20</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'Google Analytics ID'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">                <span class="op">(</span><span class="str">'google_tag_manager_id'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'ID do Google Tag Manager (ex: GTM-XXXXXXX)'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">20</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'Google Tag Manager ID'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">                <span class="op">(</span><span class="str">'facebook_pixel_id'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'ID do Facebook Pixel'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">20</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'Facebook Pixel ID'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">                <span class="op">(</span><span class="str">'facebook_url'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">URLField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'URL da p&#225;gina do Facebook'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'URL do Facebook'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">                <span class="op">(</span><span class="str">'twitter_url'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">URLField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'URL do perfil do Twitter'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'URL do Twitter'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">                <span class="op">(</span><span class="str">'instagram_url'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">URLField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'URL do perfil do Instagram'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'URL do Instagram'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">                <span class="op">(</span><span class="str">'linkedin_url'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">URLField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'URL do perfil do LinkedIn'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'URL do LinkedIn'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">                <span class="op">(</span><span class="str">'youtube_url'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">URLField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'URL do canal do YouTube'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'URL do YouTube'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">                <span class="op">(</span><span class="str">'contact_email'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">EmailField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Email principal de contato'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">254</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'email de contato'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">                <span class="op">(</span><span class="str">'contact_phone'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Telefone principal de contato'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">20</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'telefone de contato'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">                <span class="op">(</span><span class="str">'address'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">TextField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Endere&#231;o f&#237;sico da empresa'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'endere&#231;o'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">                <span class="op">(</span><span class="str">'organization_type'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">'Organization'</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Tipo de organiza&#231;&#227;o para Schema.org'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">50</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'tipo de organiza&#231;&#227;o'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">                <span class="op">(</span><span class="str">'robots_txt'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">TextField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Conte&#250;do do arquivo robots.txt'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'robots.txt'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">                <span class="op">(</span><span class="str">'enable_sitemap'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BooleanField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Se deve gerar sitemap automaticamente'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'habilitar sitemap'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">                <span class="op">(</span><span class="str">'created_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">auto_now_add</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'criado em'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">                <span class="op">(</span><span class="str">'updated_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">auto_now</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'atualizado em'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">            <span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">            <span class="nam">options</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">                <span class="str">'verbose_name'</span><span class="op">:</span> <span class="str">'configura&#231;&#227;o de SEO'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">                <span class="str">'verbose_name_plural'</span><span class="op">:</span> <span class="str">'configura&#231;&#245;es de SEO'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">            <span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">CreateModel</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">            <span class="nam">name</span><span class="op">=</span><span class="str">'Page'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">            <span class="nam">fields</span><span class="op">=</span><span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">                <span class="op">(</span><span class="str">'id'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BigAutoField</span><span class="op">(</span><span class="nam">auto_created</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">primary_key</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">serialize</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'ID'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">                <span class="op">(</span><span class="str">'title'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">help_text</span><span class="op">=</span><span class="str">'T&#237;tulo da p&#225;gina'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">200</span><span class="op">,</span> <span class="nam">validators</span><span class="op">=</span><span class="op">[</span><span class="nam">django</span><span class="op">.</span><span class="nam">core</span><span class="op">.</span><span class="nam">validators</span><span class="op">.</span><span class="nam">MinLengthValidator</span><span class="op">(</span><span class="num">3</span><span class="op">)</span><span class="op">]</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'t&#237;tulo'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">                <span class="op">(</span><span class="str">'slug'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">SlugField</span><span class="op">(</span><span class="nam">help_text</span><span class="op">=</span><span class="str">'URL amig&#225;vel da p&#225;gina'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">200</span><span class="op">,</span> <span class="nam">unique</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'slug'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">                <span class="op">(</span><span class="str">'content'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">TextField</span><span class="op">(</span><span class="nam">help_text</span><span class="op">=</span><span class="str">'Conte&#250;do da p&#225;gina em HTML ou Markdown'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'conte&#250;do'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">                <span class="op">(</span><span class="str">'excerpt'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">TextField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Resumo da p&#225;gina para SEO e listagens'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">500</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'resumo'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">                <span class="op">(</span><span class="str">'status'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">choices</span><span class="op">=</span><span class="op">[</span><span class="op">(</span><span class="str">'draft'</span><span class="op">,</span> <span class="str">'Rascunho'</span><span class="op">)</span><span class="op">,</span> <span class="op">(</span><span class="str">'published'</span><span class="op">,</span> <span class="str">'Publicado'</span><span class="op">)</span><span class="op">,</span> <span class="op">(</span><span class="str">'archived'</span><span class="op">,</span> <span class="str">'Arquivado'</span><span class="op">)</span><span class="op">]</span><span class="op">,</span> <span class="nam">default</span><span class="op">=</span><span class="str">'draft'</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Status de publica&#231;&#227;o da p&#225;gina'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">20</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'status'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">                <span class="op">(</span><span class="str">'template'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">choices</span><span class="op">=</span><span class="op">[</span><span class="op">(</span><span class="str">'pages/default.html'</span><span class="op">,</span> <span class="str">'Padr&#227;o'</span><span class="op">)</span><span class="op">,</span> <span class="op">(</span><span class="str">'pages/landing.html'</span><span class="op">,</span> <span class="str">'Landing Page'</span><span class="op">)</span><span class="op">,</span> <span class="op">(</span><span class="str">'pages/full_width.html'</span><span class="op">,</span> <span class="str">'Largura Total'</span><span class="op">)</span><span class="op">,</span> <span class="op">(</span><span class="str">'pages/sidebar.html'</span><span class="op">,</span> <span class="str">'Com Sidebar'</span><span class="op">)</span><span class="op">]</span><span class="op">,</span> <span class="nam">default</span><span class="op">=</span><span class="str">'pages/default.html'</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Template a ser usado para renderizar a p&#225;gina'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">100</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'template'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">                <span class="op">(</span><span class="str">'is_homepage'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BooleanField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Define se esta &#233; a p&#225;gina inicial do site'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'&#233; p&#225;gina inicial'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">                <span class="op">(</span><span class="str">'show_in_menu'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BooleanField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Se a p&#225;gina deve aparecer no menu de navega&#231;&#227;o'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'mostrar no menu'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">                <span class="op">(</span><span class="str">'menu_order'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">PositiveIntegerField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Ordem de exibi&#231;&#227;o no menu (menor n&#250;mero = primeiro)'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'ordem no menu'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">                <span class="op">(</span><span class="str">'meta_title'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'T&#237;tulo para SEO (m&#225;ximo 60 caracteres)'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">60</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'meta t&#237;tulo'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">                <span class="op">(</span><span class="str">'meta_description'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Descri&#231;&#227;o para SEO (m&#225;ximo 160 caracteres)'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">160</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'meta descri&#231;&#227;o'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">                <span class="op">(</span><span class="str">'meta_keywords'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Palavras-chave separadas por v&#237;rgula'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">255</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'palavras-chave'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">                <span class="op">(</span><span class="str">'created_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">auto_now_add</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'criado em'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">                <span class="op">(</span><span class="str">'updated_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">auto_now</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'atualizado em'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">                <span class="op">(</span><span class="str">'published_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Data e hora de publica&#231;&#227;o'</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'publicado em'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">                <span class="op">(</span><span class="str">'view_count'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">PositiveIntegerField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'N&#250;mero de visualiza&#231;&#245;es da p&#225;gina'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'visualiza&#231;&#245;es'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">                <span class="op">(</span><span class="str">'created_by'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">ForeignKey</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">on_delete</span><span class="op">=</span><span class="nam">django</span><span class="op">.</span><span class="nam">db</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">deletion</span><span class="op">.</span><span class="nam">SET_NULL</span><span class="op">,</span> <span class="nam">related_name</span><span class="op">=</span><span class="str">'created_pages'</span><span class="op">,</span> <span class="nam">to</span><span class="op">=</span><span class="nam">settings</span><span class="op">.</span><span class="nam">AUTH_USER_MODEL</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'criado por'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">                <span class="op">(</span><span class="str">'parent'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">ForeignKey</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'P&#225;gina pai para criar hierarquia'</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">on_delete</span><span class="op">=</span><span class="nam">django</span><span class="op">.</span><span class="nam">db</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">deletion</span><span class="op">.</span><span class="nam">CASCADE</span><span class="op">,</span> <span class="nam">to</span><span class="op">=</span><span class="str">'pages.page'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'p&#225;gina pai'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">                <span class="op">(</span><span class="str">'updated_by'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">ForeignKey</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">on_delete</span><span class="op">=</span><span class="nam">django</span><span class="op">.</span><span class="nam">db</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">deletion</span><span class="op">.</span><span class="nam">SET_NULL</span><span class="op">,</span> <span class="nam">related_name</span><span class="op">=</span><span class="str">'updated_pages'</span><span class="op">,</span> <span class="nam">to</span><span class="op">=</span><span class="nam">settings</span><span class="op">.</span><span class="nam">AUTH_USER_MODEL</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'atualizado por'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">            <span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">            <span class="nam">options</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">                <span class="str">'verbose_name'</span><span class="op">:</span> <span class="str">'p&#225;gina'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">                <span class="str">'verbose_name_plural'</span><span class="op">:</span> <span class="str">'p&#225;ginas'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">                <span class="str">'ordering'</span><span class="op">:</span> <span class="op">[</span><span class="str">'menu_order'</span><span class="op">,</span> <span class="str">'title'</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">            <span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">CreateModel</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">            <span class="nam">name</span><span class="op">=</span><span class="str">'NavigationItem'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">            <span class="nam">fields</span><span class="op">=</span><span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">                <span class="op">(</span><span class="str">'id'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BigAutoField</span><span class="op">(</span><span class="nam">auto_created</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">primary_key</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">serialize</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'ID'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">                <span class="op">(</span><span class="str">'title'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">help_text</span><span class="op">=</span><span class="str">'Texto exibido no menu'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">100</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'t&#237;tulo'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">                <span class="op">(</span><span class="str">'url'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'URL personalizada (para links externos)'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">255</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'URL'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">                <span class="op">(</span><span class="str">'nav_type'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">choices</span><span class="op">=</span><span class="op">[</span><span class="op">(</span><span class="str">'page'</span><span class="op">,</span> <span class="str">'P&#225;gina'</span><span class="op">)</span><span class="op">,</span> <span class="op">(</span><span class="str">'url'</span><span class="op">,</span> <span class="str">'URL Externa'</span><span class="op">)</span><span class="op">,</span> <span class="op">(</span><span class="str">'category'</span><span class="op">,</span> <span class="str">'Categoria'</span><span class="op">)</span><span class="op">]</span><span class="op">,</span> <span class="nam">default</span><span class="op">=</span><span class="str">'page'</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Tipo de item de navega&#231;&#227;o'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">20</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'tipo'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">                <span class="op">(</span><span class="str">'order'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">PositiveIntegerField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Ordem de exibi&#231;&#227;o no menu'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'ordem'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">                <span class="op">(</span><span class="str">'is_active'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BooleanField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Se o item deve ser exibido no menu'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'ativo'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">                <span class="op">(</span><span class="str">'open_in_new_tab'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BooleanField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Se o link deve abrir em nova aba'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'abrir em nova aba'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">                <span class="op">(</span><span class="str">'css_class'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Classes CSS personalizadas para o item'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">100</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'classe CSS'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">                <span class="op">(</span><span class="str">'icon'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Classe do &#237;cone (ex: fas fa-home)'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">50</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'&#237;cone'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">                <span class="op">(</span><span class="str">'created_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">auto_now_add</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'criado em'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">                <span class="op">(</span><span class="str">'updated_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">auto_now</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'atualizado em'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">                <span class="op">(</span><span class="str">'parent'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">ForeignKey</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Item pai para criar submenus'</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">on_delete</span><span class="op">=</span><span class="nam">django</span><span class="op">.</span><span class="nam">db</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">deletion</span><span class="op">.</span><span class="nam">CASCADE</span><span class="op">,</span> <span class="nam">to</span><span class="op">=</span><span class="str">'pages.navigationitem'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'item pai'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">                <span class="op">(</span><span class="str">'page'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">ForeignKey</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'P&#225;gina interna do site'</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">on_delete</span><span class="op">=</span><span class="nam">django</span><span class="op">.</span><span class="nam">db</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">deletion</span><span class="op">.</span><span class="nam">CASCADE</span><span class="op">,</span> <span class="nam">to</span><span class="op">=</span><span class="str">'pages.page'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'p&#225;gina'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">            <span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">            <span class="nam">options</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">                <span class="str">'verbose_name'</span><span class="op">:</span> <span class="str">'item de navega&#231;&#227;o'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">                <span class="str">'verbose_name_plural'</span><span class="op">:</span> <span class="str">'itens de navega&#231;&#227;o'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">                <span class="str">'ordering'</span><span class="op">:</span> <span class="op">[</span><span class="str">'order'</span><span class="op">,</span> <span class="str">'title'</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">            <span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">AddIndex</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">            <span class="nam">model_name</span><span class="op">=</span><span class="str">'page'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">            <span class="nam">index</span><span class="op">=</span><span class="nam">models</span><span class="op">.</span><span class="nam">Index</span><span class="op">(</span><span class="nam">fields</span><span class="op">=</span><span class="op">[</span><span class="str">'slug'</span><span class="op">]</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'pages_page_slug_3e99a9_idx'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">AddIndex</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">            <span class="nam">model_name</span><span class="op">=</span><span class="str">'page'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">            <span class="nam">index</span><span class="op">=</span><span class="nam">models</span><span class="op">.</span><span class="nam">Index</span><span class="op">(</span><span class="nam">fields</span><span class="op">=</span><span class="op">[</span><span class="str">'status'</span><span class="op">,</span> <span class="str">'published_at'</span><span class="op">]</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'pages_page_status_de97e2_idx'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">AddIndex</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">            <span class="nam">model_name</span><span class="op">=</span><span class="str">'page'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">            <span class="nam">index</span><span class="op">=</span><span class="nam">models</span><span class="op">.</span><span class="nam">Index</span><span class="op">(</span><span class="nam">fields</span><span class="op">=</span><span class="op">[</span><span class="str">'show_in_menu'</span><span class="op">,</span> <span class="str">'menu_order'</span><span class="op">]</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'pages_page_show_in_ad7186_idx'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">    <span class="op">]</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_73977dc7d293fed5_services_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_2481a92531cb87aa___init___py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 21:05 -0300
        </p>
    </div>
</footer>
</body>
</html>
