{% extends 'base.html' %}

{% block title %}Deletar Usuário - {{ block.super }}{% endblock %}

{% block content %}
<div class="container my-5">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h2 text-danger">
                <i class="fas fa-trash me-2"></i>Deletar Usuário
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'config:dashboard' %}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'config:user_list' %}">Usuários</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'config:user_detail' user_detail.id %}">{{ user_detail.email }}</a></li>
                    <li class="breadcrumb-item active">Deletar</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{% url 'config:user_detail' user_detail.id %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Voltar
            </a>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Aviso de Confirmação -->
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>Confirmação de Exclusão
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h5 class="alert-heading">
                            <i class="fas fa-warning me-2"></i>Atenção!
                        </h5>
                        <p class="mb-0">
                            Você está prestes a <strong>deletar permanentemente</strong> o usuário abaixo. 
                            Esta ação <strong>não pode ser desfeita</strong>.
                        </p>
                    </div>

                    <!-- Informações do Usuário -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">Usuário a ser deletado:</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">E-mail:</label>
                                        <p class="form-control-plaintext">{{ user_detail.email }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Nome:</label>
                                        <p class="form-control-plaintext">{{ user_detail.get_full_name|default:"Não informado" }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Nome de usuário:</label>
                                        <p class="form-control-plaintext">{{ user_detail.username }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Status:</label>
                                        <div>
                                            {% if user_detail.is_active %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>Ativo
                                                </span>
                                            {% else %}
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times me-1"></i>Inativo
                                                </span>
                                            {% endif %}
                                            
                                            {% if user_detail.is_verified %}
                                                <span class="badge bg-info">
                                                    <i class="fas fa-check-circle me-1"></i>Verificado
                                                </span>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Tipo:</label>
                                        <div>
                                            {% if user_detail.is_superuser %}
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-crown me-1"></i>Superusuário
                                                </span>
                                            {% elif user_detail.is_staff %}
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-user-tie me-1"></i>Staff
                                                </span>
                                            {% else %}
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-user me-1"></i>Usuário comum
                                                </span>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Criado em:</label>
                                        <p class="form-control-plaintext">{{ user_detail.date_joined|date:"d/m/Y H:i" }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Consequências da Exclusão -->
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>Consequências da exclusão:
                        </h6>
                        <ul class="mb-0">
                            <li>O usuário será <strong>permanentemente removido</strong> do sistema</li>
                            <li>Todos os dados associados ao usuário serão <strong>perdidos</strong></li>
                            <li>O usuário <strong>não poderá mais fazer login</strong></li>
                            <li>Esta ação <strong>não pode ser desfeita</strong></li>
                            {% if user_detail.is_superuser %}
                                <li class="text-danger"><strong>ATENÇÃO:</strong> Este é um superusuário com acesso total ao sistema</li>
                            {% endif %}
                        </ul>
                    </div>

                    <!-- Formulário de Confirmação -->
                    <form method="post" id="deleteForm">
                        {% csrf_token %}
                        
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                                <label class="form-check-label fw-bold text-danger" for="confirmDelete">
                                    Eu entendo que esta ação é irreversível e confirmo a exclusão
                                </label>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <div>
                                <a href="{% url 'config:user_detail' user_detail.id %}" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>Cancelar
                                </a>
                                <a href="{% url 'config:user_list' %}" class="btn btn-outline-secondary ms-2">
                                    <i class="fas fa-list me-1"></i>Lista de Usuários
                                </a>
                            </div>
                            <div>
                                <button type="submit" class="btn btn-danger" id="deleteButton" disabled>
                                    <i class="fas fa-trash me-1"></i>Deletar Usuário Permanentemente
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmCheckbox = document.getElementById('confirmDelete');
    const deleteButton = document.getElementById('deleteButton');
    const deleteForm = document.getElementById('deleteForm');
    
    // Habilita/desabilita o botão baseado no checkbox
    confirmCheckbox.addEventListener('change', function() {
        deleteButton.disabled = !this.checked;
        
        if (this.checked) {
            deleteButton.classList.remove('btn-danger');
            deleteButton.classList.add('btn-outline-danger');
        } else {
            deleteButton.classList.remove('btn-outline-danger');
            deleteButton.classList.add('btn-danger');
        }
    });
    
    // Confirmação adicional no submit
    deleteForm.addEventListener('submit', function(e) {
        if (!confirmCheckbox.checked) {
            e.preventDefault();
            alert('Você deve confirmar a exclusão marcando a caixa de seleção.');
            return;
        }
        
        const userEmail = '{{ user_detail.email|escapejs }}';
        const confirmMessage = `Tem certeza absoluta que deseja deletar o usuário "${userEmail}"?\n\nEsta ação NÃO PODE ser desfeita!`;
        
        if (!confirm(confirmMessage)) {
            e.preventDefault();
        }
    });
    
    // Adiciona efeito visual de perigo
    deleteButton.addEventListener('mouseenter', function() {
        if (!this.disabled) {
            this.classList.add('btn-danger');
            this.classList.remove('btn-outline-danger');
        }
    });
    
    deleteButton.addEventListener('mouseleave', function() {
        if (!this.disabled && confirmCheckbox.checked) {
            this.classList.remove('btn-danger');
            this.classList.add('btn-outline-danger');
        }
    });
});
</script>
{% endblock %}
