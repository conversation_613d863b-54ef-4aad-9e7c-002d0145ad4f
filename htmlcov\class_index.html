<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">41%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 21:05 -0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1___init___py.html">apps\accounts\__init__.py</a></td>
                <td class="name left"><a href="z_0daf9d4b3925bce1___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_admin_py.html#t5">apps\accounts\admin.py</a></td>
                <td class="name left"><a href="z_0daf9d4b3925bce1_admin_py.html#t5"><data value='CustomUserAdmin'>CustomUserAdmin</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_admin_py.html#t23">apps\accounts\admin.py</a></td>
                <td class="name left"><a href="z_0daf9d4b3925bce1_admin_py.html#t23"><data value='VerificationCodeAdmin'>VerificationCodeAdmin</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_admin_py.html">apps\accounts\admin.py</a></td>
                <td class="name left"><a href="z_0daf9d4b3925bce1_admin_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_apps_py.html#t3">apps\accounts\apps.py</a></td>
                <td class="name left"><a href="z_0daf9d4b3925bce1_apps_py.html#t3"><data value='AccountsConfig'>AccountsConfig</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_apps_py.html">apps\accounts\apps.py</a></td>
                <td class="name left"><a href="z_0daf9d4b3925bce1_apps_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_96c8905fe34cb2c1___init___py.html">apps\accounts\factories\__init__.py</a></td>
                <td class="name left"><a href="z_96c8905fe34cb2c1___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_96c8905fe34cb2c1_service_factory_py.html#t14">apps\accounts\factories\service_factory.py</a></td>
                <td class="name left"><a href="z_96c8905fe34cb2c1_service_factory_py.html#t14"><data value='AccountServiceFactory'>AccountServiceFactory</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_96c8905fe34cb2c1_service_factory_py.html">apps\accounts\factories\service_factory.py</a></td>
                <td class="name left"><a href="z_96c8905fe34cb2c1_service_factory_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e___init___py.html">apps\accounts\forms\__init__.py</a></td>
                <td class="name left"><a href="z_9f13b1157adba62e___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_authentication_py.html#t13">apps\accounts\forms\authentication.py</a></td>
                <td class="name left"><a href="z_9f13b1157adba62e_authentication_py.html#t13"><data value='FlexibleLoginForm'>FlexibleLoginForm</data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_authentication_py.html#t180">apps\accounts\forms\authentication.py</a></td>
                <td class="name left"><a href="z_9f13b1157adba62e_authentication_py.html#t180"><data value='QuickLoginForm'>QuickLoginForm</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_authentication_py.html">apps\accounts\forms\authentication.py</a></td>
                <td class="name left"><a href="z_9f13b1157adba62e_authentication_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_password_reset_py.html">apps\accounts\forms\password_reset.py</a></td>
                <td class="name left"><a href="z_9f13b1157adba62e_password_reset_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_profile_forms_py.html#t12">apps\accounts\forms\profile_forms.py</a></td>
                <td class="name left"><a href="z_9f13b1157adba62e_profile_forms_py.html#t12"><data value='ProfileUpdateForm'>ProfileUpdateForm</data></a></td>
                <td>67</td>
                <td>67</td>
                <td>0</td>
                <td class="right" data-ratio="0 67">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_profile_forms_py.html#t15">apps\accounts\forms\profile_forms.py</a></td>
                <td class="name left"><a href="z_9f13b1157adba62e_profile_forms_py.html#t15"><data value='Meta'>ProfileUpdateForm.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_profile_forms_py.html#t206">apps\accounts\forms\profile_forms.py</a></td>
                <td class="name left"><a href="z_9f13b1157adba62e_profile_forms_py.html#t206"><data value='AvatarUpdateForm'>AvatarUpdateForm</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_profile_forms_py.html#t209">apps\accounts\forms\profile_forms.py</a></td>
                <td class="name left"><a href="z_9f13b1157adba62e_profile_forms_py.html#t209"><data value='Meta'>AvatarUpdateForm.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_profile_forms_py.html#t270">apps\accounts\forms\profile_forms.py</a></td>
                <td class="name left"><a href="z_9f13b1157adba62e_profile_forms_py.html#t270"><data value='EmailUpdateForm'>EmailUpdateForm</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_profile_forms_py.html#t283">apps\accounts\forms\profile_forms.py</a></td>
                <td class="name left"><a href="z_9f13b1157adba62e_profile_forms_py.html#t283"><data value='Meta'>EmailUpdateForm.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_profile_forms_py.html#t362">apps\accounts\forms\profile_forms.py</a></td>
                <td class="name left"><a href="z_9f13b1157adba62e_profile_forms_py.html#t362"><data value='PasswordChangeForm'>PasswordChangeForm</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_profile_forms_py.html">apps\accounts\forms\profile_forms.py</a></td>
                <td class="name left"><a href="z_9f13b1157adba62e_profile_forms_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="45 45">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_registration_py.html#t11">apps\accounts\forms\registration.py</a></td>
                <td class="name left"><a href="z_9f13b1157adba62e_registration_py.html#t11"><data value='RegistrationForm'>RegistrationForm</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_registration_py.html#t90">apps\accounts\forms\registration.py</a></td>
                <td class="name left"><a href="z_9f13b1157adba62e_registration_py.html#t90"><data value='Meta'>RegistrationForm.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_registration_py.html#t111">apps\accounts\forms\registration.py</a></td>
                <td class="name left"><a href="z_9f13b1157adba62e_registration_py.html#t111"><data value='VerificationForm'>VerificationForm</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_registration_py.html">apps\accounts\forms\registration.py</a></td>
                <td class="name left"><a href="z_9f13b1157adba62e_registration_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ee7bd68557b5fff3___init___py.html">apps\accounts\handlers\__init__.py</a></td>
                <td class="name left"><a href="z_ee7bd68557b5fff3___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ee7bd68557b5fff3_user_handlers_py.html">apps\accounts\handlers\user_handlers.py</a></td>
                <td class="name left"><a href="z_ee7bd68557b5fff3_user_handlers_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="17 25">68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7190ae31b87f647e___init___py.html">apps\accounts\interfaces\__init__.py</a></td>
                <td class="name left"><a href="z_7190ae31b87f647e___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7190ae31b87f647e_notifications_py.html#t3">apps\accounts\interfaces\notifications.py</a></td>
                <td class="name left"><a href="z_7190ae31b87f647e_notifications_py.html#t3"><data value='INotificationService'>INotificationService</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7190ae31b87f647e_notifications_py.html">apps\accounts\interfaces\notifications.py</a></td>
                <td class="name left"><a href="z_7190ae31b87f647e_notifications_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7190ae31b87f647e_repositories_py.html#t7">apps\accounts\interfaces\repositories.py</a></td>
                <td class="name left"><a href="z_7190ae31b87f647e_repositories_py.html#t7"><data value='IUserRepository'>IUserRepository</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7190ae31b87f647e_repositories_py.html#t51">apps\accounts\interfaces\repositories.py</a></td>
                <td class="name left"><a href="z_7190ae31b87f647e_repositories_py.html#t51"><data value='IVerificationRepository'>IVerificationRepository</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7190ae31b87f647e_repositories_py.html">apps\accounts\interfaces\repositories.py</a></td>
                <td class="name left"><a href="z_7190ae31b87f647e_repositories_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7190ae31b87f647e_services_py.html#t7">apps\accounts\interfaces\services.py</a></td>
                <td class="name left"><a href="z_7190ae31b87f647e_services_py.html#t7"><data value='IRegistrationService'>IRegistrationService</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7190ae31b87f647e_services_py.html#t33">apps\accounts\interfaces\services.py</a></td>
                <td class="name left"><a href="z_7190ae31b87f647e_services_py.html#t33"><data value='IPasswordService'>IPasswordService</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7190ae31b87f647e_services_py.html#t57">apps\accounts\interfaces\services.py</a></td>
                <td class="name left"><a href="z_7190ae31b87f647e_services_py.html#t57"><data value='IAuthService'>IAuthService</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7190ae31b87f647e_services_py.html">apps\accounts\interfaces\services.py</a></td>
                <td class="name left"><a href="z_7190ae31b87f647e_services_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e66b20f0cc2b9be7___init___py.html">apps\accounts\management\__init__.py</a></td>
                <td class="name left"><a href="z_e66b20f0cc2b9be7___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7a12aab24e24085f___init___py.html">apps\accounts\management\commands\__init__.py</a></td>
                <td class="name left"><a href="z_7a12aab24e24085f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7a12aab24e24085f_create_test_users_py.html">apps\accounts\management\commands\create_test_users.py</a></td>
                <td class="name left"><a href="z_7a12aab24e24085f_create_test_users_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7a12aab24e24085f_test_email_py.html#t10">apps\accounts\management\commands\test_email.py</a></td>
                <td class="name left"><a href="z_7a12aab24e24085f_test_email_py.html#t10"><data value='Command'>Command</data></a></td>
                <td>74</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="0 74">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7a12aab24e24085f_test_email_py.html">apps\accounts\management\commands\test_email.py</a></td>
                <td class="name left"><a href="z_7a12aab24e24085f_test_email_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_middleware_py.html#t14">apps\accounts\middleware.py</a></td>
                <td class="name left"><a href="z_0daf9d4b3925bce1_middleware_py.html#t14"><data value='AccessControlMiddleware'>AccessControlMiddleware</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_middleware_py.html#t167">apps\accounts\middleware.py</a></td>
                <td class="name left"><a href="z_0daf9d4b3925bce1_middleware_py.html#t167"><data value='LoginRedirectMiddleware'>LoginRedirectMiddleware</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_middleware_py.html#t311">apps\accounts\middleware.py</a></td>
                <td class="name left"><a href="z_0daf9d4b3925bce1_middleware_py.html#t311"><data value='SmartRedirectMiddleware'>SmartRedirectMiddleware</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_middleware_py.html">apps\accounts\middleware.py</a></td>
                <td class="name left"><a href="z_0daf9d4b3925bce1_middleware_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="34 43">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e835377efbddd70_0001_initial_py.html#t10">apps\accounts\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_8e835377efbddd70_0001_initial_py.html#t10"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e835377efbddd70_0001_initial_py.html">apps\accounts\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_8e835377efbddd70_0001_initial_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e835377efbddd70_0002_user_avatar_user_bio_user_birth_date_user_location_and_more_py.html#t7">apps\accounts\migrations\0002_user_avatar_user_bio_user_birth_date_user_location_and_more.py</a></td>
                <td class="name left"><a href="z_8e835377efbddd70_0002_user_avatar_user_bio_user_birth_date_user_location_and_more_py.html#t7"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e835377efbddd70_0002_user_avatar_user_bio_user_birth_date_user_location_and_more_py.html">apps\accounts\migrations\0002_user_avatar_user_bio_user_birth_date_user_location_and_more.py</a></td>
                <td class="name left"><a href="z_8e835377efbddd70_0002_user_avatar_user_bio_user_birth_date_user_location_and_more_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e835377efbddd70___init___py.html">apps\accounts\migrations\__init__.py</a></td>
                <td class="name left"><a href="z_8e835377efbddd70___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_880a307ae6abc71f___init___py.html">apps\accounts\mixins\__init__.py</a></td>
                <td class="name left"><a href="z_880a307ae6abc71f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_880a307ae6abc71f_service_mixins_py.html#t9">apps\accounts\mixins\service_mixins.py</a></td>
                <td class="name left"><a href="z_880a307ae6abc71f_service_mixins_py.html#t9"><data value='AuthServiceMixin'>AuthServiceMixin</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_880a307ae6abc71f_service_mixins_py.html#t22">apps\accounts\mixins\service_mixins.py</a></td>
                <td class="name left"><a href="z_880a307ae6abc71f_service_mixins_py.html#t22"><data value='RegistrationServiceMixin'>RegistrationServiceMixin</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_880a307ae6abc71f_service_mixins_py.html#t35">apps\accounts\mixins\service_mixins.py</a></td>
                <td class="name left"><a href="z_880a307ae6abc71f_service_mixins_py.html#t35"><data value='PasswordServiceMixin'>PasswordServiceMixin</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_880a307ae6abc71f_service_mixins_py.html#t48">apps\accounts\mixins\service_mixins.py</a></td>
                <td class="name left"><a href="z_880a307ae6abc71f_service_mixins_py.html#t48"><data value='AccountServicesMixin'>AccountServicesMixin</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_880a307ae6abc71f_service_mixins_py.html">apps\accounts\mixins\service_mixins.py</a></td>
                <td class="name left"><a href="z_880a307ae6abc71f_service_mixins_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_424a1052f51881f8___init___py.html">apps\accounts\models\__init__.py</a></td>
                <td class="name left"><a href="z_424a1052f51881f8___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_424a1052f51881f8_user_py.html#t6">apps\accounts\models\user.py</a></td>
                <td class="name left"><a href="z_424a1052f51881f8_user_py.html#t6"><data value='UserManager'>UserManager</data></a></td>
                <td>15</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="12 15">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_424a1052f51881f8_user_py.html#t40">apps\accounts\models\user.py</a></td>
                <td class="name left"><a href="z_424a1052f51881f8_user_py.html#t40"><data value='User'>User</data></a></td>
                <td>23</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="20 23">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_424a1052f51881f8_user_py.html#t99">apps\accounts\models\user.py</a></td>
                <td class="name left"><a href="z_424a1052f51881f8_user_py.html#t99"><data value='Meta'>User.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_424a1052f51881f8_user_py.html">apps\accounts\models\user.py</a></td>
                <td class="name left"><a href="z_424a1052f51881f8_user_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="32 35">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_424a1052f51881f8_verification_py.html#t8">apps\accounts\models\verification.py</a></td>
                <td class="name left"><a href="z_424a1052f51881f8_verification_py.html#t8"><data value='VerificationCode'>VerificationCode</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_424a1052f51881f8_verification_py.html#t47">apps\accounts\models\verification.py</a></td>
                <td class="name left"><a href="z_424a1052f51881f8_verification_py.html#t47"><data value='Meta'>VerificationCode.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_424a1052f51881f8_verification_py.html">apps\accounts\models\verification.py</a></td>
                <td class="name left"><a href="z_424a1052f51881f8_verification_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6b96ac88d8d43145___init___py.html">apps\accounts\notifications\__init__.py</a></td>
                <td class="name left"><a href="z_6b96ac88d8d43145___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6b96ac88d8d43145_email_notification_py.html#t8">apps\accounts\notifications\email_notification.py</a></td>
                <td class="name left"><a href="z_6b96ac88d8d43145_email_notification_py.html#t8"><data value='EmailNotificationService'>EmailNotificationService</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6b96ac88d8d43145_email_notification_py.html">apps\accounts\notifications\email_notification.py</a></td>
                <td class="name left"><a href="z_6b96ac88d8d43145_email_notification_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b9b6fc10c689f92___init___py.html">apps\accounts\repositories\__init__.py</a></td>
                <td class="name left"><a href="z_3b9b6fc10c689f92___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b9b6fc10c689f92_user_repository_py.html#t7">apps\accounts\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_3b9b6fc10c689f92_user_repository_py.html#t7"><data value='DjangoUserRepository'>DjangoUserRepository</data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b9b6fc10c689f92_user_repository_py.html">apps\accounts\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_3b9b6fc10c689f92_user_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b9b6fc10c689f92_verification_repository_py.html#t6">apps\accounts\repositories\verification_repository.py</a></td>
                <td class="name left"><a href="z_3b9b6fc10c689f92_verification_repository_py.html#t6"><data value='DjangoVerificationRepository'>DjangoVerificationRepository</data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b9b6fc10c689f92_verification_repository_py.html">apps\accounts\repositories\verification_repository.py</a></td>
                <td class="name left"><a href="z_3b9b6fc10c689f92_verification_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f42f61afc382a2f___init___py.html">apps\accounts\services\__init__.py</a></td>
                <td class="name left"><a href="z_8f42f61afc382a2f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f42f61afc382a2f_auth_service_py.html#t5">apps\accounts\services\auth_service.py</a></td>
                <td class="name left"><a href="z_8f42f61afc382a2f_auth_service_py.html#t5"><data value='AuthService'>AuthService</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f42f61afc382a2f_auth_service_py.html">apps\accounts\services\auth_service.py</a></td>
                <td class="name left"><a href="z_8f42f61afc382a2f_auth_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f42f61afc382a2f_email_service_py.html#t11">apps\accounts\services\email_service.py</a></td>
                <td class="name left"><a href="z_8f42f61afc382a2f_email_service_py.html#t11"><data value='EmailService'>EmailService</data></a></td>
                <td>104</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="0 104">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f42f61afc382a2f_email_service_py.html">apps\accounts\services\email_service.py</a></td>
                <td class="name left"><a href="z_8f42f61afc382a2f_email_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f42f61afc382a2f_image_service_py.html#t11">apps\accounts\services\image_service.py</a></td>
                <td class="name left"><a href="z_8f42f61afc382a2f_image_service_py.html#t11"><data value='ImageService'>ImageService</data></a></td>
                <td>49</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="20 49">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f42f61afc382a2f_image_service_py.html">apps\accounts\services\image_service.py</a></td>
                <td class="name left"><a href="z_8f42f61afc382a2f_image_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f42f61afc382a2f_password_service_py.html#t11">apps\accounts\services\password_service.py</a></td>
                <td class="name left"><a href="z_8f42f61afc382a2f_password_service_py.html#t11"><data value='PasswordService'>PasswordService</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f42f61afc382a2f_password_service_py.html">apps\accounts\services\password_service.py</a></td>
                <td class="name left"><a href="z_8f42f61afc382a2f_password_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="9 11">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f42f61afc382a2f_registration_service_py.html#t17">apps\accounts\services\registration_service.py</a></td>
                <td class="name left"><a href="z_8f42f61afc382a2f_registration_service_py.html#t17"><data value='RegistrationService'>RegistrationService</data></a></td>
                <td>28</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="23 28">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f42f61afc382a2f_registration_service_py.html">apps\accounts\services\registration_service.py</a></td>
                <td class="name left"><a href="z_8f42f61afc382a2f_registration_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_signals_py.html">apps\accounts\signals.py</a></td>
                <td class="name left"><a href="z_0daf9d4b3925bce1_signals_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_tests_py.html">apps\accounts\tests.py</a></td>
                <td class="name left"><a href="z_0daf9d4b3925bce1_tests_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230___init___py.html">apps\accounts\tests\__init__.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_forms_py.html#t9">apps\accounts\tests\test_forms.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_forms_py.html#t9"><data value='RegistrationFormTest'>RegistrationFormTest</data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_forms_py.html#t121">apps\accounts\tests\test_forms.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_forms_py.html#t121"><data value='VerificationFormTest'>VerificationFormTest</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_forms_py.html#t157">apps\accounts\tests\test_forms.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_forms_py.html#t157"><data value='LoginFormTest'>LoginFormTest</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_forms_py.html">apps\accounts\tests\test_forms.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_forms_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_middleware_py.html#t16">apps\accounts\tests\test_middleware.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_middleware_py.html#t16"><data value='AccessControlMiddlewareTest'>AccessControlMiddlewareTest</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_middleware_py.html#t138">apps\accounts\tests\test_middleware.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_middleware_py.html#t138"><data value='LoginRedirectMiddlewareTest'>LoginRedirectMiddlewareTest</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_middleware_py.html#t178">apps\accounts\tests\test_middleware.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_middleware_py.html#t178"><data value='SecurityHeadersMiddlewareTest'>SecurityHeadersMiddlewareTest</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_middleware_py.html">apps\accounts\tests\test_middleware.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_middleware_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_models_py.html#t15">apps\accounts\tests\test_models.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_models_py.html#t15"><data value='UserModelTest'>UserModelTest</data></a></td>
                <td>49</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="49 49">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_models_py.html#t130">apps\accounts\tests\test_models.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_models_py.html#t130"><data value='VerificationCodeModelTest'>VerificationCodeModelTest</data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_models_py.html">apps\accounts\tests\test_models.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_repositories_py.html#t11">apps\accounts\tests\test_repositories.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_repositories_py.html#t11"><data value='DjangoUserRepositoryTest'>DjangoUserRepositoryTest</data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_repositories_py.html#t109">apps\accounts\tests\test_repositories.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_repositories_py.html#t109"><data value='DjangoVerificationRepositoryTest'>DjangoVerificationRepositoryTest</data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_repositories_py.html">apps\accounts\tests\test_repositories.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_repositories_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_services_py.html#t19">apps\accounts\tests\test_services.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_services_py.html#t19"><data value='AuthServiceTest'>AuthServiceTest</data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_services_py.html#t73">apps\accounts\tests\test_services.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_services_py.html#t73"><data value='RegistrationServiceTest'>RegistrationServiceTest</data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_services_py.html#t165">apps\accounts\tests\test_services.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_services_py.html#t165"><data value='ImageServiceTest'>ImageServiceTest</data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_services_py.html">apps\accounts\tests\test_services.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_services_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_validators_py.html#t14">apps\accounts\tests\test_validators.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_validators_py.html#t14"><data value='UserEmailValidatorTest'>UserEmailValidatorTest</data></a></td>
                <td>25</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="17 25">68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_validators_py.html#t86">apps\accounts\tests\test_validators.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_validators_py.html#t86"><data value='UserUsernameValidatorTest'>UserUsernameValidatorTest</data></a></td>
                <td>20</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="16 20">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_validators_py.html#t146">apps\accounts\tests\test_validators.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_validators_py.html#t146"><data value='UserPasswordValidatorTest'>UserPasswordValidatorTest</data></a></td>
                <td>19</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="15 19">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_validators_py.html#t196">apps\accounts\tests\test_validators.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_validators_py.html#t196"><data value='UserDataValidatorTest'>UserDataValidatorTest</data></a></td>
                <td>24</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="18 24">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_validators_py.html">apps\accounts\tests\test_validators.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_validators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="38 38">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_views_py.html#t10">apps\accounts\tests\test_views.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_views_py.html#t10"><data value='RegistrationViewTest'>RegistrationViewTest</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_views_py.html#t81">apps\accounts\tests\test_views.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_views_py.html#t81"><data value='VerificationViewTest'>VerificationViewTest</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_views_py.html#t161">apps\accounts\tests\test_views.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_views_py.html#t161"><data value='LoginViewTest'>LoginViewTest</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_views_py.html#t223">apps\accounts\tests\test_views.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_views_py.html#t223"><data value='LogoutViewTest'>LogoutViewTest</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_views_py.html">apps\accounts\tests\test_views.py</a></td>
                <td class="name left"><a href="z_4cd509e0a38bf230_test_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_urls_py.html">apps\accounts\urls.py</a></td>
                <td class="name left"><a href="z_0daf9d4b3925bce1_urls_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_utils_py.html">apps\accounts\utils.py</a></td>
                <td class="name left"><a href="z_0daf9d4b3925bce1_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af5f57e6b6e54959___init___py.html">apps\accounts\validators\__init__.py</a></td>
                <td class="name left"><a href="z_af5f57e6b6e54959___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af5f57e6b6e54959_user_validators_py.html#t14">apps\accounts\validators\user_validators.py</a></td>
                <td class="name left"><a href="z_af5f57e6b6e54959_user_validators_py.html#t14"><data value='UserEmailValidator'>UserEmailValidator</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af5f57e6b6e54959_user_validators_py.html#t45">apps\accounts\validators\user_validators.py</a></td>
                <td class="name left"><a href="z_af5f57e6b6e54959_user_validators_py.html#t45"><data value='UserUsernameValidator'>UserUsernameValidator</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af5f57e6b6e54959_user_validators_py.html#t83">apps\accounts\validators\user_validators.py</a></td>
                <td class="name left"><a href="z_af5f57e6b6e54959_user_validators_py.html#t83"><data value='UserPasswordValidator'>UserPasswordValidator</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af5f57e6b6e54959_user_validators_py.html#t116">apps\accounts\validators\user_validators.py</a></td>
                <td class="name left"><a href="z_af5f57e6b6e54959_user_validators_py.html#t116"><data value='UserDataValidator'>UserDataValidator</data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af5f57e6b6e54959_user_validators_py.html">apps\accounts\validators\user_validators.py</a></td>
                <td class="name left"><a href="z_af5f57e6b6e54959_user_validators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6___init___py.html">apps\accounts\views\__init__.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_authentication_py.html#t11">apps\accounts\views\authentication.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6_authentication_py.html#t11"><data value='LoginView'>LoginView</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_authentication_py.html#t84">apps\accounts\views\authentication.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6_authentication_py.html#t84"><data value='LogoutView'>LogoutView</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_authentication_py.html">apps\accounts\views\authentication.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6_authentication_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_email_test_py.html#t15">apps\accounts\views\email_test.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6_email_test_py.html#t15"><data value='EmailDiagnosticView'>EmailDiagnosticView</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_email_test_py.html#t52">apps\accounts\views\email_test.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6_email_test_py.html#t52"><data value='TestEmailSendView'>TestEmailSendView</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_email_test_py.html#t100">apps\accounts\views\email_test.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6_email_test_py.html#t100"><data value='TestConnectionView'>TestConnectionView</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_email_test_py.html#t122">apps\accounts\views\email_test.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6_email_test_py.html#t122"><data value='QuickEmailSetupView'>QuickEmailSetupView</data></a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_email_test_py.html#t231">apps\accounts\views\email_test.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6_email_test_py.html#t231"><data value='PasswordResetTestView'>PasswordResetTestView</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_email_test_py.html">apps\accounts\views\email_test.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6_email_test_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_password_reset_py.html#t13">apps\accounts\views\password_reset.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6_password_reset_py.html#t13"><data value='PasswordResetRequestView'>PasswordResetRequestView</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_password_reset_py.html#t49">apps\accounts\views\password_reset.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6_password_reset_py.html#t49"><data value='PasswordResetConfirmView'>PasswordResetConfirmView</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_password_reset_py.html">apps\accounts\views\password_reset.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6_password_reset_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_profile_py.html#t16">apps\accounts\views\profile.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6_profile_py.html#t16"><data value='UserProfileView'>UserProfileView</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_profile_py.html#t30">apps\accounts\views\profile.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6_profile_py.html#t30"><data value='UserUpdateView'>UserUpdateView</data></a></td>
                <td>77</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="0 77">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_profile_py.html#t167">apps\accounts\views\profile.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6_profile_py.html#t167"><data value='RemoveAvatarView'>RemoveAvatarView</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_profile_py.html">apps\accounts\views\profile.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6_profile_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_registration_py.html#t24">apps\accounts\views\registration.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6_registration_py.html#t24"><data value='RegistrationView'>RegistrationView</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_registration_py.html#t83">apps\accounts\views\registration.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6_registration_py.html#t83"><data value='VerificationView'>VerificationView</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_registration_py.html">apps\accounts\views\registration.py</a></td>
                <td class="name left"><a href="z_ae12c07b376a20c6_registration_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="24 30">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b6255d6c5a7dc68___init___py.html">apps\articles\__init__.py</a></td>
                <td class="name left"><a href="z_9b6255d6c5a7dc68___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b6255d6c5a7dc68_admin_py.html#t5">apps\articles\admin.py</a></td>
                <td class="name left"><a href="z_9b6255d6c5a7dc68_admin_py.html#t5"><data value='CategoryAdmin'>CategoryAdmin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b6255d6c5a7dc68_admin_py.html#t18">apps\articles\admin.py</a></td>
                <td class="name left"><a href="z_9b6255d6c5a7dc68_admin_py.html#t18"><data value='TagAdmin'>TagAdmin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b6255d6c5a7dc68_admin_py.html#t30">apps\articles\admin.py</a></td>
                <td class="name left"><a href="z_9b6255d6c5a7dc68_admin_py.html#t30"><data value='ArticleAdmin'>ArticleAdmin</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b6255d6c5a7dc68_admin_py.html#t68">apps\articles\admin.py</a></td>
                <td class="name left"><a href="z_9b6255d6c5a7dc68_admin_py.html#t68"><data value='CommentAdmin'>CommentAdmin</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b6255d6c5a7dc68_admin_py.html">apps\articles\admin.py</a></td>
                <td class="name left"><a href="z_9b6255d6c5a7dc68_admin_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="40 40">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b6255d6c5a7dc68_apps_py.html#t4">apps\articles\apps.py</a></td>
                <td class="name left"><a href="z_9b6255d6c5a7dc68_apps_py.html#t4"><data value='ArticlesConfig'>ArticlesConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b6255d6c5a7dc68_apps_py.html">apps\articles\apps.py</a></td>
                <td class="name left"><a href="z_9b6255d6c5a7dc68_apps_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_10e6b26d1150d373___init___py.html">apps\articles\interfaces\__init__.py</a></td>
                <td class="name left"><a href="z_10e6b26d1150d373___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_10e6b26d1150d373_repositories_py.html#t5">apps\articles\interfaces\repositories.py</a></td>
                <td class="name left"><a href="z_10e6b26d1150d373_repositories_py.html#t5"><data value='IArticleRepository'>IArticleRepository</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_10e6b26d1150d373_repositories_py.html#t68">apps\articles\interfaces\repositories.py</a></td>
                <td class="name left"><a href="z_10e6b26d1150d373_repositories_py.html#t68"><data value='ICategoryRepository'>ICategoryRepository</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_10e6b26d1150d373_repositories_py.html#t106">apps\articles\interfaces\repositories.py</a></td>
                <td class="name left"><a href="z_10e6b26d1150d373_repositories_py.html#t106"><data value='ITagRepository'>ITagRepository</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_10e6b26d1150d373_repositories_py.html#t149">apps\articles\interfaces\repositories.py</a></td>
                <td class="name left"><a href="z_10e6b26d1150d373_repositories_py.html#t149"><data value='ICommentRepository'>ICommentRepository</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_10e6b26d1150d373_repositories_py.html">apps\articles\interfaces\repositories.py</a></td>
                <td class="name left"><a href="z_10e6b26d1150d373_repositories_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>79</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="79 79">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_10e6b26d1150d373_services_py.html#t8">apps\articles\interfaces\services.py</a></td>
                <td class="name left"><a href="z_10e6b26d1150d373_services_py.html#t8"><data value='IArticleService'>IArticleService</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_10e6b26d1150d373_services_py.html#t115">apps\articles\interfaces\services.py</a></td>
                <td class="name left"><a href="z_10e6b26d1150d373_services_py.html#t115"><data value='ICategoryService'>ICategoryService</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_10e6b26d1150d373_services_py.html#t154">apps\articles\interfaces\services.py</a></td>
                <td class="name left"><a href="z_10e6b26d1150d373_services_py.html#t154"><data value='ITagService'>ITagService</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_10e6b26d1150d373_services_py.html#t184">apps\articles\interfaces\services.py</a></td>
                <td class="name left"><a href="z_10e6b26d1150d373_services_py.html#t184"><data value='ICommentService'>ICommentService</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_10e6b26d1150d373_services_py.html">apps\articles\interfaces\services.py</a></td>
                <td class="name left"><a href="z_10e6b26d1150d373_services_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>53</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="53 53">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ac3d5cc3c7d2853_0001_initial_py.html#t9">apps\articles\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_6ac3d5cc3c7d2853_0001_initial_py.html#t9"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ac3d5cc3c7d2853_0001_initial_py.html">apps\articles\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_6ac3d5cc3c7d2853_0001_initial_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ac3d5cc3c7d2853___init___py.html">apps\articles\migrations\__init__.py</a></td>
                <td class="name left"><a href="z_6ac3d5cc3c7d2853___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b6255d6c5a7dc68_models_py.html">apps\articles\models.py</a></td>
                <td class="name left"><a href="z_9b6255d6c5a7dc68_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d9000b597388c2b4___init___py.html">apps\articles\models\__init__.py</a></td>
                <td class="name left"><a href="z_d9000b597388c2b4___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d9000b597388c2b4_article_py.html#t10">apps\articles\models\article.py</a></td>
                <td class="name left"><a href="z_d9000b597388c2b4_article_py.html#t10"><data value='Article'>Article</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d9000b597388c2b4_article_py.html#t161">apps\articles\models\article.py</a></td>
                <td class="name left"><a href="z_d9000b597388c2b4_article_py.html#t161"><data value='Meta'>Article.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d9000b597388c2b4_article_py.html">apps\articles\models\article.py</a></td>
                <td class="name left"><a href="z_d9000b597388c2b4_article_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>50</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="50 50">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d9000b597388c2b4_category_py.html#t6">apps\articles\models\category.py</a></td>
                <td class="name left"><a href="z_d9000b597388c2b4_category_py.html#t6"><data value='Category'>Category</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d9000b597388c2b4_category_py.html#t81">apps\articles\models\category.py</a></td>
                <td class="name left"><a href="z_d9000b597388c2b4_category_py.html#t81"><data value='Meta'>Category.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d9000b597388c2b4_category_py.html">apps\articles\models\category.py</a></td>
                <td class="name left"><a href="z_d9000b597388c2b4_category_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d9000b597388c2b4_comment_py.html#t7">apps\articles\models\comment.py</a></td>
                <td class="name left"><a href="z_d9000b597388c2b4_comment_py.html#t7"><data value='Comment'>Comment</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d9000b597388c2b4_comment_py.html#t99">apps\articles\models\comment.py</a></td>
                <td class="name left"><a href="z_d9000b597388c2b4_comment_py.html#t99"><data value='Meta'>Comment.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d9000b597388c2b4_comment_py.html">apps\articles\models\comment.py</a></td>
                <td class="name left"><a href="z_d9000b597388c2b4_comment_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d9000b597388c2b4_tag_py.html#t6">apps\articles\models\tag.py</a></td>
                <td class="name left"><a href="z_d9000b597388c2b4_tag_py.html#t6"><data value='Tag'>Tag</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d9000b597388c2b4_tag_py.html#t62">apps\articles\models\tag.py</a></td>
                <td class="name left"><a href="z_d9000b597388c2b4_tag_py.html#t62"><data value='Meta'>Tag.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d9000b597388c2b4_tag_py.html">apps\articles\models\tag.py</a></td>
                <td class="name left"><a href="z_d9000b597388c2b4_tag_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6831235e64db73da___init___py.html">apps\articles\repositories\__init__.py</a></td>
                <td class="name left"><a href="z_6831235e64db73da___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6831235e64db73da_article_repository_py.html#t8">apps\articles\repositories\article_repository.py</a></td>
                <td class="name left"><a href="z_6831235e64db73da_article_repository_py.html#t8"><data value='DjangoArticleRepository'>DjangoArticleRepository</data></a></td>
                <td>73</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="0 73">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6831235e64db73da_article_repository_py.html">apps\articles\repositories\article_repository.py</a></td>
                <td class="name left"><a href="z_6831235e64db73da_article_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_99cb94331c41d741___init___py.html">apps\articles\services\__init__.py</a></td>
                <td class="name left"><a href="z_99cb94331c41d741___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_99cb94331c41d741_article_service_py.html#t12">apps\articles\services\article_service.py</a></td>
                <td class="name left"><a href="z_99cb94331c41d741_article_service_py.html#t12"><data value='ArticleService'>ArticleService</data></a></td>
                <td>59</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="0 59">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_99cb94331c41d741_article_service_py.html">apps\articles\services\article_service.py</a></td>
                <td class="name left"><a href="z_99cb94331c41d741_article_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b6255d6c5a7dc68_tests_py.html">apps\articles\tests.py</a></td>
                <td class="name left"><a href="z_9b6255d6c5a7dc68_tests_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b6255d6c5a7dc68_urls_py.html">apps\articles\urls.py</a></td>
                <td class="name left"><a href="z_9b6255d6c5a7dc68_urls_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b6255d6c5a7dc68_views_py.html">apps\articles\views.py</a></td>
                <td class="name left"><a href="z_9b6255d6c5a7dc68_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ea92ad1c62fa4397___init___py.html">apps\articles\views\__init__.py</a></td>
                <td class="name left"><a href="z_ea92ad1c62fa4397___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ea92ad1c62fa4397_article_views_py.html#t7">apps\articles\views\article_views.py</a></td>
                <td class="name left"><a href="z_ea92ad1c62fa4397_article_views_py.html#t7"><data value='ArticleListView'>ArticleListView</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ea92ad1c62fa4397_article_views_py.html#t38">apps\articles\views\article_views.py</a></td>
                <td class="name left"><a href="z_ea92ad1c62fa4397_article_views_py.html#t38"><data value='ArticleDetailView'>ArticleDetailView</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ea92ad1c62fa4397_article_views_py.html#t84">apps\articles\views\article_views.py</a></td>
                <td class="name left"><a href="z_ea92ad1c62fa4397_article_views_py.html#t84"><data value='ArticleSearchView'>ArticleSearchView</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ea92ad1c62fa4397_article_views_py.html">apps\articles\views\article_views.py</a></td>
                <td class="name left"><a href="z_ea92ad1c62fa4397_article_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3___init___py.html">apps\config\__init__.py</a></td>
                <td class="name left"><a href="z_ad224057aff2f7a3___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_admin_py.html#t9">apps\config\admin.py</a></td>
                <td class="name left"><a href="z_ad224057aff2f7a3_admin_py.html#t9"><data value='SystemConfigurationAdmin'>SystemConfigurationAdmin</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_admin_py.html#t35">apps\config\admin.py</a></td>
                <td class="name left"><a href="z_ad224057aff2f7a3_admin_py.html#t35"><data value='UserActivityLogAdmin'>UserActivityLogAdmin</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_admin_py.html#t71">apps\config\admin.py</a></td>
                <td class="name left"><a href="z_ad224057aff2f7a3_admin_py.html#t71"><data value='CustomUserAdmin'>CustomUserAdmin</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_admin_py.html">apps\config\admin.py</a></td>
                <td class="name left"><a href="z_ad224057aff2f7a3_admin_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_apps_py.html#t4">apps\config\apps.py</a></td>
                <td class="name left"><a href="z_ad224057aff2f7a3_apps_py.html#t4"><data value='ConfigConfig'>ConfigConfig</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_apps_py.html">apps\config\apps.py</a></td>
                <td class="name left"><a href="z_ad224057aff2f7a3_apps_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a___init___py.html">apps\config\forms\__init__.py</a></td>
                <td class="name left"><a href="z_e131a04fbcf9a69a___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_advanced_config_forms_py.html#t14">apps\config\forms\advanced_config_forms.py</a></td>
                <td class="name left"><a href="z_e131a04fbcf9a69a_advanced_config_forms_py.html#t14"><data value='DatabaseConfigForm'>DatabaseConfigForm</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_advanced_config_forms_py.html#t212">apps\config\forms\advanced_config_forms.py</a></td>
                <td class="name left"><a href="z_e131a04fbcf9a69a_advanced_config_forms_py.html#t212"><data value='EmailConfigForm'>EmailConfigForm</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_advanced_config_forms_py.html#t444">apps\config\forms\advanced_config_forms.py</a></td>
                <td class="name left"><a href="z_e131a04fbcf9a69a_advanced_config_forms_py.html#t444"><data value='EnvironmentVariablesForm'>EnvironmentVariablesForm</data></a></td>
                <td>65</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="0 65">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_advanced_config_forms_py.html">apps\config\forms\advanced_config_forms.py</a></td>
                <td class="name left"><a href="z_e131a04fbcf9a69a_advanced_config_forms_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>57</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="57 57">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_multi_config_forms_py.html#t9">apps\config\forms\multi_config_forms.py</a></td>
                <td class="name left"><a href="z_e131a04fbcf9a69a_multi_config_forms_py.html#t9"><data value='EmailConfigurationForm'>EmailConfigurationForm</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_multi_config_forms_py.html#t12">apps\config\forms\multi_config_forms.py</a></td>
                <td class="name left"><a href="z_e131a04fbcf9a69a_multi_config_forms_py.html#t12"><data value='Meta'>EmailConfigurationForm.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_multi_config_forms_py.html#t134">apps\config\forms\multi_config_forms.py</a></td>
                <td class="name left"><a href="z_e131a04fbcf9a69a_multi_config_forms_py.html#t134"><data value='DatabaseConfigurationForm'>DatabaseConfigurationForm</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_multi_config_forms_py.html#t137">apps\config\forms\multi_config_forms.py</a></td>
                <td class="name left"><a href="z_e131a04fbcf9a69a_multi_config_forms_py.html#t137"><data value='Meta'>DatabaseConfigurationForm.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_multi_config_forms_py.html#t271">apps\config\forms\multi_config_forms.py</a></td>
                <td class="name left"><a href="z_e131a04fbcf9a69a_multi_config_forms_py.html#t271"><data value='ConfigurationTestForm'>ConfigurationTestForm</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_multi_config_forms_py.html">apps\config\forms\multi_config_forms.py</a></td>
                <td class="name left"><a href="z_e131a04fbcf9a69a_multi_config_forms_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_user_forms_py.html#t12">apps\config\forms\user_forms.py</a></td>
                <td class="name left"><a href="z_e131a04fbcf9a69a_user_forms_py.html#t12"><data value='UserCreateForm'>UserCreateForm</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_user_forms_py.html#t125">apps\config\forms\user_forms.py</a></td>
                <td class="name left"><a href="z_e131a04fbcf9a69a_user_forms_py.html#t125"><data value='Meta'>UserCreateForm.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_user_forms_py.html#t146">apps\config\forms\user_forms.py</a></td>
                <td class="name left"><a href="z_e131a04fbcf9a69a_user_forms_py.html#t146"><data value='UserUpdateForm'>UserUpdateForm</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_user_forms_py.html#t206">apps\config\forms\user_forms.py</a></td>
                <td class="name left"><a href="z_e131a04fbcf9a69a_user_forms_py.html#t206"><data value='Meta'>UserUpdateForm.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_user_forms_py.html#t265">apps\config\forms\user_forms.py</a></td>
                <td class="name left"><a href="z_e131a04fbcf9a69a_user_forms_py.html#t265"><data value='UserPermissionForm'>UserPermissionForm</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_user_forms_py.html#t292">apps\config\forms\user_forms.py</a></td>
                <td class="name left"><a href="z_e131a04fbcf9a69a_user_forms_py.html#t292"><data value='UserSearchForm'>UserSearchForm</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_user_forms_py.html">apps\config\forms\user_forms.py</a></td>
                <td class="name left"><a href="z_e131a04fbcf9a69a_user_forms_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>48</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="48 48">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2807aa7e64bc1821___init___py.html">apps\config\interfaces\__init__.py</a></td>
                <td class="name left"><a href="z_2807aa7e64bc1821___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2807aa7e64bc1821_repositories_py.html#t9">apps\config\interfaces\repositories.py</a></td>
                <td class="name left"><a href="z_2807aa7e64bc1821_repositories_py.html#t9"><data value='IUserRepository'>IUserRepository</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2807aa7e64bc1821_repositories_py.html#t57">apps\config\interfaces\repositories.py</a></td>
                <td class="name left"><a href="z_2807aa7e64bc1821_repositories_py.html#t57"><data value='IPermissionRepository'>IPermissionRepository</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2807aa7e64bc1821_repositories_py.html#t90">apps\config\interfaces\repositories.py</a></td>
                <td class="name left"><a href="z_2807aa7e64bc1821_repositories_py.html#t90"><data value='IGroupRepository'>IGroupRepository</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2807aa7e64bc1821_repositories_py.html#t138">apps\config\interfaces\repositories.py</a></td>
                <td class="name left"><a href="z_2807aa7e64bc1821_repositories_py.html#t138"><data value='ISystemConfigRepository'>ISystemConfigRepository</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2807aa7e64bc1821_repositories_py.html#t166">apps\config\interfaces\repositories.py</a></td>
                <td class="name left"><a href="z_2807aa7e64bc1821_repositories_py.html#t166"><data value='IAuditLogRepository'>IAuditLogRepository</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2807aa7e64bc1821_repositories_py.html">apps\config\interfaces\repositories.py</a></td>
                <td class="name left"><a href="z_2807aa7e64bc1821_repositories_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>79</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="79 79">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2807aa7e64bc1821_services_py.html#t9">apps\config\interfaces\services.py</a></td>
                <td class="name left"><a href="z_2807aa7e64bc1821_services_py.html#t9"><data value='IUserManagementService'>IUserManagementService</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2807aa7e64bc1821_services_py.html#t70">apps\config\interfaces\services.py</a></td>
                <td class="name left"><a href="z_2807aa7e64bc1821_services_py.html#t70"><data value='IPermissionManagementService'>IPermissionManagementService</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2807aa7e64bc1821_services_py.html#t135">apps\config\interfaces\services.py</a></td>
                <td class="name left"><a href="z_2807aa7e64bc1821_services_py.html#t135"><data value='ISystemConfigService'>ISystemConfigService</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2807aa7e64bc1821_services_py.html#t178">apps\config\interfaces\services.py</a></td>
                <td class="name left"><a href="z_2807aa7e64bc1821_services_py.html#t178"><data value='IAuditLogService'>IAuditLogService</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2807aa7e64bc1821_services_py.html">apps\config\interfaces\services.py</a></td>
                <td class="name left"><a href="z_2807aa7e64bc1821_services_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>48</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="48 48">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0017935c50f3bc1c___init___py.html">apps\config\management\__init__.py</a></td>
                <td class="name left"><a href="z_0017935c50f3bc1c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c595fba4bf79988f___init___py.html">apps\config\management\commands\__init__.py</a></td>
                <td class="name left"><a href="z_c595fba4bf79988f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c595fba4bf79988f_create_admin_user_py.html#t9">apps\config\management\commands\create_admin_user.py</a></td>
                <td class="name left"><a href="z_c595fba4bf79988f_create_admin_user_py.html#t9"><data value='Command'>Command</data></a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c595fba4bf79988f_create_admin_user_py.html">apps\config\management\commands\create_admin_user.py</a></td>
                <td class="name left"><a href="z_c595fba4bf79988f_create_admin_user_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c595fba4bf79988f_migrate_configs_py.html#t8">apps\config\management\commands\migrate_configs.py</a></td>
                <td class="name left"><a href="z_c595fba4bf79988f_migrate_configs_py.html#t8"><data value='Command'>Command</data></a></td>
                <td>73</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="0 73">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c595fba4bf79988f_migrate_configs_py.html">apps\config\management\commands\migrate_configs.py</a></td>
                <td class="name left"><a href="z_c595fba4bf79988f_migrate_configs_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c595fba4bf79988f_setup_permissions_py.html#t8">apps\config\management\commands\setup_permissions.py</a></td>
                <td class="name left"><a href="z_c595fba4bf79988f_setup_permissions_py.html#t8"><data value='Command'>Command</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c595fba4bf79988f_setup_permissions_py.html">apps\config\management\commands\setup_permissions.py</a></td>
                <td class="name left"><a href="z_c595fba4bf79988f_setup_permissions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4df18df340a057a7_0001_initial_py.html#t9">apps\config\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_4df18df340a057a7_0001_initial_py.html#t9"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4df18df340a057a7_0001_initial_py.html">apps\config\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_4df18df340a057a7_0001_initial_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4df18df340a057a7_0002_databaseconfiguration_emailconfiguration_py.html#t8">apps\config\migrations\0002_databaseconfiguration_emailconfiguration.py</a></td>
                <td class="name left"><a href="z_4df18df340a057a7_0002_databaseconfiguration_emailconfiguration_py.html#t8"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4df18df340a057a7_0002_databaseconfiguration_emailconfiguration_py.html">apps\config\migrations\0002_databaseconfiguration_emailconfiguration.py</a></td>
                <td class="name left"><a href="z_4df18df340a057a7_0002_databaseconfiguration_emailconfiguration_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4df18df340a057a7_0003_alter_emailconfiguration_email_host_password_and_more_py.html#t6">apps\config\migrations\0003_alter_emailconfiguration_email_host_password_and_more.py</a></td>
                <td class="name left"><a href="z_4df18df340a057a7_0003_alter_emailconfiguration_email_host_password_and_more_py.html#t6"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4df18df340a057a7_0003_alter_emailconfiguration_email_host_password_and_more_py.html">apps\config\migrations\0003_alter_emailconfiguration_email_host_password_and_more.py</a></td>
                <td class="name left"><a href="z_4df18df340a057a7_0003_alter_emailconfiguration_email_host_password_and_more_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4df18df340a057a7_0004_alter_emailconfiguration_created_by_and_more_py.html#t8">apps\config\migrations\0004_alter_emailconfiguration_created_by_and_more.py</a></td>
                <td class="name left"><a href="z_4df18df340a057a7_0004_alter_emailconfiguration_created_by_and_more_py.html#t8"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4df18df340a057a7_0004_alter_emailconfiguration_created_by_and_more_py.html">apps\config\migrations\0004_alter_emailconfiguration_created_by_and_more.py</a></td>
                <td class="name left"><a href="z_4df18df340a057a7_0004_alter_emailconfiguration_created_by_and_more_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4df18df340a057a7___init___py.html">apps\config\migrations\__init__.py</a></td>
                <td class="name left"><a href="z_4df18df340a057a7___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_mixins_py.html#t9">apps\config\mixins.py</a></td>
                <td class="name left"><a href="z_ad224057aff2f7a3_mixins_py.html#t9"><data value='AdminRequiredMixin'>AdminRequiredMixin</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_mixins_py.html#t91">apps\config\mixins.py</a></td>
                <td class="name left"><a href="z_ad224057aff2f7a3_mixins_py.html#t91"><data value='SuperuserRequiredMixin'>SuperuserRequiredMixin</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_mixins_py.html#t131">apps\config\mixins.py</a></td>
                <td class="name left"><a href="z_ad224057aff2f7a3_mixins_py.html#t131"><data value='StaffRequiredMixin'>StaffRequiredMixin</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_mixins_py.html#t165">apps\config\mixins.py</a></td>
                <td class="name left"><a href="z_ad224057aff2f7a3_mixins_py.html#t165"><data value='ConfigPermissionMixin'>ConfigPermissionMixin</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_mixins_py.html#t271">apps\config\mixins.py</a></td>
                <td class="name left"><a href="z_ad224057aff2f7a3_mixins_py.html#t271"><data value='PermissionHelperMixin'>PermissionHelperMixin</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_mixins_py.html">apps\config\mixins.py</a></td>
                <td class="name left"><a href="z_ad224057aff2f7a3_mixins_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="24 44">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_models_py.html">apps\config\models.py</a></td>
                <td class="name left"><a href="z_ad224057aff2f7a3_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_038d050f2a364532___init___py.html">apps\config\models\__init__.py</a></td>
                <td class="name left"><a href="z_038d050f2a364532___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_038d050f2a364532_configuration_models_py.html#t10">apps\config\models\configuration_models.py</a></td>
                <td class="name left"><a href="z_038d050f2a364532_configuration_models_py.html#t10"><data value='EmailConfiguration'>EmailConfiguration</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_038d050f2a364532_configuration_models_py.html#t131">apps\config\models\configuration_models.py</a></td>
                <td class="name left"><a href="z_038d050f2a364532_configuration_models_py.html#t131"><data value='Meta'>EmailConfiguration.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_038d050f2a364532_configuration_models_py.html#t240">apps\config\models\configuration_models.py</a></td>
                <td class="name left"><a href="z_038d050f2a364532_configuration_models_py.html#t240"><data value='DatabaseConfiguration'>DatabaseConfiguration</data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_038d050f2a364532_configuration_models_py.html#t335">apps\config\models\configuration_models.py</a></td>
                <td class="name left"><a href="z_038d050f2a364532_configuration_models_py.html#t335"><data value='Meta'>DatabaseConfiguration.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_038d050f2a364532_configuration_models_py.html">apps\config\models\configuration_models.py</a></td>
                <td class="name left"><a href="z_038d050f2a364532_configuration_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>70</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="70 70">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_038d050f2a364532_system_configuration_py.html#t8">apps\config\models\system_configuration.py</a></td>
                <td class="name left"><a href="z_038d050f2a364532_system_configuration_py.html#t8"><data value='SystemConfiguration'>SystemConfiguration</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_038d050f2a364532_system_configuration_py.html#t49">apps\config\models\system_configuration.py</a></td>
                <td class="name left"><a href="z_038d050f2a364532_system_configuration_py.html#t49"><data value='Meta'>SystemConfiguration.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_038d050f2a364532_system_configuration_py.html">apps\config\models\system_configuration.py</a></td>
                <td class="name left"><a href="z_038d050f2a364532_system_configuration_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_038d050f2a364532_user_activity_log_py.html#t6">apps\config\models\user_activity_log.py</a></td>
                <td class="name left"><a href="z_038d050f2a364532_user_activity_log_py.html#t6"><data value='UserActivityLog'>UserActivityLog</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_038d050f2a364532_user_activity_log_py.html#t65">apps\config\models\user_activity_log.py</a></td>
                <td class="name left"><a href="z_038d050f2a364532_user_activity_log_py.html#t65"><data value='Meta'>UserActivityLog.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_038d050f2a364532_user_activity_log_py.html">apps\config\models\user_activity_log.py</a></td>
                <td class="name left"><a href="z_038d050f2a364532_user_activity_log_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b825969784292050___init___py.html">apps\config\repositories\__init__.py</a></td>
                <td class="name left"><a href="z_b825969784292050___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b825969784292050_config_repository_py.html#t11">apps\config\repositories\config_repository.py</a></td>
                <td class="name left"><a href="z_b825969784292050_config_repository_py.html#t11"><data value='DjangoSystemConfigRepository'>DjangoSystemConfigRepository</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b825969784292050_config_repository_py.html#t66">apps\config\repositories\config_repository.py</a></td>
                <td class="name left"><a href="z_b825969784292050_config_repository_py.html#t66"><data value='DjangoAuditLogRepository'>DjangoAuditLogRepository</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b825969784292050_config_repository_py.html">apps\config\repositories\config_repository.py</a></td>
                <td class="name left"><a href="z_b825969784292050_config_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b825969784292050_permission_repository_py.html#t11">apps\config\repositories\permission_repository.py</a></td>
                <td class="name left"><a href="z_b825969784292050_permission_repository_py.html#t11"><data value='DjangoPermissionRepository'>DjangoPermissionRepository</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b825969784292050_permission_repository_py.html#t54">apps\config\repositories\permission_repository.py</a></td>
                <td class="name left"><a href="z_b825969784292050_permission_repository_py.html#t54"><data value='DjangoGroupRepository'>DjangoGroupRepository</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b825969784292050_permission_repository_py.html">apps\config\repositories\permission_repository.py</a></td>
                <td class="name left"><a href="z_b825969784292050_permission_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b825969784292050_user_repository_py.html#t9">apps\config\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_b825969784292050_user_repository_py.html#t9"><data value='DjangoUserRepository'>DjangoUserRepository</data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b825969784292050_user_repository_py.html">apps\config\repositories\user_repository.py</a></td>
                <td class="name left"><a href="z_b825969784292050_user_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f472e13a5c526310___init___py.html">apps\config\services\__init__.py</a></td>
                <td class="name left"><a href="z_f472e13a5c526310___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f472e13a5c526310_permission_management_service_py.html#t12">apps\config\services\permission_management_service.py</a></td>
                <td class="name left"><a href="z_f472e13a5c526310_permission_management_service_py.html#t12"><data value='PermissionManagementService'>PermissionManagementService</data></a></td>
                <td>49</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f472e13a5c526310_permission_management_service_py.html">apps\config\services\permission_management_service.py</a></td>
                <td class="name left"><a href="z_f472e13a5c526310_permission_management_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f472e13a5c526310_system_config_service_py.html#t12">apps\config\services\system_config_service.py</a></td>
                <td class="name left"><a href="z_f472e13a5c526310_system_config_service_py.html#t12"><data value='SystemConfigService'>SystemConfigService</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f472e13a5c526310_system_config_service_py.html#t123">apps\config\services\system_config_service.py</a></td>
                <td class="name left"><a href="z_f472e13a5c526310_system_config_service_py.html#t123"><data value='AuditLogService'>AuditLogService</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f472e13a5c526310_system_config_service_py.html">apps\config\services\system_config_service.py</a></td>
                <td class="name left"><a href="z_f472e13a5c526310_system_config_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f472e13a5c526310_user_management_service_py.html#t12">apps\config\services\user_management_service.py</a></td>
                <td class="name left"><a href="z_f472e13a5c526310_user_management_service_py.html#t12"><data value='UserManagementService'>UserManagementService</data></a></td>
                <td>42</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f472e13a5c526310_user_management_service_py.html">apps\config\services\user_management_service.py</a></td>
                <td class="name left"><a href="z_f472e13a5c526310_user_management_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_signals_py.html">apps\config\signals.py</a></td>
                <td class="name left"><a href="z_ad224057aff2f7a3_signals_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="17 20">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_89375c84e16dd47a___init___py.html">apps\config\templatetags\__init__.py</a></td>
                <td class="name left"><a href="z_89375c84e16dd47a___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_89375c84e16dd47a_config_extras_py.html">apps\config\templatetags\config_extras.py</a></td>
                <td class="name left"><a href="z_89375c84e16dd47a_config_extras_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>49</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="18 49">37%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_tests_py.html">apps\config\tests.py</a></td>
                <td class="name left"><a href="z_ad224057aff2f7a3_tests_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_urls_py.html">apps\config\urls.py</a></td>
                <td class="name left"><a href="z_ad224057aff2f7a3_urls_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_views_py.html">apps\config\views.py</a></td>
                <td class="name left"><a href="z_ad224057aff2f7a3_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1___init___py.html">apps\config\views\__init__.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_advanced_config_views_py.html#t18">apps\config\views\advanced_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_advanced_config_views_py.html#t18"><data value='DatabaseConfigView'>DatabaseConfigView</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_advanced_config_views_py.html#t48">apps\config\views\advanced_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_advanced_config_views_py.html#t48"><data value='EmailConfigView'>EmailConfigView</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_advanced_config_views_py.html#t103">apps\config\views\advanced_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_advanced_config_views_py.html#t103"><data value='EnvironmentVariablesView'>EnvironmentVariablesView</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_advanced_config_views_py.html#t149">apps\config\views\advanced_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_advanced_config_views_py.html#t149"><data value='TestEmailView'>TestEmailView</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_advanced_config_views_py.html#t181">apps\config\views\advanced_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_advanced_config_views_py.html#t181"><data value='SendTestEmailView'>SendTestEmailView</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_advanced_config_views_py.html#t245">apps\config\views\advanced_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_advanced_config_views_py.html#t245"><data value='ExportConfigView'>ExportConfigView</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_advanced_config_views_py.html#t319">apps\config\views\advanced_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_advanced_config_views_py.html#t319"><data value='ImportConfigView'>ImportConfigView</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_advanced_config_views_py.html">apps\config\views\advanced_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_advanced_config_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_dashboard_py.html#t11">apps\config\views\dashboard.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_dashboard_py.html#t11"><data value='ConfigDashboardView'>ConfigDashboardView</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_dashboard_py.html">apps\config\views\dashboard.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_dashboard_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t20">apps\config\views\multi_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t20"><data value='EmailConfigListView'>EmailConfigListView</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t38">apps\config\views\multi_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t38"><data value='EmailConfigCreateView'>EmailConfigCreateView</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t84">apps\config\views\multi_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t84"><data value='EmailConfigUpdateView'>EmailConfigUpdateView</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t129">apps\config\views\multi_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t129"><data value='EmailConfigDeleteView'>EmailConfigDeleteView</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t158">apps\config\views\multi_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t158"><data value='EmailConfigTestView'>EmailConfigTestView</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t215">apps\config\views\multi_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t215"><data value='EmailConfigSetDefaultView'>EmailConfigSetDefaultView</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t245">apps\config\views\multi_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t245"><data value='DatabaseConfigListView'>DatabaseConfigListView</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t263">apps\config\views\multi_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t263"><data value='DatabaseConfigCreateView'>DatabaseConfigCreateView</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t308">apps\config\views\multi_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t308"><data value='DatabaseConfigUpdateView'>DatabaseConfigUpdateView</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t352">apps\config\views\multi_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t352"><data value='DatabaseConfigDeleteView'>DatabaseConfigDeleteView</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t382">apps\config\views\multi_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t382"><data value='DatabaseConfigSetDefaultView'>DatabaseConfigSetDefaultView</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t410">apps\config\views\multi_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html#t410"><data value='DatabaseConfigTestView'>DatabaseConfigTestView</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html">apps\config\views\multi_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>52</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="52 52">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_system_config_views_py.html#t11">apps\config\views\system_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_system_config_views_py.html#t11"><data value='SystemConfigView'>SystemConfigView</data></a></td>
                <td>53</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_system_config_views_py.html">apps\config\views\system_config_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_system_config_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_user_views_py.html#t16">apps\config\views\user_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_user_views_py.html#t16"><data value='UserListView'>UserListView</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_user_views_py.html#t63">apps\config\views\user_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_user_views_py.html#t63"><data value='UserCreateView'>UserCreateView</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_user_views_py.html#t120">apps\config\views\user_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_user_views_py.html#t120"><data value='UserDetailView'>UserDetailView</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_user_views_py.html#t139">apps\config\views\user_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_user_views_py.html#t139"><data value='UserUpdateView'>UserUpdateView</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_user_views_py.html#t196">apps\config\views\user_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_user_views_py.html#t196"><data value='UserDeleteView'>UserDeleteView</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_user_views_py.html">apps\config\views\user_views.py</a></td>
                <td class="name left"><a href="z_7862fbf3146bb8a1_user_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e775d4d1883b20c___init___py.html">apps\pages\__init__.py</a></td>
                <td class="name left"><a href="z_8e775d4d1883b20c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e775d4d1883b20c_admin_py.html#t6">apps\pages\admin.py</a></td>
                <td class="name left"><a href="z_8e775d4d1883b20c_admin_py.html#t6"><data value='PageAdmin'>PageAdmin</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e775d4d1883b20c_admin_py.html#t38">apps\pages\admin.py</a></td>
                <td class="name left"><a href="z_8e775d4d1883b20c_admin_py.html#t38"><data value='NavigationItemAdmin'>NavigationItemAdmin</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e775d4d1883b20c_admin_py.html#t46">apps\pages\admin.py</a></td>
                <td class="name left"><a href="z_8e775d4d1883b20c_admin_py.html#t46"><data value='SEOSettingsAdmin'>SEOSettingsAdmin</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e775d4d1883b20c_admin_py.html">apps\pages\admin.py</a></td>
                <td class="name left"><a href="z_8e775d4d1883b20c_admin_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e775d4d1883b20c_apps_py.html#t4">apps\pages\apps.py</a></td>
                <td class="name left"><a href="z_8e775d4d1883b20c_apps_py.html#t4"><data value='PagesConfig'>PagesConfig</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e775d4d1883b20c_apps_py.html">apps\pages\apps.py</a></td>
                <td class="name left"><a href="z_8e775d4d1883b20c_apps_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e337c540cc623d9___init___py.html">apps\pages\forms\__init__.py</a></td>
                <td class="name left"><a href="z_8e337c540cc623d9___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e337c540cc623d9_contact_form_py.html#t4">apps\pages\forms\contact_form.py</a></td>
                <td class="name left"><a href="z_8e337c540cc623d9_contact_form_py.html#t4"><data value='ContactForm'>ContactForm</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e337c540cc623d9_contact_form_py.html">apps\pages\forms\contact_form.py</a></td>
                <td class="name left"><a href="z_8e337c540cc623d9_contact_form_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e337c540cc623d9_contact_forms_py.html#t9">apps\pages\forms\contact_forms.py</a></td>
                <td class="name left"><a href="z_8e337c540cc623d9_contact_forms_py.html#t9"><data value='ContactForm'>ContactForm</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e337c540cc623d9_contact_forms_py.html#t145">apps\pages\forms\contact_forms.py</a></td>
                <td class="name left"><a href="z_8e337c540cc623d9_contact_forms_py.html#t145"><data value='NewsletterForm'>NewsletterForm</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e337c540cc623d9_contact_forms_py.html#t178">apps\pages\forms\contact_forms.py</a></td>
                <td class="name left"><a href="z_8e337c540cc623d9_contact_forms_py.html#t178"><data value='SearchForm'>SearchForm</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e337c540cc623d9_contact_forms_py.html">apps\pages\forms\contact_forms.py</a></td>
                <td class="name left"><a href="z_8e337c540cc623d9_contact_forms_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_73977dc7d293fed5___init___py.html">apps\pages\interfaces\__init__.py</a></td>
                <td class="name left"><a href="z_73977dc7d293fed5___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_73977dc7d293fed5_repositories_py.html#t5">apps\pages\interfaces\repositories.py</a></td>
                <td class="name left"><a href="z_73977dc7d293fed5_repositories_py.html#t5"><data value='IPageRepository'>IPageRepository</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_73977dc7d293fed5_repositories_py.html#t63">apps\pages\interfaces\repositories.py</a></td>
                <td class="name left"><a href="z_73977dc7d293fed5_repositories_py.html#t63"><data value='INavigationRepository'>INavigationRepository</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_73977dc7d293fed5_repositories_py.html#t96">apps\pages\interfaces\repositories.py</a></td>
                <td class="name left"><a href="z_73977dc7d293fed5_repositories_py.html#t96"><data value='ISEORepository'>ISEORepository</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_73977dc7d293fed5_repositories_py.html#t114">apps\pages\interfaces\repositories.py</a></td>
                <td class="name left"><a href="z_73977dc7d293fed5_repositories_py.html#t114"><data value='IAnalyticsRepository'>IAnalyticsRepository</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_73977dc7d293fed5_repositories_py.html">apps\pages\interfaces\repositories.py</a></td>
                <td class="name left"><a href="z_73977dc7d293fed5_repositories_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>55</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="55 55">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_73977dc7d293fed5_services_py.html#t8">apps\pages\interfaces\services.py</a></td>
                <td class="name left"><a href="z_73977dc7d293fed5_services_py.html#t8"><data value='IPageService'>IPageService</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_73977dc7d293fed5_services_py.html#t92">apps\pages\interfaces\services.py</a></td>
                <td class="name left"><a href="z_73977dc7d293fed5_services_py.html#t92"><data value='INavigationService'>INavigationService</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_73977dc7d293fed5_services_py.html#t130">apps\pages\interfaces\services.py</a></td>
                <td class="name left"><a href="z_73977dc7d293fed5_services_py.html#t130"><data value='ISEOService'>ISEOService</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_73977dc7d293fed5_services_py.html#t176">apps\pages\interfaces\services.py</a></td>
                <td class="name left"><a href="z_73977dc7d293fed5_services_py.html#t176"><data value='IAnalyticsService'>IAnalyticsService</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_73977dc7d293fed5_services_py.html">apps\pages\interfaces\services.py</a></td>
                <td class="name left"><a href="z_73977dc7d293fed5_services_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>51</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="51 51">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2481a92531cb87aa_0001_initial_py.html#t9">apps\pages\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_2481a92531cb87aa_0001_initial_py.html#t9"><data value='Migration'>Migration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2481a92531cb87aa_0001_initial_py.html">apps\pages\migrations\0001_initial.py</a></td>
                <td class="name left"><a href="z_2481a92531cb87aa_0001_initial_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2481a92531cb87aa___init___py.html">apps\pages\migrations\__init__.py</a></td>
                <td class="name left"><a href="z_2481a92531cb87aa___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_293efa6a78a0dee6___init___py.html">apps\pages\models\__init__.py</a></td>
                <td class="name left"><a href="z_293efa6a78a0dee6___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_293efa6a78a0dee6_navigation_py.html#t4">apps\pages\models\navigation.py</a></td>
                <td class="name left"><a href="z_293efa6a78a0dee6_navigation_py.html#t4"><data value='NavigationItem'>NavigationItem</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_293efa6a78a0dee6_navigation_py.html#t83">apps\pages\models\navigation.py</a></td>
                <td class="name left"><a href="z_293efa6a78a0dee6_navigation_py.html#t83"><data value='Meta'>NavigationItem.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_293efa6a78a0dee6_navigation_py.html">apps\pages\models\navigation.py</a></td>
                <td class="name left"><a href="z_293efa6a78a0dee6_navigation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_293efa6a78a0dee6_page_py.html#t9">apps\pages\models\page.py</a></td>
                <td class="name left"><a href="z_293efa6a78a0dee6_page_py.html#t9"><data value='Page'>Page</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_293efa6a78a0dee6_page_py.html#t144">apps\pages\models\page.py</a></td>
                <td class="name left"><a href="z_293efa6a78a0dee6_page_py.html#t144"><data value='Meta'>Page.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_293efa6a78a0dee6_page_py.html">apps\pages\models\page.py</a></td>
                <td class="name left"><a href="z_293efa6a78a0dee6_page_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="45 45">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_293efa6a78a0dee6_seo_py.html#t4">apps\pages\models\seo.py</a></td>
                <td class="name left"><a href="z_293efa6a78a0dee6_seo_py.html#t4"><data value='SEOSettings'>SEOSettings</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_293efa6a78a0dee6_seo_py.html#t131">apps\pages\models\seo.py</a></td>
                <td class="name left"><a href="z_293efa6a78a0dee6_seo_py.html#t131"><data value='Meta'>SEOSettings.Meta</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_293efa6a78a0dee6_seo_py.html">apps\pages\models\seo.py</a></td>
                <td class="name left"><a href="z_293efa6a78a0dee6_seo_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7837dd24d49bbe44___init___py.html">apps\pages\repositories\__init__.py</a></td>
                <td class="name left"><a href="z_7837dd24d49bbe44___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7837dd24d49bbe44_navigation_repository_py.html#t7">apps\pages\repositories\navigation_repository.py</a></td>
                <td class="name left"><a href="z_7837dd24d49bbe44_navigation_repository_py.html#t7"><data value='DjangoNavigationRepository'>DjangoNavigationRepository</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7837dd24d49bbe44_navigation_repository_py.html">apps\pages\repositories\navigation_repository.py</a></td>
                <td class="name left"><a href="z_7837dd24d49bbe44_navigation_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7837dd24d49bbe44_page_repository_py.html#t8">apps\pages\repositories\page_repository.py</a></td>
                <td class="name left"><a href="z_7837dd24d49bbe44_page_repository_py.html#t8"><data value='DjangoPageRepository'>DjangoPageRepository</data></a></td>
                <td>77</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="0 77">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7837dd24d49bbe44_page_repository_py.html">apps\pages\repositories\page_repository.py</a></td>
                <td class="name left"><a href="z_7837dd24d49bbe44_page_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7837dd24d49bbe44_seo_repository_py.html#t6">apps\pages\repositories\seo_repository.py</a></td>
                <td class="name left"><a href="z_7837dd24d49bbe44_seo_repository_py.html#t6"><data value='DjangoSEORepository'>DjangoSEORepository</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7837dd24d49bbe44_seo_repository_py.html">apps\pages\repositories\seo_repository.py</a></td>
                <td class="name left"><a href="z_7837dd24d49bbe44_seo_repository_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_368ff6a066478aeb___init___py.html">apps\pages\services\__init__.py</a></td>
                <td class="name left"><a href="z_368ff6a066478aeb___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_368ff6a066478aeb_navigation_service_py.html#t8">apps\pages\services\navigation_service.py</a></td>
                <td class="name left"><a href="z_368ff6a066478aeb_navigation_service_py.html#t8"><data value='NavigationService'>NavigationService</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_368ff6a066478aeb_navigation_service_py.html">apps\pages\services\navigation_service.py</a></td>
                <td class="name left"><a href="z_368ff6a066478aeb_navigation_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_368ff6a066478aeb_page_service_py.html#t13">apps\pages\services\page_service.py</a></td>
                <td class="name left"><a href="z_368ff6a066478aeb_page_service_py.html#t13"><data value='PageService'>PageService</data></a></td>
                <td>63</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="0 63">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_368ff6a066478aeb_page_service_py.html">apps\pages\services\page_service.py</a></td>
                <td class="name left"><a href="z_368ff6a066478aeb_page_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_368ff6a066478aeb_seo_service_py.html#t9">apps\pages\services\seo_service.py</a></td>
                <td class="name left"><a href="z_368ff6a066478aeb_seo_service_py.html#t9"><data value='SEOService'>SEOService</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_368ff6a066478aeb_seo_service_py.html">apps\pages\services\seo_service.py</a></td>
                <td class="name left"><a href="z_368ff6a066478aeb_seo_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e775d4d1883b20c_signals_py.html">apps\pages\signals.py</a></td>
                <td class="name left"><a href="z_8e775d4d1883b20c_signals_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="17 38">45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e775d4d1883b20c_tests_py.html">apps\pages\tests.py</a></td>
                <td class="name left"><a href="z_8e775d4d1883b20c_tests_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e775d4d1883b20c_urls_py.html">apps\pages\urls.py</a></td>
                <td class="name left"><a href="z_8e775d4d1883b20c_urls_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e775d4d1883b20c_views_py.html">apps\pages\views.py</a></td>
                <td class="name left"><a href="z_8e775d4d1883b20c_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9e5a0612d4355adf___init___py.html">apps\pages\views\__init__.py</a></td>
                <td class="name left"><a href="z_9e5a0612d4355adf___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9e5a0612d4355adf_home_py.html#t7">apps\pages\views\home.py</a></td>
                <td class="name left"><a href="z_9e5a0612d4355adf_home_py.html#t7"><data value='HomeView'>HomeView</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9e5a0612d4355adf_home_py.html">apps\pages\views\home.py</a></td>
                <td class="name left"><a href="z_9e5a0612d4355adf_home_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9e5a0612d4355adf_page_views_py.html#t9">apps\pages\views\page_views.py</a></td>
                <td class="name left"><a href="z_9e5a0612d4355adf_page_views_py.html#t9"><data value='PageDetailView'>PageDetailView</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9e5a0612d4355adf_page_views_py.html#t61">apps\pages\views\page_views.py</a></td>
                <td class="name left"><a href="z_9e5a0612d4355adf_page_views_py.html#t61"><data value='PageListView'>PageListView</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9e5a0612d4355adf_page_views_py.html#t99">apps\pages\views\page_views.py</a></td>
                <td class="name left"><a href="z_9e5a0612d4355adf_page_views_py.html#t99"><data value='PageSearchView'>PageSearchView</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9e5a0612d4355adf_page_views_py.html">apps\pages\views\page_views.py</a></td>
                <td class="name left"><a href="z_9e5a0612d4355adf_page_views_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9e5a0612d4355adf_static_pages_py.html#t8">apps\pages\views\static_pages.py</a></td>
                <td class="name left"><a href="z_9e5a0612d4355adf_static_pages_py.html#t8"><data value='AboutView'>AboutView</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9e5a0612d4355adf_static_pages_py.html#t21">apps\pages\views\static_pages.py</a></td>
                <td class="name left"><a href="z_9e5a0612d4355adf_static_pages_py.html#t21"><data value='ContactView'>ContactView</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9e5a0612d4355adf_static_pages_py.html#t81">apps\pages\views\static_pages.py</a></td>
                <td class="name left"><a href="z_9e5a0612d4355adf_static_pages_py.html#t81"><data value='PrivacyView'>PrivacyView</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9e5a0612d4355adf_static_pages_py.html#t94">apps\pages\views\static_pages.py</a></td>
                <td class="name left"><a href="z_9e5a0612d4355adf_static_pages_py.html#t94"><data value='TermsView'>TermsView</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9e5a0612d4355adf_static_pages_py.html">apps\pages\views\static_pages.py</a></td>
                <td class="name left"><a href="z_9e5a0612d4355adf_static_pages_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>6482</td>
                <td>3795</td>
                <td>0</td>
                <td class="right" data-ratio="2687 6482">41%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 21:05 -0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
