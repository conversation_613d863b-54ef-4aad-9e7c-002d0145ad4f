"""
Factory para criação de services com suas dependências
Implementa o padrão Factory para centralizar a criação de objetos
"""

from apps.accounts.services.auth_service import AuthService
from apps.accounts.services.registration_service import RegistrationService
from apps.accounts.services.password_service import PasswordService
from apps.accounts.repositories.user_repository import DjangoUserRepository
from apps.accounts.repositories.verification_repository import DjangoVerificationRepository
from apps.accounts.notifications.email_notification import EmailNotificationService


class AccountServiceFactory:
    """Factory para services do app accounts"""
    
    @staticmethod
    def create_auth_service() -> AuthService:
        """
        Cria uma instância do AuthService com suas dependências
        :return: AuthService configurado
        """
        user_repository = DjangoUserRepository()
        return AuthService(user_repository)
    
    @staticmethod
    def create_registration_service() -> RegistrationService:
        """
        Cria uma instância do RegistrationService com suas dependências
        :return: RegistrationService configurado
        """
        user_repository = DjangoUserRepository()
        verification_repository = DjangoVerificationRepository()
        notification_service = EmailNotificationService()
        
        return RegistrationService(
            user_repository=user_repository,
            verification_repository=verification_repository,
            notification_service=notification_service
        )
    
    @staticmethod
    def create_password_service() -> PasswordService:
        """
        Cria uma instância do PasswordService com suas dependências
        :return: PasswordService configurado
        """
        user_repository = DjangoUserRepository()
        verification_repository = DjangoVerificationRepository()
        notification_service = EmailNotificationService()
        
        return PasswordService(
            user_repository=user_repository,
            verification_repository=verification_repository,
            notification_service=notification_service
        )
