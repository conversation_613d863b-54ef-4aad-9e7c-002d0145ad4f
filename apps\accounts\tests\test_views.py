from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.contrib.messages import get_messages
from unittest.mock import patch, Mock
from apps.accounts.models.verification import VerificationCode
import json

User = get_user_model()


class RegistrationViewTest(TestCase):
    """Testes para RegistrationView"""

    def setUp(self):
        """Configuração inicial para os testes"""
        self.client = Client()
        self.registration_url = reverse('accounts:register')
        self.registration_data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'testpass123',
            'password_confirm': 'testpass123'
        }

    def test_get_registration_page(self):
        """Testa acesso à página de registro"""
        response = self.client.get(self.registration_url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<PERSON><PERSON><PERSON>')
        self.assertContains(response, 'form')

    @patch('apps.accounts.views.registration.RegistrationServiceMixin.get_registration_service')
    def test_post_registration_success(self, mock_get_service):
        """Testa registro bem-sucedido"""
        # Mock do service
        mock_service = Mock()
        mock_service.register_user.return_value = {
            'success': True,
            'user': Mock(email='<EMAIL>'),
            'error': None
        }
        mock_get_service.return_value = mock_service

        response = self.client.post(self.registration_url, self.registration_data)

        # Deve redirecionar para verificação
        self.assertEqual(response.status_code, 302)
        self.assertIn('verificacao', response.url)

    @patch('apps.accounts.views.registration.RegistrationServiceMixin.get_registration_service')
    def test_post_registration_service_error(self, mock_get_service):
        """Testa erro no service durante registro"""
        # Mock do service com erro
        mock_service = Mock()
        mock_service.register_user.return_value = {
            'success': False,
            'user': None,
            'error': 'Email já em uso'
        }
        mock_get_service.return_value = mock_service

        response = self.client.post(self.registration_url, self.registration_data)

        # Deve retornar à página com erro
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Email já em uso')

    def test_post_registration_invalid_form(self):
        """Testa registro com formulário inválido"""
        invalid_data = self.registration_data.copy()
        invalid_data['password_confirm'] = 'different_password'

        response = self.client.post(self.registration_url, invalid_data)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'erro')


class VerificationViewTest(TestCase):
    """Testes para VerificationView"""

    def setUp(self):
        """Configuração inicial para os testes"""
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123',
            is_verified=False
        )
        self.verification_url = reverse('accounts:verify_email')

    def test_get_verification_page(self):
        """Testa acesso à página de verificação"""
        # Adiciona email na sessão (simulando registro)
        session = self.client.session
        session['registration_email'] = self.user.email
        session.save()

        response = self.client.get(self.verification_url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Verificação')
        self.assertContains(response, self.user.email)

    def test_get_verification_page_no_email_in_session(self):
        """Testa acesso sem email na sessão"""
        response = self.client.get(self.verification_url)

        # Deve redirecionar para registro
        self.assertEqual(response.status_code, 302)
        self.assertIn('register', response.url)

    @patch('apps.accounts.views.registration.RegistrationServiceMixin.get_registration_service')
    def test_post_verification_success(self, mock_get_service):
        """Testa verificação bem-sucedida"""
        # Adiciona email na sessão
        session = self.client.session
        session['registration_email'] = self.user.email
        session.save()

        # Mock do service
        mock_service = Mock()
        mock_service.verify_email.return_value = {
            'success': True,
            'error': None
        }
        mock_get_service.return_value = mock_service

        response = self.client.post(self.verification_url, {'code': '123456'})

        # Deve redirecionar para login
        self.assertEqual(response.status_code, 302)
        self.assertIn('login', response.url)

    @patch('apps.accounts.views.registration.RegistrationServiceMixin.get_registration_service')
    def test_post_verification_invalid_code(self, mock_get_service):
        """Testa verificação com código inválido"""
        # Adiciona email na sessão
        session = self.client.session
        session['registration_email'] = self.user.email
        session.save()

        # Mock do service com erro
        mock_service = Mock()
        mock_service.verify_email.return_value = {
            'success': False,
            'error': 'Código inválido'
        }
        mock_get_service.return_value = mock_service

        response = self.client.post(self.verification_url, {'code': '999999'})

        # Deve retornar à página com erro
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Código inválido')


class LoginViewTest(TestCase):
    """Testes para LoginView"""

    def setUp(self):
        """Configuração inicial para os testes"""
        self.client = Client()
        self.login_url = reverse('accounts:login')
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123',
            is_verified=True
        )

    def test_get_login_page(self):
        """Testa acesso à página de login"""
        response = self.client.get(self.login_url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Entrar')
        self.assertContains(response, 'form')

    def test_post_login_success(self):
        """Testa login bem-sucedido"""
        response = self.client.post(self.login_url, {
            'email': '<EMAIL>',
            'password': 'testpass123'
        })

        # Deve redirecionar após login
        self.assertEqual(response.status_code, 302)

        # Verifica se usuário está logado
        user = response.wsgi_request.user
        self.assertTrue(user.is_authenticated)

    def test_post_login_invalid_credentials(self):
        """Testa login com credenciais inválidas"""
        response = self.client.post(self.login_url, {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        })

        # Deve retornar à página de login
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'erro')

    def test_post_login_unverified_user(self):
        """Testa login com usuário não verificado"""
        self.user.is_verified = False
        self.user.save()

        response = self.client.post(self.login_url, {
            'email': '<EMAIL>',
            'password': 'testpass123'
        })

        # Deve retornar à página de login com erro
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'verificada')


class LogoutViewTest(TestCase):
    """Testes para LogoutView"""

    def setUp(self):
        """Configuração inicial para os testes"""
        self.client = Client()
        self.logout_url = reverse('accounts:logout')
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123',
            is_verified=True
        )

    def test_logout(self):
        """Testa logout"""
        # Faz login primeiro
        self.client.login(email='<EMAIL>', password='testpass123')

        response = self.client.post(self.logout_url)

        # Deve redirecionar
        self.assertEqual(response.status_code, 302)

        # Verifica se usuário foi deslogado
        response = self.client.get('/')
        user = response.wsgi_request.user
        self.assertFalse(user.is_authenticated)


class ProfileViewTest(TestCase):
    """Testes para views de perfil"""

    def setUp(self):
        """Configuração inicial para os testes"""
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123',
            is_verified=True
        )
        self.profile_url = reverse('accounts:profile')
        self.settings_url = reverse('accounts:settings')

    def test_profile_view_authenticated(self):
        """Testa acesso ao perfil com usuário autenticado"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.profile_url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.user.email)
        self.assertContains(response, 'Perfil')

    def test_profile_view_anonymous(self):
        """Testa redirecionamento para login quando não autenticado"""
        response = self.client.get(self.profile_url)

        self.assertEqual(response.status_code, 302)
        self.assertIn('login', response.url)

    def test_settings_view_authenticated(self):
        """Testa acesso às configurações com usuário autenticado"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.settings_url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Configurações')

    def test_settings_update_valid_data(self):
        """Testa atualização de configurações com dados válidos"""
        self.client.login(email='<EMAIL>', password='testpass123')

        response = self.client.post(self.settings_url, {
            'first_name': 'Updated',
            'last_name': 'Name',
            'bio': 'Updated bio'
        })

        # Deve redirecionar após sucesso
        self.assertEqual(response.status_code, 302)

        # Verifica se dados foram atualizados
        self.user.refresh_from_db()
        self.assertEqual(self.user.first_name, 'Updated')
        self.assertEqual(self.user.last_name, 'Name')

    def test_settings_update_invalid_data(self):
        """Testa atualização com dados inválidos"""
        self.client.login(email='<EMAIL>', password='testpass123')

        response = self.client.post(self.settings_url, {
            'first_name': 'A' * 200,  # Nome muito longo
            'last_name': 'Name'
        })

        # Deve retornar à página com erro
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'erro')


class PasswordResetViewTest(TestCase):
    """Testes para views de reset de senha"""

    def setUp(self):
        """Configuração inicial para os testes"""
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123',
            is_verified=True
        )
        self.reset_url = reverse('accounts:password_reset')

    def test_password_reset_request_get(self):
        """Testa acesso à página de solicitação de reset"""
        response = self.client.get(self.reset_url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Recuperar Senha')
        self.assertContains(response, 'form')

    def test_password_reset_request_valid_email(self):
        """Testa solicitação de reset com email válido"""
        response = self.client.post(self.reset_url, {
            'email': '<EMAIL>'
        })

        # Deve redirecionar para página de confirmação
        self.assertEqual(response.status_code, 302)

    def test_password_reset_request_invalid_email(self):
        """Testa solicitação de reset com email inválido"""
        response = self.client.post(self.reset_url, {
            'email': '<EMAIL>'
        })

        # Deve retornar à página com erro
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'erro')

    def test_password_reset_request_unverified_user(self):
        """Testa solicitação de reset para usuário não verificado"""
        self.user.is_verified = False
        self.user.save()

        response = self.client.post(self.reset_url, {
            'email': '<EMAIL>'
        })

        # Deve retornar erro informando que usuário não está verificado
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'verificado')


class EmailTestViewTest(TestCase):
    """Testes para EmailTestView"""

    def setUp(self):
        """Configuração inicial para os testes"""
        self.client = Client()
        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            username='staffuser',
            password='testpass123',
            is_verified=True,
            is_staff=True
        )
        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            username='regularuser',
            password='testpass123',
            is_verified=True
        )
        self.email_test_url = reverse('accounts:email_test')

    def test_email_test_access_staff_user(self):
        """Testa acesso com usuário staff"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.email_test_url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Teste de Email')

    def test_email_test_access_regular_user(self):
        """Testa acesso negado para usuário regular"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.email_test_url)

        # Deve retornar 403 ou redirecionar
        self.assertIn(response.status_code, [403, 302])

    def test_email_test_access_anonymous(self):
        """Testa acesso negado para usuário anônimo"""
        response = self.client.get(self.email_test_url)

        # Deve redirecionar para login
        self.assertEqual(response.status_code, 302)
        self.assertIn('login', response.url)

    @patch('apps.accounts.services.email_service.EmailService.send_test_email')
    def test_send_test_email_success(self, mock_send):
        """Testa envio de email de teste bem-sucedido"""
        mock_send.return_value = True

        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.post(self.email_test_url, {
            'email_type': 'registration',
            'recipient_email': '<EMAIL>'
        })

        # Deve retornar sucesso
        self.assertEqual(response.status_code, 200)
        mock_send.assert_called_once()

    @patch('apps.accounts.services.email_service.EmailService.send_test_email')
    def test_send_test_email_failure(self, mock_send):
        """Testa falha no envio de email de teste"""
        mock_send.return_value = False

        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.post(self.email_test_url, {
            'email_type': 'registration',
            'recipient_email': '<EMAIL>'
        })

        # Deve retornar erro
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'erro')


class AvatarUploadViewTest(TestCase):
    """Testes para upload de avatar"""

    def setUp(self):
        """Configuração inicial para os testes"""
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123',
            is_verified=True
        )
        self.avatar_url = reverse('accounts:avatar_upload')

    def test_avatar_upload_authenticated(self):
        """Testa acesso à página de upload com usuário autenticado"""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.avatar_url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Avatar')

    def test_avatar_upload_anonymous(self):
        """Testa redirecionamento para login quando não autenticado"""
        response = self.client.get(self.avatar_url)

        self.assertEqual(response.status_code, 302)
        self.assertIn('login', response.url)

    @patch('apps.accounts.services.image_service.ImageService.process_avatar')
    def test_avatar_upload_valid_image(self, mock_process):
        """Testa upload de imagem válida"""
        from django.core.files.uploadedfile import SimpleUploadedFile
        from PIL import Image
        import io

        # Cria uma imagem de teste
        img = Image.new('RGB', (100, 100), color='red')
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='JPEG')
        img_buffer.seek(0)

        uploaded_file = SimpleUploadedFile(
            'test.jpg',
            img_buffer.getvalue(),
            content_type='image/jpeg'
        )

        mock_process.return_value = 'path/to/avatar.jpg'

        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.post(self.avatar_url, {
            'avatar': uploaded_file
        })

        # Deve redirecionar após sucesso
        self.assertEqual(response.status_code, 302)
        mock_process.assert_called_once()

    def test_avatar_upload_invalid_file(self):
        """Testa upload de arquivo inválido"""
        from django.core.files.uploadedfile import SimpleUploadedFile

        text_file = SimpleUploadedFile(
            'test.txt',
            b'This is not an image',
            content_type='text/plain'
        )

        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.post(self.avatar_url, {
            'avatar': text_file
        })

        # Deve retornar erro
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'erro')


class PasswordChangeViewTest(TestCase):
    """Testes para mudança de senha"""

    def setUp(self):
        """Configuração inicial para os testes"""
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='oldpass123',
            is_verified=True
        )
        self.password_change_url = reverse('accounts:password_change')

    def test_password_change_authenticated(self):
        """Testa acesso à página de mudança de senha"""
        self.client.login(email='<EMAIL>', password='oldpass123')
        response = self.client.get(self.password_change_url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Alterar Senha')

    def test_password_change_anonymous(self):
        """Testa redirecionamento para login quando não autenticado"""
        response = self.client.get(self.password_change_url)

        self.assertEqual(response.status_code, 302)
        self.assertIn('login', response.url)

    def test_password_change_valid_data(self):
        """Testa mudança de senha com dados válidos"""
        self.client.login(email='<EMAIL>', password='oldpass123')

        response = self.client.post(self.password_change_url, {
            'old_password': 'oldpass123',
            'new_password1': 'newpass123',
            'new_password2': 'newpass123'
        })

        # Deve redirecionar após sucesso
        self.assertEqual(response.status_code, 302)

    def test_password_change_wrong_old_password(self):
        """Testa mudança de senha com senha antiga incorreta"""
        self.client.login(email='<EMAIL>', password='oldpass123')

        response = self.client.post(self.password_change_url, {
            'old_password': 'wrongpass',
            'new_password1': 'newpass123',
            'new_password2': 'newpass123'
        })

        # Deve retornar erro
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'erro')

    def test_password_change_mismatch(self):
        """Testa mudança de senha com senhas que não coincidem"""
        self.client.login(email='<EMAIL>', password='oldpass123')

        response = self.client.post(self.password_change_url, {
            'old_password': 'oldpass123',
            'new_password1': 'newpass123',
            'new_password2': 'differentpass'
        })

        # Deve retornar erro
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'erro')