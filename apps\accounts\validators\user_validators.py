"""
Validators para dados de usuário
Implementa o padrão Strategy para validações
"""

from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from typing import Optional
import re

User = get_user_model()


class UserEmailValidator:
    """Validator para email de usuário"""
    
    @staticmethod
    def validate_email_not_in_use(email: str, exclude_user_id: Optional[int] = None) -> None:
        """
        Valida se o email não está em uso por usuário verificado
        :param email: Email a ser validado
        :param exclude_user_id: ID do usuário a ser excluído da validação
        :raises ValidationError: Se email já estiver em uso
        """
        query = User.objects.filter(email__iexact=email, is_verified=True)
        
        if exclude_user_id:
            query = query.exclude(id=exclude_user_id)
        
        if query.exists():
            raise ValidationError('Já existe um usuário verificado com este e-mail.')
    
    @staticmethod
    def validate_email_format(email: str) -> None:
        """
        Valida formato do email
        :param email: Email a ser validado
        :raises ValidationError: Se formato for inválido
        """
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            raise ValidationError('Formato de email inválido.')


class UserUsernameValidator:
    """Validator para username de usuário"""
    
    @staticmethod
    def validate_username_not_in_use(username: str, exclude_user_id: Optional[int] = None) -> None:
        """
        Valida se o username não está em uso
        :param username: Username a ser validado
        :param exclude_user_id: ID do usuário a ser excluído da validação
        :raises ValidationError: Se username já estiver em uso
        """
        query = User.objects.filter(username__iexact=username)
        
        if exclude_user_id:
            query = query.exclude(id=exclude_user_id)
        
        if query.exists():
            raise ValidationError('Este nome de usuário já está em uso.')
    
    @staticmethod
    def validate_username_format(username: str) -> None:
        """
        Valida formato do username
        :param username: Username a ser validado
        :raises ValidationError: Se formato for inválido
        """
        # Username deve ter entre 3 e 150 caracteres, apenas letras, números e _
        if len(username) < 3:
            raise ValidationError('Nome de usuário deve ter pelo menos 3 caracteres.')
        
        if len(username) > 150:
            raise ValidationError('Nome de usuário deve ter no máximo 150 caracteres.')
        
        username_pattern = r'^[a-zA-Z0-9_]+$'
        if not re.match(username_pattern, username):
            raise ValidationError('Nome de usuário deve conter apenas letras, números e underscore.')


class UserPasswordValidator:
    """Validator para senha de usuário"""
    
    @staticmethod
    def validate_password_strength(password: str) -> None:
        """
        Valida força da senha
        :param password: Senha a ser validada
        :raises ValidationError: Se senha for fraca
        """
        if len(password) < 8:
            raise ValidationError('Senha deve ter pelo menos 8 caracteres.')
        
        # Verifica se tem pelo menos uma letra
        if not re.search(r'[a-zA-Z]', password):
            raise ValidationError('Senha deve conter pelo menos uma letra.')
        
        # Verifica se tem pelo menos um número
        if not re.search(r'\d', password):
            raise ValidationError('Senha deve conter pelo menos um número.')
    
    @staticmethod
    def validate_password_confirmation(password: str, password_confirmation: str) -> None:
        """
        Valida se as senhas coincidem
        :param password: Senha
        :param password_confirmation: Confirmação da senha
        :raises ValidationError: Se senhas não coincidirem
        """
        if password != password_confirmation:
            raise ValidationError('As senhas não coincidem.')


class UserDataValidator:
    """Validator principal para dados de usuário"""
    
    def __init__(self):
        self.email_validator = UserEmailValidator()
        self.username_validator = UserUsernameValidator()
        self.password_validator = UserPasswordValidator()
    
    def validate_registration_data(self, email: str, username: str, password: str, password_confirmation: str) -> None:
        """
        Valida dados de registro
        :param email: Email
        :param username: Username
        :param password: Senha
        :param password_confirmation: Confirmação da senha
        :raises ValidationError: Se algum dado for inválido
        """
        self.email_validator.validate_email_format(email)
        self.email_validator.validate_email_not_in_use(email)
        
        self.username_validator.validate_username_format(username)
        self.username_validator.validate_username_not_in_use(username)
        
        self.password_validator.validate_password_strength(password)
        self.password_validator.validate_password_confirmation(password, password_confirmation)
    
    def validate_update_data(self, user_id: int, email: str = None, username: str = None) -> None:
        """
        Valida dados de atualização
        :param user_id: ID do usuário sendo atualizado
        :param email: Email (opcional)
        :param username: Username (opcional)
        :raises ValidationError: Se algum dado for inválido
        """
        if email:
            self.email_validator.validate_email_format(email)
            self.email_validator.validate_email_not_in_use(email, exclude_user_id=user_id)
        
        if username:
            self.username_validator.validate_username_format(username)
            self.username_validator.validate_username_not_in_use(username, exclude_user_id=user_id)
