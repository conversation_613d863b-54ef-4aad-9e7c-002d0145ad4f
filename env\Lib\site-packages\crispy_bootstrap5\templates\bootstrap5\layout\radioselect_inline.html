{% if field.is_hidden %}
    {{ field }}
{% else %}
    <div id="div_{{ field.auto_id }}" class="mb-3{% if 'form-horizontal' in form_class %} row{% endif %}{% if wrapper_class %} {{ wrapper_class }}{% endif %}{% if field.css_classes %} {{ field.css_classes }}{% endif %}">

        {% if field.label %}
            <fieldset{% if field.aria_describedby %} aria-describedby="{{ field.aria_describedby }}"{% endif %}{% if 'form-horizontal' in form_class %} class="row"{% endif %}>
            <legend for="{{ field.id_for_label }}"  class="{{ label_class }}{% if 'form-horizontal' in form_class %} col-form-label pt-0{% else %} form-label{% endif %}{% if field.field.required %} requiredField{% endif %}">
                {{ field.label }}{% if field.field.required %}<span class="asteriskField">*</span>{% endif %}
            </legend>
        {% endif %}

        {% include 'bootstrap5/layout/radio_checkbox_select.html' %}
        {% if field.label %}</fieldset>{% endif %}
    </div>
{% endif %}
