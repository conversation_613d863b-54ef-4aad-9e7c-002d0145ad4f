{% extends "base.html" %}
{% load static %}

{% block title %}Deletar Artigo - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
.delete-container {
    max-width: 600px;
    margin: 3rem auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.delete-header {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    padding: 2rem;
    text-align: center;
}

.delete-content {
    padding: 2rem;
}

.article-preview {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    border-left: 4px solid #dc3545;
}

.article-meta {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.warning-box {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 1rem;
    margin: 1.5rem 0;
}

.warning-icon {
    color: #856404;
    font-size: 1.2rem;
    margin-right: 0.5rem;
}

.delete-actions {
    background: #f8f9fa;
    padding: 1.5rem 2rem;
    text-align: center;
    border-top: 1px solid #dee2e6;
}

.btn-delete {
    background: linear-gradient(45deg, #dc3545, #c82333);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    margin-right: 1rem;
    transition: transform 0.2s ease;
}

.btn-delete:hover {
    transform: translateY(-2px);
    color: white;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: transform 0.2s ease;
}

.btn-cancel:hover {
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.confirmation-checkbox {
    margin: 1.5rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #dee2e6;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #dc3545;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

@media (max-width: 768px) {
    .delete-container {
        margin: 1rem;
        border-radius: 10px;
    }
    
    .delete-header,
    .delete-content,
    .delete-actions {
        padding: 1.5rem;
    }
    
    .btn-delete,
    .btn-cancel {
        display: block;
        width: 100%;
        margin: 0.5rem 0;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="delete-container">
        <!-- Header -->
        <div class="delete-header">
            <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
            <h2>Confirmar Exclusão</h2>
            <p class="mb-0">Esta ação não pode ser desfeita</p>
        </div>

        <!-- Conteúdo -->
        <div class="delete-content">
            <p class="lead text-center">
                Tem certeza de que deseja deletar este artigo?
            </p>

            <!-- Preview do Artigo -->
            <div class="article-preview">
                <div class="article-meta">
                    <i class="fas fa-calendar me-1"></i>{{ object.created_at|date:"d/m/Y H:i" }}
                    <span class="mx-2">•</span>
                    <i class="fas fa-user me-1"></i>{{ object.author.get_full_name|default:object.author.username }}
                    {% if object.category %}
                    <span class="mx-2">•</span>
                    <i class="fas fa-folder me-1"></i>{{ object.category.name }}
                    {% endif %}
                </div>
                
                <h4 class="mb-2">{{ object.title }}</h4>
                
                {% if object.excerpt %}
                <p class="text-muted mb-2">{{ object.excerpt|truncatewords:30 }}</p>
                {% endif %}

                <!-- Tags -->
                {% if object.tags.all %}
                <div class="mb-2">
                    {% for tag in object.tags.all %}
                    <span class="badge bg-secondary me-1">{{ tag.name }}</span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <!-- Estatísticas -->
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">{{ object.views|default:0 }}</div>
                    <div class="stat-label">Visualizações</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ object.comments.count|default:0 }}</div>
                    <div class="stat-label">Comentários</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ object.content|wordcount|default:0 }}</div>
                    <div class="stat-label">Palavras</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">
                        {% if object.status == 'published' %}
                            <i class="fas fa-eye text-success"></i>
                        {% else %}
                            <i class="fas fa-eye-slash text-warning"></i>
                        {% endif %}
                    </div>
                    <div class="stat-label">{{ object.get_status_display }}</div>
                </div>
            </div>

            <!-- Aviso -->
            <div class="warning-box">
                <i class="fas fa-exclamation-triangle warning-icon"></i>
                <strong>Atenção:</strong> Ao deletar este artigo, você também perderá:
                <ul class="mb-0 mt-2">
                    <li>Todos os comentários associados</li>
                    <li>Estatísticas de visualização</li>
                    <li>Histórico de edições</li>
                    <li>Links externos que apontam para este artigo</li>
                </ul>
            </div>

            <!-- Confirmação -->
            <form method="post" id="deleteForm">
                {% csrf_token %}
                
                <div class="confirmation-checkbox">
                    <div class="form-check">
                        <input class="form-check-input" 
                               type="checkbox" 
                               id="confirmDelete" 
                               required>
                        <label class="form-check-label" for="confirmDelete">
                            <strong>Eu entendo que esta ação é irreversível e confirmo que desejo deletar este artigo.</strong>
                        </label>
                    </div>
                </div>

                <!-- Campo de confirmação adicional -->
                <div class="mb-3">
                    <label for="confirmTitle" class="form-label">
                        Para confirmar, digite o título do artigo:
                    </label>
                    <input type="text" 
                           class="form-control" 
                           id="confirmTitle" 
                           placeholder="Digite: {{ object.title }}"
                           required>
                    <div class="form-text">
                        Digite exatamente: <strong>{{ object.title }}</strong>
                    </div>
                </div>
            </form>
        </div>

        <!-- Ações -->
        <div class="delete-actions">
            <button type="submit" 
                    form="deleteForm" 
                    class="btn btn-delete" 
                    id="deleteBtn" 
                    disabled>
                <i class="fas fa-trash me-1"></i>Sim, Deletar Artigo
            </button>
            <a href="{% url 'articles:detail' object.slug %}" class="btn btn-cancel">
                <i class="fas fa-arrow-left me-1"></i>Cancelar
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmCheckbox = document.getElementById('confirmDelete');
    const confirmTitle = document.getElementById('confirmTitle');
    const deleteBtn = document.getElementById('deleteBtn');
    const deleteForm = document.getElementById('deleteForm');
    const expectedTitle = "{{ object.title }}";
    
    function checkFormValidity() {
        const isChecked = confirmCheckbox.checked;
        const titleMatches = confirmTitle.value.trim() === expectedTitle;
        
        deleteBtn.disabled = !(isChecked && titleMatches);
        
        if (titleMatches) {
            confirmTitle.classList.remove('is-invalid');
            confirmTitle.classList.add('is-valid');
        } else if (confirmTitle.value.trim() !== '') {
            confirmTitle.classList.remove('is-valid');
            confirmTitle.classList.add('is-invalid');
        } else {
            confirmTitle.classList.remove('is-valid', 'is-invalid');
        }
    }
    
    confirmCheckbox.addEventListener('change', checkFormValidity);
    confirmTitle.addEventListener('input', checkFormValidity);
    
    // Confirmação final antes do envio
    deleteForm.addEventListener('submit', function(e) {
        if (!confirm('ÚLTIMA CONFIRMAÇÃO: Tem absoluta certeza de que deseja deletar este artigo? Esta ação NÃO pode ser desfeita.')) {
            e.preventDefault();
        } else {
            // Desabilita o botão para evitar duplo clique
            deleteBtn.disabled = true;
            deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Deletando...';
        }
    });
    
    // Animação de entrada
    const container = document.querySelector('.delete-container');
    container.style.opacity = '0';
    container.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
        container.style.transition = 'all 0.5s ease';
        container.style.opacity = '1';
        container.style.transform = 'translateY(0)';
    }, 100);
    
    // Foco automático no checkbox
    setTimeout(() => {
        confirmCheckbox.focus();
    }, 500);
});
</script>
{% endblock %}
