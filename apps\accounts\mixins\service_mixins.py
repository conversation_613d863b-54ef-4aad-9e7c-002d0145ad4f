"""
Mixins para views que precisam de services
Implementa o padrão Mixin para reutilização de código
"""

from apps.accounts.factories.service_factory import AccountServiceFactory


class AuthServiceMixin:
    """Mixin que fornece AuthService para views"""
    
    def get_auth_service(self):
        """
        Obtém uma instância do AuthService
        :return: AuthService configurado
        """
        if not hasattr(self, '_auth_service'):
            self._auth_service = AccountServiceFactory.create_auth_service()
        return self._auth_service


class RegistrationServiceMixin:
    """Mixin que fornece RegistrationService para views"""
    
    def get_registration_service(self):
        """
        Obtém uma instância do RegistrationService
        :return: RegistrationService configurado
        """
        if not hasattr(self, '_registration_service'):
            self._registration_service = AccountServiceFactory.create_registration_service()
        return self._registration_service


class PasswordServiceMixin:
    """Mixin que fornece PasswordService para views"""
    
    def get_password_service(self):
        """
        Obtém uma instância do PasswordService
        :return: PasswordService configurado
        """
        if not hasattr(self, '_password_service'):
            self._password_service = AccountServiceFactory.create_password_service()
        return self._password_service


class AccountServicesMixin(AuthServiceMixin, RegistrationServiceMixin, PasswordServiceMixin):
    """Mixin que fornece todos os services de accounts"""
    pass
