{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.2", "globals": "aec070e29b11e8e60f61905f4f619252", "files": {"z_0daf9d4b3925bce1___init___py": {"hash": "7d6aaf5403688c932c3480f6e6c289a2", "index": {"url": "z_0daf9d4b3925bce1___init___py.html", "file": "apps\\accounts\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0daf9d4b3925bce1_admin_py": {"hash": "7d07d4ab149d1f06c9aaee9ba2984b20", "index": {"url": "z_0daf9d4b3925bce1_admin_py.html", "file": "apps\\accounts\\admin.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 16, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0daf9d4b3925bce1_apps_py": {"hash": "9d4d76fa0db6b718447db3682b0a05ca", "index": {"url": "z_0daf9d4b3925bce1_apps_py.html", "file": "apps\\accounts\\apps.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_96c8905fe34cb2c1___init___py": {"hash": "1cffb8f49ae5dbb1de4b16d3b25cedfb", "index": {"url": "z_96c8905fe34cb2c1___init___py.html", "file": "apps\\accounts\\factories\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_96c8905fe34cb2c1_service_factory_py": {"hash": "e20af9ab3f347c293221b3ae614a5e01", "index": {"url": "z_96c8905fe34cb2c1_service_factory_py.html", "file": "apps\\accounts\\factories\\service_factory.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 23, "n_excluded": 0, "n_missing": 10, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9f13b1157adba62e___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_9f13b1157adba62e___init___py.html", "file": "apps\\accounts\\forms\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9f13b1157adba62e_authentication_py": {"hash": "9d7a17b6631a0f835cef7c23baab2d15", "index": {"url": "z_9f13b1157adba62e_authentication_py.html", "file": "apps\\accounts\\forms\\authentication.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 62, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9f13b1157adba62e_password_reset_py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_9f13b1157adba62e_password_reset_py.html", "file": "apps\\accounts\\forms\\password_reset.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9f13b1157adba62e_profile_forms_py": {"hash": "331d261d10eb8bb3fe7e60b42646115f", "index": {"url": "z_9f13b1157adba62e_profile_forms_py.html", "file": "apps\\accounts\\forms\\profile_forms.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 190, "n_excluded": 0, "n_missing": 145, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9f13b1157adba62e_registration_py": {"hash": "ec56bf4a0c451c6ce9e534664d80820e", "index": {"url": "z_9f13b1157adba62e_registration_py.html", "file": "apps\\accounts\\forms\\registration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 52, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ee7bd68557b5fff3___init___py": {"hash": "64f940fa1969184859bd1330434ea2c6", "index": {"url": "z_ee7bd68557b5fff3___init___py.html", "file": "apps\\accounts\\handlers\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ee7bd68557b5fff3_user_handlers_py": {"hash": "4498f4d3a5b09409b29e2563c5db017f", "index": {"url": "z_ee7bd68557b5fff3_user_handlers_py.html", "file": "apps\\accounts\\handlers\\user_handlers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7190ae31b87f647e___init___py": {"hash": "7f6b4134057e1e3459f73559b0fb82a2", "index": {"url": "z_7190ae31b87f647e___init___py.html", "file": "apps\\accounts\\interfaces\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7190ae31b87f647e_notifications_py": {"hash": "73b9c38233d4c3100314d70b2d4a3f3e", "index": {"url": "z_7190ae31b87f647e_notifications_py.html", "file": "apps\\accounts\\interfaces\\notifications.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7190ae31b87f647e_repositories_py": {"hash": "0bf7d6539ddeeb9b7270b2ebf3adda04", "index": {"url": "z_7190ae31b87f647e_repositories_py.html", "file": "apps\\accounts\\interfaces\\repositories.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 27, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7190ae31b87f647e_services_py": {"hash": "5ece7eaeb8f1a10e056f3ab4151bae8a", "index": {"url": "z_7190ae31b87f647e_services_py.html", "file": "apps\\accounts\\interfaces\\services.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 22, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e66b20f0cc2b9be7___init___py": {"hash": "a637503eda7162b7220aa66880bf1c0d", "index": {"url": "z_e66b20f0cc2b9be7___init___py.html", "file": "apps\\accounts\\management\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7a12aab24e24085f___init___py": {"hash": "87aea171533b8858c5d1fd2092b9f659", "index": {"url": "z_7a12aab24e24085f___init___py.html", "file": "apps\\accounts\\management\\commands\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7a12aab24e24085f_create_test_users_py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_7a12aab24e24085f_create_test_users_py.html", "file": "apps\\accounts\\management\\commands\\create_test_users.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7a12aab24e24085f_test_email_py": {"hash": "63b26f7ace42de0c1177a8f63be369a0", "index": {"url": "z_7a12aab24e24085f_test_email_py.html", "file": "apps\\accounts\\management\\commands\\test_email.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 84, "n_excluded": 0, "n_missing": 84, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0daf9d4b3925bce1_middleware_py": {"hash": "e4fe177c0315ec9143dcaffd5d5d5620", "index": {"url": "z_0daf9d4b3925bce1_middleware_py.html", "file": "apps\\accounts\\middleware.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 118, "n_excluded": 0, "n_missing": 84, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8e835377efbddd70_0001_initial_py": {"hash": "dfda1e08918eb44ec905e5d6141bb9b2", "index": {"url": "z_8e835377efbddd70_0001_initial_py.html", "file": "apps\\accounts\\migrations\\0001_initial.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8e835377efbddd70_0002_user_avatar_user_bio_user_birth_date_user_location_and_more_py": {"hash": "2497195250486527a06f2984d2e27268", "index": {"url": "z_8e835377efbddd70_0002_user_avatar_user_bio_user_birth_date_user_location_and_more_py.html", "file": "apps\\accounts\\migrations\\0002_user_avatar_user_bio_user_birth_date_user_location_and_more.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8e835377efbddd70___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_8e835377efbddd70___init___py.html", "file": "apps\\accounts\\migrations\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_880a307ae6abc71f___init___py": {"hash": "60c7c0cf363f42c787cb44d0b418c30c", "index": {"url": "z_880a307ae6abc71f___init___py.html", "file": "apps\\accounts\\mixins\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_880a307ae6abc71f_service_mixins_py": {"hash": "355f8deaea1b774211e59ddd19790d85", "index": {"url": "z_880a307ae6abc71f_service_mixins_py.html", "file": "apps\\accounts\\mixins\\service_mixins.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 18, "n_excluded": 0, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_424a1052f51881f8___init___py": {"hash": "c14260d012a873a928c00c4998749a6e", "index": {"url": "z_424a1052f51881f8___init___py.html", "file": "apps\\accounts\\models\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_424a1052f51881f8_user_py": {"hash": "abbb4efc9aa7dbc811a9fe565f6be566", "index": {"url": "z_424a1052f51881f8_user_py.html", "file": "apps\\accounts\\models\\user.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 73, "n_excluded": 0, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_424a1052f51881f8_verification_py": {"hash": "50e44f6cffb966e138e9b8dff9ea6eff", "index": {"url": "z_424a1052f51881f8_verification_py.html", "file": "apps\\accounts\\models\\verification.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 28, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6b96ac88d8d43145___init___py": {"hash": "bcf6edb400e4e6129b03d10f1bfe678c", "index": {"url": "z_6b96ac88d8d43145___init___py.html", "file": "apps\\accounts\\notifications\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6b96ac88d8d43145_email_notification_py": {"hash": "078373fb7a55eac8d92b378269f4cb32", "index": {"url": "z_6b96ac88d8d43145_email_notification_py.html", "file": "apps\\accounts\\notifications\\email_notification.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 32, "n_excluded": 0, "n_missing": 23, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3b9b6fc10c689f92___init___py": {"hash": "7a8592bcadaf58693114023bc069ad66", "index": {"url": "z_3b9b6fc10c689f92___init___py.html", "file": "apps\\accounts\\repositories\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3b9b6fc10c689f92_user_repository_py": {"hash": "f83623003f2111e7df58591e2a36e034", "index": {"url": "z_3b9b6fc10c689f92_user_repository_py.html", "file": "apps\\accounts\\repositories\\user_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3b9b6fc10c689f92_verification_repository_py": {"hash": "2b0541a30edf2c9d66ddf5896c9bc118", "index": {"url": "z_3b9b6fc10c689f92_verification_repository_py.html", "file": "apps\\accounts\\repositories\\verification_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f42f61afc382a2f___init___py": {"hash": "f0187df64e15e869ea282aa9b66b5114", "index": {"url": "z_8f42f61afc382a2f___init___py.html", "file": "apps\\accounts\\services\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f42f61afc382a2f_auth_service_py": {"hash": "520e67a6ef515f79b7aa301132e7dba0", "index": {"url": "z_8f42f61afc382a2f_auth_service_py.html", "file": "apps\\accounts\\services\\auth_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 13, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f42f61afc382a2f_email_service_py": {"hash": "ee3345a323cb41c56325f086e7b1da28", "index": {"url": "z_8f42f61afc382a2f_email_service_py.html", "file": "apps\\accounts\\services\\email_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 122, "n_excluded": 0, "n_missing": 104, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f42f61afc382a2f_image_service_py": {"hash": "a726bf1cf4003a819cba8dc4062f9f66", "index": {"url": "z_8f42f61afc382a2f_image_service_py.html", "file": "apps\\accounts\\services\\image_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 66, "n_excluded": 0, "n_missing": 29, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f42f61afc382a2f_password_service_py": {"hash": "91f97ff8ba2bc097609a269506f907dd", "index": {"url": "z_8f42f61afc382a2f_password_service_py.html", "file": "apps\\accounts\\services\\password_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 36, "n_excluded": 0, "n_missing": 27, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f42f61afc382a2f_registration_service_py": {"hash": "cb7b3cbcbaaa1b48a2ffa943fb0f09ff", "index": {"url": "z_8f42f61afc382a2f_registration_service_py.html", "file": "apps\\accounts\\services\\registration_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 45, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0daf9d4b3925bce1_signals_py": {"hash": "6129b709dda2ee937b3bf77264c70bf8", "index": {"url": "z_0daf9d4b3925bce1_signals_py.html", "file": "apps\\accounts\\signals.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0daf9d4b3925bce1_tests_py": {"hash": "65d0f68addf660e448351283c334c761", "index": {"url": "z_0daf9d4b3925bce1_tests_py.html", "file": "apps\\accounts\\tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4cd509e0a38bf230___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_4cd509e0a38bf230___init___py.html", "file": "apps\\accounts\\tests\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4cd509e0a38bf230_test_forms_py": {"hash": "de0b23753f22a357fffe732f9230fc45", "index": {"url": "z_4cd509e0a38bf230_test_forms_py.html", "file": "apps\\accounts\\tests\\test_forms.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 128, "n_excluded": 0, "n_missing": 128, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4cd509e0a38bf230_test_middleware_py": {"hash": "358969957ae5aa695b40e00dcfcdbc2d", "index": {"url": "z_4cd509e0a38bf230_test_middleware_py.html", "file": "apps\\accounts\\tests\\test_middleware.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 138, "n_excluded": 0, "n_missing": 138, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4cd509e0a38bf230_test_models_py": {"hash": "61b16ba52339ccfacc37d9817572e27c", "index": {"url": "z_4cd509e0a38bf230_test_models_py.html", "file": "apps\\accounts\\tests\\test_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 108, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4cd509e0a38bf230_test_repositories_py": {"hash": "0953e128cdeac330bc0123e070815374", "index": {"url": "z_4cd509e0a38bf230_test_repositories_py.html", "file": "apps\\accounts\\tests\\test_repositories.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 92, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4cd509e0a38bf230_test_services_py": {"hash": "871edb3c0fb580db985162c90f355eaf", "index": {"url": "z_4cd509e0a38bf230_test_services_py.html", "file": "apps\\accounts\\tests\\test_services.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 121, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4cd509e0a38bf230_test_validators_py": {"hash": "82f7055573974e1d7f25372fc935baf8", "index": {"url": "z_4cd509e0a38bf230_test_validators_py.html", "file": "apps\\accounts\\tests\\test_validators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 126, "n_excluded": 0, "n_missing": 22, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4cd509e0a38bf230_test_views_py": {"hash": "e113bc8540e952eebaf0a596a636643d", "index": {"url": "z_4cd509e0a38bf230_test_views_py.html", "file": "apps\\accounts\\tests\\test_views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 114, "n_excluded": 0, "n_missing": 114, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0daf9d4b3925bce1_urls_py": {"hash": "e8fad5557cc8282981aa9f9538f8cdad", "index": {"url": "z_0daf9d4b3925bce1_urls_py.html", "file": "apps\\accounts\\urls.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0daf9d4b3925bce1_utils_py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_0daf9d4b3925bce1_utils_py.html", "file": "apps\\accounts\\utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_af5f57e6b6e54959___init___py": {"hash": "df30e5d1e704f1311acc5fe1769c9936", "index": {"url": "z_af5f57e6b6e54959___init___py.html", "file": "apps\\accounts\\validators\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_af5f57e6b6e54959_user_validators_py": {"hash": "092c99a6aa86766c038e826a6a08b790", "index": {"url": "z_af5f57e6b6e54959_user_validators_py.html", "file": "apps\\accounts\\validators\\user_validators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 67, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ae12c07b376a20c6___init___py": {"hash": "a56c3ade13e883ed107e97bf8c06eaa1", "index": {"url": "z_ae12c07b376a20c6___init___py.html", "file": "apps\\accounts\\views\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ae12c07b376a20c6_authentication_py": {"hash": "721c41360b20913c1801a262eabfac42", "index": {"url": "z_ae12c07b376a20c6_authentication_py.html", "file": "apps\\accounts\\views\\authentication.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 0, "n_missing": 38, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ae12c07b376a20c6_email_test_py": {"hash": "5d45f34509dcc3b426acb7943f1fe1fa", "index": {"url": "z_ae12c07b376a20c6_email_test_py.html", "file": "apps\\accounts\\views\\email_test.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 125, "n_excluded": 0, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ae12c07b376a20c6_password_reset_py": {"hash": "470ddbfd0c215362a5e788bd02c74335", "index": {"url": "z_ae12c07b376a20c6_password_reset_py.html", "file": "apps\\accounts\\views\\password_reset.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 60, "n_excluded": 0, "n_missing": 42, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ae12c07b376a20c6_profile_py": {"hash": "fe5a110c7c58909ae0869d203e897a49", "index": {"url": "z_ae12c07b376a20c6_profile_py.html", "file": "apps\\accounts\\views\\profile.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 117, "n_excluded": 0, "n_missing": 89, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ae12c07b376a20c6_registration_py": {"hash": "499584ded7d9694e2213760a2dce9e13", "index": {"url": "z_ae12c07b376a20c6_registration_py.html", "file": "apps\\accounts\\views\\registration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 62, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9b6255d6c5a7dc68___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_9b6255d6c5a7dc68___init___py.html", "file": "apps\\articles\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9b6255d6c5a7dc68_admin_py": {"hash": "44063be760b83f9667edcb277d35d2f1", "index": {"url": "z_9b6255d6c5a7dc68_admin_py.html", "file": "apps\\articles\\admin.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 47, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9b6255d6c5a7dc68_apps_py": {"hash": "94bf15056f72812bafb6669694c61338", "index": {"url": "z_9b6255d6c5a7dc68_apps_py.html", "file": "apps\\articles\\apps.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_10e6b26d1150d373___init___py": {"hash": "c42ed8e7b6d3fca77050aaef1be54688", "index": {"url": "z_10e6b26d1150d373___init___py.html", "file": "apps\\articles\\interfaces\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_10e6b26d1150d373_repositories_py": {"hash": "b29bd98cfebdd981c3f6d2a08bf96e27", "index": {"url": "z_10e6b26d1150d373_repositories_py.html", "file": "apps\\articles\\interfaces\\repositories.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 115, "n_excluded": 0, "n_missing": 36, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_10e6b26d1150d373_services_py": {"hash": "d5b344ad71328a354a4384ebe815ac20", "index": {"url": "z_10e6b26d1150d373_services_py.html", "file": "apps\\articles\\interfaces\\services.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 75, "n_excluded": 0, "n_missing": 22, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6ac3d5cc3c7d2853_0001_initial_py": {"hash": "2543662fc3986a42a641d3e0e2964ebf", "index": {"url": "z_6ac3d5cc3c7d2853_0001_initial_py.html", "file": "apps\\articles\\migrations\\0001_initial.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6ac3d5cc3c7d2853___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_6ac3d5cc3c7d2853___init___py.html", "file": "apps\\articles\\migrations\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9b6255d6c5a7dc68_models_py": {"hash": "8c4c1abc074569f0cc0789b01d5481cd", "index": {"url": "z_9b6255d6c5a7dc68_models_py.html", "file": "apps\\articles\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d9000b597388c2b4___init___py": {"hash": "1fb9fe23b45b86103ac94c0adc2461a8", "index": {"url": "z_d9000b597388c2b4___init___py.html", "file": "apps\\articles\\models\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d9000b597388c2b4_article_py": {"hash": "fb46ca878bf72e06ccf9ca0be92086ba", "index": {"url": "z_d9000b597388c2b4_article_py.html", "file": "apps\\articles\\models\\article.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 73, "n_excluded": 0, "n_missing": 23, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d9000b597388c2b4_category_py": {"hash": "a2c37806b64463f63a32406c31155bb9", "index": {"url": "z_d9000b597388c2b4_category_py.html", "file": "apps\\articles\\models\\category.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 50, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d9000b597388c2b4_comment_py": {"hash": "7af4831f0a144edd105ad69d4205927b", "index": {"url": "z_d9000b597388c2b4_comment_py.html", "file": "apps\\articles\\models\\comment.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d9000b597388c2b4_tag_py": {"hash": "239a3c67030d0f394f83718f2408c27e", "index": {"url": "z_d9000b597388c2b4_tag_py.html", "file": "apps\\articles\\models\\tag.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6831235e64db73da___init___py": {"hash": "786717cbb79a4bf570ad5245c381e70f", "index": {"url": "z_6831235e64db73da___init___py.html", "file": "apps\\articles\\repositories\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6831235e64db73da_article_repository_py": {"hash": "b703798024dc87b04e636a990755de06", "index": {"url": "z_6831235e64db73da_article_repository_py.html", "file": "apps\\articles\\repositories\\article_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 97, "n_excluded": 0, "n_missing": 73, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_99cb94331c41d741___init___py": {"hash": "48e1f44d2aba5225ca78bf776c23742c", "index": {"url": "z_99cb94331c41d741___init___py.html", "file": "apps\\articles\\services\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_99cb94331c41d741_article_service_py": {"hash": "611515a49419e5dd0ad8e8e148a68409", "index": {"url": "z_99cb94331c41d741_article_service_py.html", "file": "apps\\articles\\services\\article_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 59, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9b6255d6c5a7dc68_tests_py": {"hash": "65d0f68addf660e448351283c334c761", "index": {"url": "z_9b6255d6c5a7dc68_tests_py.html", "file": "apps\\articles\\tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9b6255d6c5a7dc68_urls_py": {"hash": "87ba01a0a000572160734105cb9f69da", "index": {"url": "z_9b6255d6c5a7dc68_urls_py.html", "file": "apps\\articles\\urls.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9b6255d6c5a7dc68_views_py": {"hash": "6b85b6639c258ca7452f353afdd71a6d", "index": {"url": "z_9b6255d6c5a7dc68_views_py.html", "file": "apps\\articles\\views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ea92ad1c62fa4397___init___py": {"hash": "b0f84c3d86467a822be98a3e83efaaa1", "index": {"url": "z_ea92ad1c62fa4397___init___py.html", "file": "apps\\articles\\views\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ea92ad1c62fa4397_article_views_py": {"hash": "1661bee85749b3cb1960cc1d67b78bd0", "index": {"url": "z_ea92ad1c62fa4397_article_views_py.html", "file": "apps\\articles\\views\\article_views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 43, "n_excluded": 0, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ad224057aff2f7a3___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_ad224057aff2f7a3___init___py.html", "file": "apps\\config\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ad224057aff2f7a3_admin_py": {"hash": "a7c73255dd2e11d23d88db2a3961192a", "index": {"url": "z_ad224057aff2f7a3_admin_py.html", "file": "apps\\config\\admin.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 38, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ad224057aff2f7a3_apps_py": {"hash": "a9bde1ef73cc57db3be0b30ccbf35e49", "index": {"url": "z_ad224057aff2f7a3_apps_py.html", "file": "apps\\config\\apps.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e131a04fbcf9a69a___init___py": {"hash": "860c45b6b1ed7fd2331b47a4776e712f", "index": {"url": "z_e131a04fbcf9a69a___init___py.html", "file": "apps\\config\\forms\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e131a04fbcf9a69a_advanced_config_forms_py": {"hash": "e647003c2d8394ed1f54c2d74c8afa22", "index": {"url": "z_e131a04fbcf9a69a_advanced_config_forms_py.html", "file": "apps\\config\\forms\\advanced_config_forms.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 187, "n_excluded": 0, "n_missing": 130, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e131a04fbcf9a69a_multi_config_forms_py": {"hash": "2b3c0ef59ba34e034a3745778b3c23ac", "index": {"url": "z_e131a04fbcf9a69a_multi_config_forms_py.html", "file": "apps\\config\\forms\\multi_config_forms.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 74, "n_excluded": 0, "n_missing": 50, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e131a04fbcf9a69a_user_forms_py": {"hash": "45274e733dbd428327d1911da98c200b", "index": {"url": "z_e131a04fbcf9a69a_user_forms_py.html", "file": "apps\\config\\forms\\user_forms.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 98, "n_excluded": 0, "n_missing": 50, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2807aa7e64bc1821___init___py": {"hash": "7f81ccea23283e7cd63a2c25792e93af", "index": {"url": "z_2807aa7e64bc1821___init___py.html", "file": "apps\\config\\interfaces\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2807aa7e64bc1821_repositories_py": {"hash": "d1904dba900a3d2692f2a5e79ec0e18c", "index": {"url": "z_2807aa7e64bc1821_repositories_py.html", "file": "apps\\config\\interfaces\\repositories.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 113, "n_excluded": 0, "n_missing": 34, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2807aa7e64bc1821_services_py": {"hash": "ecfee559f3a6ef471848e7a8847e5029", "index": {"url": "z_2807aa7e64bc1821_services_py.html", "file": "apps\\config\\interfaces\\services.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 67, "n_excluded": 0, "n_missing": 19, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0017935c50f3bc1c___init___py": {"hash": "e277d586581929e532a8d1d19fc80c1e", "index": {"url": "z_0017935c50f3bc1c___init___py.html", "file": "apps\\config\\management\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c595fba4bf79988f___init___py": {"hash": "bcaa62ec42d79db1595ddb0becd9834d", "index": {"url": "z_c595fba4bf79988f___init___py.html", "file": "apps\\config\\management\\commands\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c595fba4bf79988f_create_admin_user_py": {"hash": "24b6c253a43047ea6f7f6c7e6c61634e", "index": {"url": "z_c595fba4bf79988f_create_admin_user_py.html", "file": "apps\\config\\management\\commands\\create_admin_user.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 74, "n_excluded": 0, "n_missing": 74, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c595fba4bf79988f_migrate_configs_py": {"hash": "3acca84ca5a1266c68da902b3f8ebeef", "index": {"url": "z_c595fba4bf79988f_migrate_configs_py.html", "file": "apps\\config\\management\\commands\\migrate_configs.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 86, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c595fba4bf79988f_setup_permissions_py": {"hash": "ae4f9ca6f46a09664e8d15c782dacc51", "index": {"url": "z_c595fba4bf79988f_setup_permissions_py.html", "file": "apps\\config\\management\\commands\\setup_permissions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 49, "n_excluded": 0, "n_missing": 49, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4df18df340a057a7_0001_initial_py": {"hash": "daeddea14cb006ed1aa2707bda88a4bf", "index": {"url": "z_4df18df340a057a7_0001_initial_py.html", "file": "apps\\config\\migrations\\0001_initial.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4df18df340a057a7_0002_databaseconfiguration_emailconfiguration_py": {"hash": "1d1295e905e62aab5be7de6b132d9294", "index": {"url": "z_4df18df340a057a7_0002_databaseconfiguration_emailconfiguration_py.html", "file": "apps\\config\\migrations\\0002_databaseconfiguration_emailconfiguration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4df18df340a057a7_0003_alter_emailconfiguration_email_host_password_and_more_py": {"hash": "5412534bf70e5d93a903accbbd7929f6", "index": {"url": "z_4df18df340a057a7_0003_alter_emailconfiguration_email_host_password_and_more_py.html", "file": "apps\\config\\migrations\\0003_alter_emailconfiguration_email_host_password_and_more.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4df18df340a057a7_0004_alter_emailconfiguration_created_by_and_more_py": {"hash": "ec6f830a86a09377f46fde7637046377", "index": {"url": "z_4df18df340a057a7_0004_alter_emailconfiguration_created_by_and_more_py.html", "file": "apps\\config\\migrations\\0004_alter_emailconfiguration_created_by_and_more.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4df18df340a057a7___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_4df18df340a057a7___init___py.html", "file": "apps\\config\\migrations\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ad224057aff2f7a3_mixins_py": {"hash": "237ff044d05766aab67a206a595589ac", "index": {"url": "z_ad224057aff2f7a3_mixins_py.html", "file": "apps\\config\\mixins.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 111, "n_excluded": 0, "n_missing": 87, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ad224057aff2f7a3_models_py": {"hash": "8c4c1abc074569f0cc0789b01d5481cd", "index": {"url": "z_ad224057aff2f7a3_models_py.html", "file": "apps\\config\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_038d050f2a364532___init___py": {"hash": "33ae21f51d33efb637a9549357b14824", "index": {"url": "z_038d050f2a364532___init___py.html", "file": "apps\\config\\models\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_038d050f2a364532_configuration_models_py": {"hash": "c8a6852a7b4eb2cfcbc79da1639557f0", "index": {"url": "z_038d050f2a364532_configuration_models_py.html", "file": "apps\\config\\models\\configuration_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 163, "n_excluded": 0, "n_missing": 93, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_038d050f2a364532_system_configuration_py": {"hash": "c1f4c7a62ef40d1123170b19363f1b05", "index": {"url": "z_038d050f2a364532_system_configuration_py.html", "file": "apps\\config\\models\\system_configuration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 26, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_038d050f2a364532_user_activity_log_py": {"hash": "be4278c6d80e7da15ddb4f97117da3b1", "index": {"url": "z_038d050f2a364532_user_activity_log_py.html", "file": "apps\\config\\models\\user_activity_log.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 21, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b825969784292050___init___py": {"hash": "5ee01326a55a8786ce74b5d693860d68", "index": {"url": "z_b825969784292050___init___py.html", "file": "apps\\config\\repositories\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b825969784292050_config_repository_py": {"hash": "7fa4264f526589258250df72a0bda13f", "index": {"url": "z_b825969784292050_config_repository_py.html", "file": "apps\\config\\repositories\\config_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 68, "n_excluded": 0, "n_missing": 48, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b825969784292050_permission_repository_py": {"hash": "42ad819fe5aba04114dfeb58223968a8", "index": {"url": "z_b825969784292050_permission_repository_py.html", "file": "apps\\config\\repositories\\permission_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 86, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b825969784292050_user_repository_py": {"hash": "e6444aa3b14eb1d0dd8a4dd4e21f1af9", "index": {"url": "z_b825969784292050_user_repository_py.html", "file": "apps\\config\\repositories\\user_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 68, "n_excluded": 0, "n_missing": 52, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f472e13a5c526310___init___py": {"hash": "8fa53ed02f354d783ef919cb3eac46bf", "index": {"url": "z_f472e13a5c526310___init___py.html", "file": "apps\\config\\services\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f472e13a5c526310_permission_management_service_py": {"hash": "432df270d06dcd068cea8a6d66575724", "index": {"url": "z_f472e13a5c526310_permission_management_service_py.html", "file": "apps\\config\\services\\permission_management_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 70, "n_excluded": 0, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f472e13a5c526310_system_config_service_py": {"hash": "0be57137627fc5e60addf04c6fc7eb62", "index": {"url": "z_f472e13a5c526310_system_config_service_py.html", "file": "apps\\config\\services\\system_config_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 67, "n_excluded": 0, "n_missing": 45, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f472e13a5c526310_user_management_service_py": {"hash": "44cd9ab4796522da798baa39f050ca2e", "index": {"url": "z_f472e13a5c526310_user_management_service_py.html", "file": "apps\\config\\services\\user_management_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 62, "n_excluded": 0, "n_missing": 42, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ad224057aff2f7a3_signals_py": {"hash": "20613e236a469814d3052a7d22dd53af", "index": {"url": "z_ad224057aff2f7a3_signals_py.html", "file": "apps\\config\\signals.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_89375c84e16dd47a___init___py": {"hash": "1901632905e472fafb13f38639c4459f", "index": {"url": "z_89375c84e16dd47a___init___py.html", "file": "apps\\config\\templatetags\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_89375c84e16dd47a_config_extras_py": {"hash": "af6becd3239fe7f09fb4bff0242250ef", "index": {"url": "z_89375c84e16dd47a_config_extras_py.html", "file": "apps\\config\\templatetags\\config_extras.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 49, "n_excluded": 0, "n_missing": 31, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ad224057aff2f7a3_tests_py": {"hash": "65d0f68addf660e448351283c334c761", "index": {"url": "z_ad224057aff2f7a3_tests_py.html", "file": "apps\\config\\tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ad224057aff2f7a3_urls_py": {"hash": "43cbb9992cdb464ade8568a890819592", "index": {"url": "z_ad224057aff2f7a3_urls_py.html", "file": "apps\\config\\urls.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ad224057aff2f7a3_views_py": {"hash": "6b85b6639c258ca7452f353afdd71a6d", "index": {"url": "z_ad224057aff2f7a3_views_py.html", "file": "apps\\config\\views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7862fbf3146bb8a1___init___py": {"hash": "7f6c768684a4274d25f6a73dcf714d19", "index": {"url": "z_7862fbf3146bb8a1___init___py.html", "file": "apps\\config\\views\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7862fbf3146bb8a1_advanced_config_views_py": {"hash": "a8d0666be45456a721f04749b3d30e08", "index": {"url": "z_7862fbf3146bb8a1_advanced_config_views_py.html", "file": "apps\\config\\views\\advanced_config_views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 182, "n_excluded": 0, "n_missing": 151, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7862fbf3146bb8a1_dashboard_py": {"hash": "971d59e2c0d49f9ad3df3a08cee7f3f3", "index": {"url": "z_7862fbf3146bb8a1_dashboard_py.html", "file": "apps\\config\\views\\dashboard.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 19, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7862fbf3146bb8a1_multi_config_views_py": {"hash": "133dc64a27025e08ad90dff6b2200be7", "index": {"url": "z_7862fbf3146bb8a1_multi_config_views_py.html", "file": "apps\\config\\views\\multi_config_views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 212, "n_excluded": 0, "n_missing": 160, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7862fbf3146bb8a1_system_config_views_py": {"hash": "a30bdfcbf50835f05a34d2d7ea060553", "index": {"url": "z_7862fbf3146bb8a1_system_config_views_py.html", "file": "apps\\config\\views\\system_config_views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 70, "n_excluded": 0, "n_missing": 53, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7862fbf3146bb8a1_user_views_py": {"hash": "abc77dee4a017a7048526c529ed0ccc4", "index": {"url": "z_7862fbf3146bb8a1_user_views_py.html", "file": "apps\\config\\views\\user_views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 142, "n_excluded": 0, "n_missing": 111, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8e775d4d1883b20c___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_8e775d4d1883b20c___init___py.html", "file": "apps\\pages\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8e775d4d1883b20c_admin_py": {"hash": "fb5741f39e280f1741f6d86228e8517f", "index": {"url": "z_8e775d4d1883b20c_admin_py.html", "file": "apps\\pages\\admin.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 29, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8e775d4d1883b20c_apps_py": {"hash": "574e4473bff98e09e024951763928eae", "index": {"url": "z_8e775d4d1883b20c_apps_py.html", "file": "apps\\pages\\apps.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8e337c540cc623d9___init___py": {"hash": "b4e1be9f5812edbce21b6726378c3f58", "index": {"url": "z_8e337c540cc623d9___init___py.html", "file": "apps\\pages\\forms\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8e337c540cc623d9_contact_form_py": {"hash": "59b38aea4c7909a9f3040e17ceae3e15", "index": {"url": "z_8e337c540cc623d9_contact_form_py.html", "file": "apps\\pages\\forms\\contact_form.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 17, "n_excluded": 0, "n_missing": 17, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8e337c540cc623d9_contact_forms_py": {"hash": "070c7988df8e179ad6451065b57dcf86", "index": {"url": "z_8e337c540cc623d9_contact_forms_py.html", "file": "apps\\pages\\forms\\contact_forms.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 61, "n_excluded": 0, "n_missing": 36, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_73977dc7d293fed5___init___py": {"hash": "56aa330689c6f6e40286bdfd715c86e8", "index": {"url": "z_73977dc7d293fed5___init___py.html", "file": "apps\\pages\\interfaces\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_73977dc7d293fed5_repositories_py": {"hash": "b26a177c14f08b46a40298af99e9df9d", "index": {"url": "z_73977dc7d293fed5_repositories_py.html", "file": "apps\\pages\\interfaces\\repositories.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 79, "n_excluded": 0, "n_missing": 24, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_73977dc7d293fed5_services_py": {"hash": "15cf726aabdcb57df622cb60f83af015", "index": {"url": "z_73977dc7d293fed5_services_py.html", "file": "apps\\pages\\interfaces\\services.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 72, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2481a92531cb87aa_0001_initial_py": {"hash": "87844cbb4c03675a628522124b2aa493", "index": {"url": "z_2481a92531cb87aa_0001_initial_py.html", "file": "apps\\pages\\migrations\\0001_initial.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2481a92531cb87aa___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_2481a92531cb87aa___init___py.html", "file": "apps\\pages\\migrations\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_293efa6a78a0dee6___init___py": {"hash": "ebfaadf0c04e07efdc3934c153267eba", "index": {"url": "z_293efa6a78a0dee6___init___py.html", "file": "apps\\pages\\models\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_293efa6a78a0dee6_navigation_py": {"hash": "25874504616e04cf60475ecb09549ba4", "index": {"url": "z_293efa6a78a0dee6_navigation_py.html", "file": "apps\\pages\\models\\navigation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 45, "n_excluded": 0, "n_missing": 19, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_293efa6a78a0dee6_page_py": {"hash": "8fc47f2b0f3b12ddd58642649ca72b2f", "index": {"url": "z_293efa6a78a0dee6_page_py.html", "file": "apps\\pages\\models\\page.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 66, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_293efa6a78a0dee6_seo_py": {"hash": "2e838231608d49beaadfebc2b0e9866f", "index": {"url": "z_293efa6a78a0dee6_seo_py.html", "file": "apps\\pages\\models\\seo.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 48, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7837dd24d49bbe44___init___py": {"hash": "8c399e35f5b0b9c9b2f966766ce1da51", "index": {"url": "z_7837dd24d49bbe44___init___py.html", "file": "apps\\pages\\repositories\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7837dd24d49bbe44_navigation_repository_py": {"hash": "d9882dbbd50eb097fa2c2eae446f2081", "index": {"url": "z_7837dd24d49bbe44_navigation_repository_py.html", "file": "apps\\pages\\repositories\\navigation_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 46, "n_excluded": 0, "n_missing": 46, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7837dd24d49bbe44_page_repository_py": {"hash": "d60d4d454370e775bc464f4261f28618", "index": {"url": "z_7837dd24d49bbe44_page_repository_py.html", "file": "apps\\pages\\repositories\\page_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 101, "n_excluded": 0, "n_missing": 77, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7837dd24d49bbe44_seo_repository_py": {"hash": "2dd90973c01fc3511192e01b7a3ce2d7", "index": {"url": "z_7837dd24d49bbe44_seo_repository_py.html", "file": "apps\\pages\\repositories\\seo_repository.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_368ff6a066478aeb___init___py": {"hash": "d27f88927740a9f7f49713fe545aa38e", "index": {"url": "z_368ff6a066478aeb___init___py.html", "file": "apps\\pages\\services\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_368ff6a066478aeb_navigation_service_py": {"hash": "9ac532558d521c509d66b193ff40b014", "index": {"url": "z_368ff6a066478aeb_navigation_service_py.html", "file": "apps\\pages\\services\\navigation_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 0, "n_missing": 56, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_368ff6a066478aeb_page_service_py": {"hash": "c393527a1d1b5d1ef53f29775c5fbc6f", "index": {"url": "z_368ff6a066478aeb_page_service_py.html", "file": "apps\\pages\\services\\page_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 92, "n_excluded": 0, "n_missing": 63, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_368ff6a066478aeb_seo_service_py": {"hash": "b0be37d053e110c696d3a07b2f316b8d", "index": {"url": "z_368ff6a066478aeb_seo_service_py.html", "file": "apps\\pages\\services\\seo_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 54, "n_excluded": 0, "n_missing": 54, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8e775d4d1883b20c_signals_py": {"hash": "5152ed5c65e6f417eea48fd81d46e4b8", "index": {"url": "z_8e775d4d1883b20c_signals_py.html", "file": "apps\\pages\\signals.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 38, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8e775d4d1883b20c_tests_py": {"hash": "65d0f68addf660e448351283c334c761", "index": {"url": "z_8e775d4d1883b20c_tests_py.html", "file": "apps\\pages\\tests.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8e775d4d1883b20c_urls_py": {"hash": "e3c64558cf8107ca72e2cfc618371567", "index": {"url": "z_8e775d4d1883b20c_urls_py.html", "file": "apps\\pages\\urls.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8e775d4d1883b20c_views_py": {"hash": "6b85b6639c258ca7452f353afdd71a6d", "index": {"url": "z_8e775d4d1883b20c_views_py.html", "file": "apps\\pages\\views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9e5a0612d4355adf___init___py": {"hash": "eb8c5c413254984e62889979aa57b141", "index": {"url": "z_9e5a0612d4355adf___init___py.html", "file": "apps\\pages\\views\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9e5a0612d4355adf_home_py": {"hash": "89bcec2d68cfa26fcf54791e6d530fe6", "index": {"url": "z_9e5a0612d4355adf_home_py.html", "file": "apps\\pages\\views\\home.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 22, "n_excluded": 0, "n_missing": 15, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9e5a0612d4355adf_page_views_py": {"hash": "67592cbba47e1631cf7463e442f852c8", "index": {"url": "z_9e5a0612d4355adf_page_views_py.html", "file": "apps\\pages\\views\\page_views.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 52, "n_excluded": 0, "n_missing": 37, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9e5a0612d4355adf_static_pages_py": {"hash": "162da4a29bdf6f0c73d48a51174a6abc", "index": {"url": "z_9e5a0612d4355adf_static_pages_py.html", "file": "apps\\pages\\views\\static_pages.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}