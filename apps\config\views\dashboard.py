from django.shortcuts import render
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from django.views import View
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta
from apps.config.services.system_config_service import AuditLogService
from apps.config.repositories.config_repository import DjangoAuditLogRepository
from apps.config.mixins import ConfigPermissionMixin, PermissionHelperMixin
from apps.config.models import SystemConfiguration
import logging

# Importações condicionais para evitar erros se apps não existirem
try:
    from apps.articles.models import Article, Category
    ARTICLES_AVAILABLE = True
except ImportError:
    ARTICLES_AVAILABLE = False

try:
    from apps.accounts.models import UserProfile
    PROFILES_AVAILABLE = True
except ImportError:
    PROFILES_AVAILABLE = False

User = get_user_model()
logger = logging.getLogger('apps.config.views')

class ConfigDashboardView(ConfigPermissionMixin, PermissionHelperMixin, View):
    """Dashboard principal do módulo de configuração"""
    template_name = 'config/dashboard.html'

    def get(self, request):
        """Exibe o dashboard"""
        # Estatísticas de usuários
        total_users = User.objects.count()
        active_users = User.objects.filter(is_active=True).count()
        staff_users = User.objects.filter(is_staff=True).count()
        total_groups = Group.objects.count()

        # Usuários recentes (últimos 30 dias)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        recent_users = User.objects.filter(date_joined__gte=thirty_days_ago).count()

        # Estatísticas de artigos (se disponível)
        article_stats = {}
        if ARTICLES_AVAILABLE:
            article_stats = {
                'total_articles': Article.objects.count(),
                'published_articles': Article.objects.published().count(),
                'draft_articles': Article.objects.filter(status='draft').count(),
                'total_categories': Category.objects.count(),
                'active_categories': Category.objects.active().count(),
            }

            # Artigos recentes (últimos 7 dias)
            seven_days_ago = timezone.now() - timedelta(days=7)
            article_stats['recent_articles'] = Article.objects.filter(
                created_at__gte=seven_days_ago
            ).count()

        # Configurações do sistema
        try:
            configs = SystemConfiguration.objects.all()
            config_count = configs.count()
        except:
            configs = []
            config_count = 0

        # Atividades recentes
        try:
            audit_service = AuditLogService(DjangoAuditLogRepository())
            recent_activities = audit_service.get_system_activity_logs(limit=10)
        except:
            recent_activities = []

        # Atividade recente adicional
        activity_feed = []

        # Últimos usuários registrados
        latest_users = User.objects.order_by('-date_joined')[:5]
        for user in latest_users:
            activity_feed.append({
                'type': 'user_registered',
                'icon': 'fas fa-user-plus',
                'color': 'success',
                'title': f'Novo usuário: {user.get_full_name() or user.username}',
                'description': f'Registrado em {user.date_joined.strftime("%d/%m/%Y %H:%M")}',
                'timestamp': user.date_joined
            })

        # Últimos artigos (se disponível)
        if ARTICLES_AVAILABLE:
            latest_articles = Article.objects.order_by('-created_at')[:5]
            for article in latest_articles:
                activity_feed.append({
                    'type': 'article_created',
                    'icon': 'fas fa-newspaper',
                    'color': 'primary',
                    'title': f'Novo artigo: {article.title}',
                    'description': f'Por {article.author.get_full_name() or article.author.username}',
                    'timestamp': article.created_at
                })

        # Ordena atividade por timestamp
        activity_feed.sort(key=lambda x: x['timestamp'], reverse=True)
        activity_feed = activity_feed[:10]  # Últimas 10 atividades

        # Status do sistema
        system_status = self._get_system_status()

        # Métricas de performance
        performance_metrics = {
            'avg_response_time': '120ms',
            'uptime': '99.9%',
            'memory_usage': '45%',
            'disk_usage': '32%'
        }

        context = {
            # Estatísticas de usuários
            'total_users': total_users,
            'active_users': active_users,
            'staff_users': staff_users,
            'total_groups': total_groups,
            'recent_users': recent_users,

            # Estatísticas de artigos
            **article_stats,

            # Configurações e atividade
            'config_count': config_count,
            'configs': configs,
            'recent_activities': recent_activities,
            'activity_feed': activity_feed,
            'system_status': system_status,
            'performance_metrics': performance_metrics,

            # Flags de disponibilidade
            'articles_available': ARTICLES_AVAILABLE,
            'profiles_available': PROFILES_AVAILABLE,

            # Meta
            'meta_title': 'Dashboard - Configurações',
            'meta_description': 'Dashboard de configurações do sistema',
        }

        logger.info(f"Dashboard acessado por {request.user.username}")

        return render(request, self.template_name, context)

    def _get_system_status(self):
        """Verifica status dos componentes do sistema"""
        status = {
            'database': 'healthy',
            'cache': 'healthy',
            'storage': 'healthy',
            'email': 'healthy'
        }

        # Verifica status do banco de dados
        try:
            User.objects.count()
        except Exception as e:
            status['database'] = 'error'
            logger.error(f"Erro no banco de dados: {e}")

        # Verifica cache (se configurado)
        try:
            from django.core.cache import cache
            cache.set('health_check', 'ok', 30)
            if cache.get('health_check') != 'ok':
                status['cache'] = 'warning'
        except Exception as e:
            status['cache'] = 'error'
            logger.error(f"Erro no cache: {e}")

        return status
