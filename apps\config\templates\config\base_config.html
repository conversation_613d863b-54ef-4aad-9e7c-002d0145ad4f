{% load static %}
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block config_title %}Configurações{% endblock %} - Havoc Admin</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link href="{% static 'css/main.css' %}" rel="stylesheet">
    <link href="{% static 'css/forms.css' %}" rel="stylesheet">

    {% block extra_css %}{% endblock %}
</head>
<body class="bg-light">
    <!-- Toasts -->
    {% include 'includes/_toasts.html' %}
<!-- Config Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-dark mb-4">
    <div class="container">
        <a class="navbar-brand" href="{% url 'config:dashboard' %}">
            <i class="fas fa-cogs me-2"></i>Config Admin
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#configNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="configNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}" 
                       href="{% url 'config:dashboard' %}">
                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle {% if 'user' in request.resolver_match.url_name %}active{% endif %}" 
                       href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-users me-1"></i>Usuários
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item {% if request.resolver_match.url_name == 'user_list' %}active{% endif %}" 
                               href="{% url 'config:user_list' %}">
                            <i class="fas fa-list me-1"></i>Lista de Usuários
                        </a></li>
                        <li><a class="dropdown-item {% if request.resolver_match.url_name == 'user_create' %}active{% endif %}" 
                               href="{% url 'config:user_create' %}">
                            <i class="fas fa-plus me-1"></i>Criar Usuário
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{% url 'config:user_list' %}?is_active=true">
                            <i class="fas fa-check-circle me-1"></i>Usuários Ativos
                        </a></li>
                        <li><a class="dropdown-item" href="{% url 'config:user_list' %}?is_staff=true">
                            <i class="fas fa-user-tie me-1"></i>Staff
                        </a></li>
                        <li><a class="dropdown-item" href="{% url 'config:user_list' %}?is_superuser=true">
                            <i class="fas fa-crown me-1"></i>Superusuários
                        </a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle {% if 'email' in request.resolver_match.url_name or 'database' in request.resolver_match.url_name or 'system' in request.resolver_match.url_name %}active{% endif %}"
                       href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-server me-1"></i>Configurações
                    </a>
                    <ul class="dropdown-menu">
                        <li><h6 class="dropdown-header">Múltiplas Configurações</h6></li>
                        <li><a class="dropdown-item {% if 'email_configs' in request.resolver_match.url_name %}active{% endif %}"
                               href="{% url 'config:email_configs' %}">
                            <i class="fas fa-envelope me-1"></i>Emails
                        </a></li>
                        <li><a class="dropdown-item {% if 'database_configs' in request.resolver_match.url_name %}active{% endif %}"
                               href="{% url 'config:database_configs' %}">
                            <i class="fas fa-database me-1"></i>Bancos de Dados
                        </a></li>

                        <li><hr class="dropdown-divider"></li>
                        <li><h6 class="dropdown-header">Sistema</h6></li>
                        <li><a class="dropdown-item {% if request.resolver_match.url_name == 'system_config' %}active{% endif %}"
                               href="{% url 'config:system_config' %}">
                            <i class="fas fa-tachometer-alt me-1"></i>Visão Geral
                        </a></li>
                        <li><a class="dropdown-item {% if request.resolver_match.url_name == 'database_config' %}active{% endif %}"
                               href="{% url 'config:database_config' %}">
                            <i class="fas fa-database me-1"></i>Banco (Avançado)
                        </a></li>
                        <li><a class="dropdown-item {% if request.resolver_match.url_name == 'email_config' %}active{% endif %}"
                               href="{% url 'config:email_config' %}">
                            <i class="fas fa-envelope me-1"></i>Email (Avançado)
                        </a></li>
                        <li><a class="dropdown-item {% if request.resolver_match.url_name == 'environment_variables' %}active{% endif %}"
                               href="{% url 'config:environment_variables' %}">
                            <i class="fas fa-code me-1"></i>Variáveis de Ambiente
                        </a></li>
                    </ul>
                </li>
            </ul>
            
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'pages:home' %}" target="_blank">
                        <i class="fas fa-external-link-alt me-1"></i>Ver Site
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>{{ request.user.get_full_name|default:request.user.username }}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">
                            <i class="fas fa-user-circle me-1"></i>Meu Perfil
                        </a></li>
                        <li><a class="dropdown-item" href="{% url 'accounts:settings' %}">
                            <i class="fas fa-cog me-1"></i>Configurações
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="{% url 'accounts:logout' %}">
                            <i class="fas fa-sign-out-alt me-1"></i>Sair
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Messages -->
{% if messages %}
    <div class="container">
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    </div>
{% endif %}

<!-- Breadcrumbs -->
{% block breadcrumbs %}
{% if breadcrumbs %}
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                {% for breadcrumb in breadcrumbs %}
                    {% if breadcrumb.is_current %}
                        <li class="breadcrumb-item active" aria-current="page">
                            {{ breadcrumb.title }}
                        </li>
                    {% else %}
                        <li class="breadcrumb-item">
                            <a href="{{ breadcrumb.url }}" class="text-decoration-none">
                                {{ breadcrumb.title }}
                            </a>
                        </li>
                    {% endif %}
                {% endfor %}
            </ol>
        </nav>
    </div>
{% endif %}
{% endblock %}

<!-- Main Content -->
<div class="container">
    {% block config_content %}
    <!-- Content goes here -->
    {% endblock %}
</div>

<!-- Footer -->
{% block config_footer %}
<footer class="mt-5 py-4 bg-light border-top">
    <div class="container">
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-muted">
                    <i class="fas fa-cogs me-1"></i>Painel de Configurações
                </h6>
                <p class="text-muted mb-0">
                    Sistema de gerenciamento e configuração do Havoc
                </p>
            </div>
            <div class="col-md-6 text-md-end">
                <div class="btn-group btn-group-sm">
                    <a href="{% url 'config:dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                    </a>
                    <a href="{% url 'config:user_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-users me-1"></i>Usuários
                    </a>
                    <a href="{% url 'pages:home' %}" class="btn btn-outline-primary" target="_blank">
                        <i class="fas fa-home me-1"></i>Site
                    </a>
                </div>
            </div>
        </div>
        <hr class="my-3">
        <div class="row">
            <div class="col-12 text-center">
                <small class="text-muted">
                    © {% now "Y" %} Havoc - Sistema de Gerenciamento de Conteúdo
                </small>
            </div>
        </div>
    </div>
</footer>
{% endblock %}

<style>
.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.375rem;
}

.dropdown-item.active {
    background-color: var(--bs-primary);
    color: white;
}

.config-stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.5rem;
}

.config-card {
    transition: transform 0.2s ease-in-out;
}

.config-card:hover {
    transform: translateY(-2px);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-close alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-danger)');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
    
    // Add tooltips to buttons
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

{% block extra_js %}{% endblock %}

</body>
</html>
