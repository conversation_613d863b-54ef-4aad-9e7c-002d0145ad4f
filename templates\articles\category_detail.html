{% extends "articles/base_articles.html" %}
{% load static %}

{% block title %}{{ category.name }} - Artigos - {{ block.super }}{% endblock %}

{% block meta_description %}{{ category.description|default:"Artigos da categoria "|add:category.name }}{% endblock %}

{% block articles_content %}
<!-- Header da Categoria -->
<div class="category-header mb-4">
    <div class="d-flex align-items-center mb-3">
        <div class="category-icon me-3">
            <i class="fas fa-folder fa-2x text-primary"></i>
        </div>
        <div>
            <h1 class="h2 mb-1">{{ category.name }}</h1>
            <p class="text-muted mb-0">
                {{ total_articles }} artigo{{ total_articles|pluralize }} nesta categoria
            </p>
        </div>
    </div>
    
    {% if category.description %}
    <div class="category-description">
        <p class="lead">{{ category.description }}</p>
    </div>
    {% endif %}
</div>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item">
            <a href="{% url 'articles:list' %}">
                <i class="fas fa-home me-1"></i>Artigos
            </a>
        </li>
        <li class="breadcrumb-item">
            <a href="{% url 'articles:category_list' %}">Categorias</a>
        </li>
        <li class="breadcrumb-item active" aria-current="page">{{ category.name }}</li>
    </ol>
</nav>

<!-- Filtros e Ordenação -->
<div class="filter-section mb-4">
    <div class="row align-items-center">
        <div class="col-md-6">
            <h6><i class="fas fa-sort me-2"></i>Ordenar por</h6>
            <form method="get" class="d-flex">
                <select name="order" class="form-select form-select-sm me-2" onchange="this.form.submit()">
                    <option value="-created_at" {% if request.GET.order == '-created_at' or not request.GET.order %}selected{% endif %}>
                        Mais Recentes
                    </option>
                    <option value="created_at" {% if request.GET.order == 'created_at' %}selected{% endif %}>
                        Mais Antigos
                    </option>
                    <option value="title" {% if request.GET.order == 'title' %}selected{% endif %}>
                        A-Z
                    </option>
                    <option value="-title" {% if request.GET.order == '-title' %}selected{% endif %}>
                        Z-A
                    </option>
                    <option value="-views" {% if request.GET.order == '-views' %}selected{% endif %}>
                        Mais Visualizados
                    </option>
                </select>
            </form>
        </div>
        
        <div class="col-md-6 text-md-end">
            <a href="{% url 'articles:list' %}" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-arrow-left me-1"></i>Todos os Artigos
            </a>
            {% if user.is_authenticated %}
            <a href="{% url 'articles:create' %}?category={{ category.slug }}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus me-1"></i>Novo Artigo
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Lista de Artigos -->
{% if articles %}
<div class="row">
    {% for article in articles %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card article-card h-100 position-relative">
            <!-- Ações do Artigo (para autores) -->
            {% if user.is_authenticated and user == article.author or user.is_staff %}
            <div class="article-actions">
                <a href="{% url 'articles:update' article.slug %}" 
                   class="action-btn text-primary" 
                   title="Editar">
                    <i class="fas fa-edit"></i>
                </a>
                <a href="{% url 'articles:delete' article.slug %}" 
                   class="action-btn text-danger" 
                   title="Deletar">
                    <i class="fas fa-trash"></i>
                </a>
            </div>
            {% endif %}

            <!-- Imagem do Artigo -->
            {% if article.featured_image %}
            <img src="{{ article.featured_image.url }}" 
                 class="article-image" 
                 alt="{{ article.title }}">
            {% else %}
            <div class="article-image bg-light d-flex align-items-center justify-content-center">
                <i class="fas fa-newspaper fa-3x text-muted"></i>
            </div>
            {% endif %}

            <div class="card-body d-flex flex-column">
                <!-- Status (se for rascunho) -->
                {% if article.status != 'published' %}
                <span class="badge bg-warning text-dark mb-2 align-self-start">
                    <i class="fas fa-eye-slash me-1"></i>{{ article.get_status_display }}
                </span>
                {% endif %}

                <!-- Título -->
                <h5 class="card-title">
                    <a href="{% url 'articles:detail' article.slug %}" 
                       class="text-decoration-none text-dark">
                        {{ article.title }}
                    </a>
                </h5>

                <!-- Excerpt -->
                {% if article.excerpt %}
                <p class="card-text article-excerpt flex-grow-1">
                    {{ article.excerpt|truncatewords:20 }}
                </p>
                {% endif %}

                <!-- Tags -->
                {% if article.tags.all %}
                <div class="mb-2">
                    {% for tag in article.tags.all %}
                    <a href="{% url 'articles:search' %}?tag={{ tag.slug }}" 
                       class="tag-badge">
                        {{ tag.name }}
                    </a>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Meta informações -->
                <div class="article-meta mt-auto">
                    <div class="d-flex justify-content-between align-items-center">
                        <small>
                            <i class="fas fa-user me-1"></i>{{ article.author.get_full_name|default:article.author.username }}
                        </small>
                        <small>
                            <i class="fas fa-calendar me-1"></i>{{ article.created_at|date:"d/m/Y" }}
                        </small>
                    </div>
                    {% if article.views %}
                    <div class="mt-1">
                        <small class="text-muted">
                            <i class="fas fa-eye me-1"></i>{{ article.views }} visualizaç{{ article.views|pluralize:"ão,ões" }}
                        </small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Paginação -->
{% if page_obj.has_other_pages %}
<nav aria-label="Paginação dos artigos" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
        <li class="page-item">
            <a class="page-link" href="?page=1{% if request.GET.order %}&order={{ request.GET.order }}{% endif %}">
                <i class="fas fa-angle-double-left"></i>
            </a>
        </li>
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.order %}&order={{ request.GET.order }}{% endif %}">
                <i class="fas fa-angle-left"></i>
            </a>
        </li>
        {% endif %}

        {% for num in page_obj.paginator.page_range %}
        {% if page_obj.number == num %}
        <li class="page-item active">
            <span class="page-link">{{ num }}</span>
        </li>
        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
        <li class="page-item">
            <a class="page-link" href="?page={{ num }}{% if request.GET.order %}&order={{ request.GET.order }}{% endif %}">
                {{ num }}
            </a>
        </li>
        {% endif %}
        {% endfor %}

        {% if page_obj.has_next %}
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.order %}&order={{ request.GET.order }}{% endif %}">
                <i class="fas fa-angle-right"></i>
            </a>
        </li>
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.order %}&order={{ request.GET.order }}{% endif %}">
                <i class="fas fa-angle-double-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<!-- Nenhum artigo encontrado -->
<div class="text-center py-5">
    <i class="fas fa-folder-open fa-4x text-muted mb-3"></i>
    <h3 class="text-muted">Nenhum artigo nesta categoria</h3>
    <p class="text-muted">
        A categoria "{{ category.name }}" ainda não possui artigos publicados.
    </p>
    {% if user.is_authenticated %}
    <a href="{% url 'articles:create' %}?category={{ category.slug }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Criar Primeiro Artigo
    </a>
    {% endif %}
</div>
{% endif %}
{% endblock %}

{% block sidebar %}
{{ block.super }}

<!-- Informações da Categoria -->
<div class="sidebar-widget">
    <h5><i class="fas fa-info-circle me-2"></i>Sobre esta Categoria</h5>
    <div class="text-center">
        <div class="stats-card mb-3">
            <div class="stats-number">{{ total_articles }}</div>
            <div>Artigos Publicados</div>
        </div>
        
        {% if category.description %}
        <p class="text-muted">{{ category.description }}</p>
        {% endif %}
        
        <div class="small text-muted">
            <div><strong>Criada em:</strong> {{ category.created_at|date:"M/Y" }}</div>
            {% if category.updated_at != category.created_at %}
            <div><strong>Atualizada em:</strong> {{ category.updated_at|date:"d/m/Y" }}</div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Outras Categorias -->
{% if categories %}
<div class="sidebar-widget">
    <h5><i class="fas fa-folder me-2"></i>Outras Categorias</h5>
    {% for cat in categories %}
    {% if cat.slug != category.slug %}
    <a href="{% url 'articles:category_detail' cat.slug %}" 
       class="category-badge">
        {{ cat.name }}
        {% if cat.article_count %}
            ({{ cat.article_count }})
        {% endif %}
    </a>
    {% endif %}
    {% endfor %}
</div>
{% endif %}
{% endblock %}
