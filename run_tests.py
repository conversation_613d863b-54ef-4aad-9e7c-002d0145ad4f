#!/usr/bin/env python
"""
Script para executar todos os testes da aplicação Havoc
"""

import os
import sys
import subprocess
import django
from django.conf import settings
from django.test.utils import get_runner

def setup_django():
    """Configura o Django para os testes"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
    django.setup()

def run_django_tests():
    """Executa testes usando o runner do Django"""
    print("🧪 Executando testes com Django Test Runner...")
    print("=" * 60)
    
    TestRunner = get_runner(settings)
    test_runner = TestRunner(verbosity=2, interactive=True)
    
    # Lista de apps para testar
    test_labels = [
        'apps.accounts.tests',
        'apps.articles.tests',
        'apps.pages.tests',
        'apps.config.tests',
    ]
    
    failures = test_runner.run_tests(test_labels)
    
    if failures:
        print(f"\n❌ {failures} teste(s) falharam!")
        return False
    else:
        print("\n✅ Todos os testes passaram!")
        return True

def run_coverage_tests():
    """Executa testes com coverage"""
    print("\n📊 Executando testes com coverage...")
    print("=" * 60)
    
    try:
        # Executa testes com coverage
        result = subprocess.run([
            'coverage', 'run', '--source=apps', 'manage.py', 'test', 
            'apps.accounts.tests',
            'apps.articles.tests', 
            'apps.pages.tests',
            'apps.config.tests',
            '--verbosity=2'
        ], capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("Erros:", result.stderr)
        
        # Gera relatório de coverage
        subprocess.run(['coverage', 'report', '--show-missing'])
        subprocess.run(['coverage', 'html'])
        
        print("\n📈 Relatório de coverage gerado em htmlcov/index.html")
        
        return result.returncode == 0
        
    except FileNotFoundError:
        print("❌ Coverage não encontrado. Instale com: pip install coverage")
        return False

def run_specific_app_tests(app_name):
    """Executa testes de um app específico"""
    print(f"\n🎯 Executando testes do app: {app_name}")
    print("=" * 60)
    
    TestRunner = get_runner(settings)
    test_runner = TestRunner(verbosity=2, interactive=True)
    
    failures = test_runner.run_tests([f'apps.{app_name}.tests'])
    
    if failures:
        print(f"\n❌ {failures} teste(s) falharam no app {app_name}!")
        return False
    else:
        print(f"\n✅ Todos os testes do app {app_name} passaram!")
        return True

def run_specific_test_type(test_type):
    """Executa testes de um tipo específico"""
    print(f"\n🔍 Executando testes do tipo: {test_type}")
    print("=" * 60)
    
    test_patterns = {
        'models': '**/test_models.py',
        'views': '**/test_views.py',
        'services': '**/test_services.py',
        'repositories': '**/test_repositories.py',
        'forms': '**/test_forms.py',
        'middleware': '**/test_middleware.py',
        'validators': '**/test_validators.py'
    }
    
    if test_type not in test_patterns:
        print(f"❌ Tipo de teste '{test_type}' não reconhecido.")
        print(f"Tipos disponíveis: {', '.join(test_patterns.keys())}")
        return False
    
    try:
        result = subprocess.run([
            'python', 'manage.py', 'test',
            '--pattern', test_patterns[test_type],
            '--verbosity=2'
        ], capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("Erros:", result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Erro ao executar testes: {e}")
        return False

def check_test_dependencies():
    """Verifica se as dependências de teste estão instaladas"""
    print("🔍 Verificando dependências de teste...")
    
    required_packages = ['coverage', 'pytest', 'pytest-django', 'pytest-cov']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Pacotes faltando: {', '.join(missing_packages)}")
        print("Instale com: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ Todas as dependências estão instaladas!")
    return True

def main():
    """Função principal"""
    print("🚀 Havoc - Sistema de Testes Automatizados")
    print("=" * 60)
    
    # Configura Django
    setup_django()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'coverage':
            success = run_coverage_tests()
        elif command == 'check-deps':
            success = check_test_dependencies()
        elif command.startswith('app:'):
            app_name = command.split(':')[1]
            success = run_specific_app_tests(app_name)
        elif command.startswith('type:'):
            test_type = command.split(':')[1]
            success = run_specific_test_type(test_type)
        elif command == 'help':
            print_help()
            return
        else:
            print(f"❌ Comando '{command}' não reconhecido.")
            print_help()
            return
    else:
        # Executa todos os testes
        success = run_django_tests()
    
    sys.exit(0 if success else 1)

def print_help():
    """Imprime ajuda sobre os comandos disponíveis"""
    print("""
📚 Comandos disponíveis:

python run_tests.py                    - Executa todos os testes
python run_tests.py coverage           - Executa testes com coverage
python run_tests.py check-deps         - Verifica dependências
python run_tests.py app:accounts       - Testa apenas o app accounts
python run_tests.py app:articles       - Testa apenas o app articles
python run_tests.py app:pages          - Testa apenas o app pages
python run_tests.py app:config         - Testa apenas o app config
python run_tests.py type:models        - Testa apenas models
python run_tests.py type:views         - Testa apenas views
python run_tests.py type:services      - Testa apenas services
python run_tests.py type:repositories  - Testa apenas repositories
python run_tests.py type:forms         - Testa apenas forms
python run_tests.py type:middleware    - Testa apenas middleware
python run_tests.py type:validators    - Testa apenas validators
python run_tests.py help               - Mostra esta ajuda

📊 Exemplos de uso:
python run_tests.py                    # Todos os testes
python run_tests.py coverage           # Com relatório de coverage
python run_tests.py app:accounts       # Apenas testes do accounts
python run_tests.py type:models        # Apenas testes de models
""")

if __name__ == '__main__':
    main()
