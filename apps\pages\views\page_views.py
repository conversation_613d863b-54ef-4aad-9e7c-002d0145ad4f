from django.shortcuts import render, get_object_or_404, redirect
from django.views import View
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib.auth.decorators import login_required
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.http import JsonResponse, Http404, HttpResponse
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.template import Template, Context
from django.template.loader import get_template
from apps.pages.services.page_service import PageService
from apps.pages.repositories.page_repository import DjangoPageRepository
from apps.pages.models import Page
import logging

logger = logging.getLogger('apps.pages.views')

class PageDetailView(View):
    """View para exibir detalhes de uma página"""
    
    def get(self, request, slug):
        """Exibe uma página específica"""
        # Inicializa service
        page_service = PageService(DjangoPageRepository())
        
        try:
            # Obtém a página
            page = page_service.get_page_by_slug(slug)
            
            # Incrementa contador de visualizações
            page_service.increment_page_views(page.id)
            
            # Obtém breadcrumbs
            breadcrumbs = page_service.get_breadcrumbs(page)
            
            # Obtém páginas filhas se existirem
            children = page.get_children()
            
            # Obtém páginas relacionadas (mesmo nível hierárquico)
            related_pages = []
            if page.parent:
                related_pages = page.parent.get_children().exclude(id=page.id)[:3]
            
            context = {
                'page': page,
                'breadcrumbs': breadcrumbs,
                'children': children,
                'related_pages': related_pages,
                'meta_title': page.seo_title,
                'meta_description': page.seo_description,
                'meta_keywords': page.meta_keywords,
            }
            
            # Usa template personalizado se definido
            template = page.template if page.template else 'pages/page_detail.html'
            
            return render(request, template, context)
            
        except Exception as e:
            # Página não encontrada
            context = {
                'error': 'Página não encontrada',
                'slug': slug,
                'meta_title': 'Página não encontrada',
                'meta_description': 'A página solicitada não foi encontrada',
            }
            return render(request, 'pages/404.html', context, status=404)


class PageListView(View):
    """View para listar páginas"""
    template_name = 'pages/page_list.html'
    
    def get(self, request):
        """Lista páginas com busca e paginação"""
        # Inicializa service
        page_service = PageService(DjangoPageRepository())
        
        # Obtém parâmetros de busca
        query = request.GET.get('q', '').strip()
        
        # Busca ou lista todas as páginas
        if query:
            pages = page_service.search_pages(query)
        else:
            pages = page_service.get_published_pages()
        
        # Paginação
        paginator = Paginator(pages, 12)  # 12 páginas por página
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)
        
        # Obtém páginas populares para sidebar
        popular_pages = page_service.get_popular_pages(limit=5)
        
        context = {
            'page_obj': page_obj,
            'pages': page_obj.object_list,
            'query': query,
            'popular_pages': popular_pages,
            'meta_title': f'Páginas{" - " + query if query else ""}',
            'meta_description': 'Lista de páginas do site',
        }
        
        return render(request, self.template_name, context)


class PageSearchView(View):
    """View para busca de páginas"""
    template_name = 'pages/search_results.html'
    
    def get(self, request):
        """Busca páginas"""
        query = request.GET.get('q', '').strip()
        
        if not query:
            context = {
                'query': '',
                'pages': [],
                'total_results': 0,
                'meta_title': 'Busca',
                'meta_description': 'Busque por páginas no site',
            }
            return render(request, self.template_name, context)
        
        # Inicializa service
        page_service = PageService(DjangoPageRepository())
        
        # Busca páginas
        pages = page_service.search_pages(query)
        
        # Paginação
        paginator = Paginator(pages, 10)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)
        
        context = {
            'query': query,
            'page_obj': page_obj,
            'pages': page_obj.object_list,
            'total_results': paginator.count,
            'meta_title': f'Busca por "{query}"',
            'meta_description': f'Resultados da busca por "{query}"',
        }
        
        return render(request, self.template_name, context)


class DynamicPageView(View):
    """View para páginas dinâmicas com conteúdo renderizado"""

    @method_decorator(cache_page(60 * 15))  # Cache por 15 minutos
    def get(self, request, slug):
        """Renderiza página dinâmica"""
        try:
            page = get_object_or_404(Page, slug=slug, is_published=True)

            # Incrementa visualizações
            page.views_count += 1
            page.save(update_fields=['views_count'])

            # Contexto para renderização dinâmica
            dynamic_context = {
                'request': request,
                'user': request.user,
                'page': page,
                'now': timezone.now() if 'timezone' in globals() else None,
            }

            # Se a página tem conteúdo dinâmico
            if page.is_dynamic and page.content:
                try:
                    # Renderiza o conteúdo como template Django
                    template = Template(page.content)
                    rendered_content = template.render(Context(dynamic_context))
                    page.rendered_content = rendered_content
                except Exception as e:
                    logger.error(f"Erro ao renderizar página dinâmica {slug}: {e}")
                    page.rendered_content = page.content
            else:
                page.rendered_content = page.content

            # Breadcrumbs
            breadcrumbs = []
            current = page
            while current:
                breadcrumbs.insert(0, {
                    'title': current.title,
                    'url': current.get_absolute_url() if current != page else None
                })
                current = current.parent

            # Páginas relacionadas
            related_pages = Page.objects.filter(
                is_published=True,
                parent=page.parent
            ).exclude(id=page.id)[:4]

            context = {
                'page': page,
                'breadcrumbs': breadcrumbs,
                'related_pages': related_pages,
                'meta_title': page.seo_title or page.title,
                'meta_description': page.seo_description or page.excerpt,
                'meta_keywords': page.meta_keywords,
            }

            # Template personalizado ou padrão
            template_name = page.template or 'pages/page_detail.html'

            return render(request, template_name, context)

        except Page.DoesNotExist:
            raise Http404("Página não encontrada")
        except Exception as e:
            logger.error(f"Erro ao exibir página {slug}: {e}")
            raise Http404("Erro ao carregar página")


class PageManagementView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    """View para gerenciamento de páginas (admin)"""
    model = Page
    template_name = 'pages/admin/page_list.html'
    context_object_name = 'pages'
    paginate_by = 20

    def test_func(self):
        return self.request.user.is_staff

    def get_queryset(self):
        queryset = Page.objects.all().select_related('parent', 'author')

        # Filtros
        search = self.request.GET.get('search')
        status = self.request.GET.get('status')
        parent = self.request.GET.get('parent')

        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(content__icontains=search) |
                Q(slug__icontains=search)
            )

        if status == 'published':
            queryset = queryset.filter(is_published=True)
        elif status == 'draft':
            queryset = queryset.filter(is_published=False)

        if parent:
            try:
                parent_page = Page.objects.get(id=parent)
                queryset = queryset.filter(parent=parent_page)
            except Page.DoesNotExist:
                pass

        return queryset.order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'search': self.request.GET.get('search', ''),
            'status': self.request.GET.get('status', ''),
            'parent': self.request.GET.get('parent', ''),
            'parent_pages': Page.objects.filter(parent__isnull=True),
            'total_pages': Page.objects.count(),
            'published_pages': Page.objects.filter(is_published=True).count(),
            'draft_pages': Page.objects.filter(is_published=False).count(),
        })
        return context


class PageCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    """View para criar nova página"""
    model = Page
    template_name = 'pages/admin/page_form.html'
    fields = [
        'title', 'slug', 'content', 'excerpt', 'parent', 'template',
        'is_published', 'is_dynamic', 'seo_title', 'seo_description',
        'meta_keywords', 'featured_image'
    ]
    success_url = reverse_lazy('pages:admin_list')

    def test_func(self):
        return self.request.user.is_staff

    def form_valid(self, form):
        form.instance.author = self.request.user
        messages.success(self.request, f'Página "{form.instance.title}" criada com sucesso!')
        logger.info(f"Página criada: {form.instance.title} por {self.request.user.username}")
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'form_title': 'Nova Página',
            'submit_text': 'Criar Página',
            'parent_pages': Page.objects.filter(parent__isnull=True),
        })
        return context


class PageUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """View para editar página"""
    model = Page
    template_name = 'pages/admin/page_form.html'
    fields = [
        'title', 'slug', 'content', 'excerpt', 'parent', 'template',
        'is_published', 'is_dynamic', 'seo_title', 'seo_description',
        'meta_keywords', 'featured_image'
    ]
    success_url = reverse_lazy('pages:admin_list')

    def test_func(self):
        return self.request.user.is_staff

    def form_valid(self, form):
        messages.success(self.request, f'Página "{form.instance.title}" atualizada com sucesso!')
        logger.info(f"Página atualizada: {form.instance.title} por {self.request.user.username}")
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'form_title': f'Editar: {self.object.title}',
            'submit_text': 'Salvar Alterações',
            'parent_pages': Page.objects.filter(parent__isnull=True).exclude(id=self.object.id),
            'is_edit': True,
        })
        return context


class PageDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    """View para deletar página"""
    model = Page
    template_name = 'pages/admin/page_confirm_delete.html'
    success_url = reverse_lazy('pages:admin_list')

    def test_func(self):
        return self.request.user.is_staff

    def delete(self, request, *args, **kwargs):
        page = self.get_object()
        page_title = page.title

        # Verifica se tem páginas filhas
        if page.get_children().exists():
            messages.error(request, f'Não é possível deletar "{page_title}" pois possui páginas filhas.')
            return redirect('pages:admin_list')

        messages.success(request, f'Página "{page_title}" deletada com sucesso!')
        logger.info(f"Página deletada: {page_title} por {request.user.username}")

        return super().delete(request, *args, **kwargs)


def page_preview(request, pk):
    """Preview de página (para admins)"""
    if not request.user.is_staff:
        raise Http404()

    page = get_object_or_404(Page, pk=pk)

    # Contexto para preview
    context = {
        'page': page,
        'is_preview': True,
        'meta_title': f'Preview: {page.title}',
        'meta_description': page.seo_description or page.excerpt,
    }

    template_name = page.template or 'pages/page_detail.html'

    return render(request, template_name, context)


def page_api(request):
    """API simples para páginas"""
    if request.method == 'GET':
        pages = Page.objects.filter(is_published=True).values(
            'id', 'title', 'slug', 'excerpt', 'created_at'
        )
        return JsonResponse({'pages': list(pages)})

    return JsonResponse({'error': 'Método não permitido'}, status=405)
