from django.shortcuts import render
from django.views import View
from django.conf import settings
from django.db import connection
import platform
import psutil
from datetime import datetime
from apps.config.mixins import ConfigPermissionMixin, PermissionHelperMixin


class SystemConfigView(ConfigPermissionMixin, PermissionHelperMixin, View):
    """View principal para configurações do sistema"""
    template_name = 'config/system_config.html'

    def get(self, request):
        """Exibe página de configurações do sistema"""
        context = self.get_context_data()
        return render(request, self.template_name, context)

    def get_context_data(self):
        """Prepara dados do contexto"""
        # Informações do sistema
        system_info = self.get_system_info()

        # Informações do Django
        django_info = self.get_django_info()

        # Informações do banco de dados
        database_info = self.get_database_info()

        # Estatísticas
        stats = self.get_system_stats()

        return {
            'system_info': system_info,
            'django_info': django_info,
            'database_info': database_info,
            'stats': stats,
        }

    def get_system_info(self):
        """Obtém informações do sistema operacional"""
        try:
            memory = psutil.virtual_memory()

            # Detectar o disco principal baseado no sistema operacional
            import os
            if os.name == 'nt':  # Windows
                disk_path = 'C:\\'
            else:  # Unix/Linux/Mac
                disk_path = '/'

            disk = psutil.disk_usage(disk_path)

            return {
                'platform': platform.platform(),
                'python_version': platform.python_version(),
                'architecture': platform.architecture()[0],
                'processor': platform.processor() or 'N/A',
                'hostname': platform.node(),
                'cpu_count': psutil.cpu_count(),
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_total': self.format_bytes(memory.total),
                'memory_used': self.format_bytes(memory.used),
                'memory_percent': memory.percent,
                'disk_total': self.format_bytes(disk.total),
                'disk_used': self.format_bytes(disk.used),
                'disk_percent': (disk.used / disk.total) * 100,
                'uptime': datetime.now() - datetime.fromtimestamp(psutil.boot_time()),
            }
        except Exception as e:
            return {'error': str(e)}

    def get_django_info(self):
        """Obtém informações do Django"""
        return {
            'version': getattr(settings, 'DJANGO_VERSION', 'N/A'),
            'debug': settings.DEBUG,
            'secret_key_set': bool(getattr(settings, 'SECRET_KEY', None)),
            'allowed_hosts': settings.ALLOWED_HOSTS,
            'installed_apps_count': len(settings.INSTALLED_APPS),
            'middleware_count': len(settings.MIDDLEWARE),
            'time_zone': settings.TIME_ZONE,
            'language_code': settings.LANGUAGE_CODE,
            'use_i18n': settings.USE_I18N,
            'use_l10n': getattr(settings, 'USE_L10N', False),
            'use_tz': settings.USE_TZ,
            'static_url': settings.STATIC_URL,
            'media_url': getattr(settings, 'MEDIA_URL', 'N/A'),
        }

    def get_database_info(self):
        """Obtém informações do banco de dados"""
        try:
            with connection.cursor() as cursor:
                # Verificar se é SQLite ou PostgreSQL
                if connection.vendor == 'sqlite':
                    cursor.execute("SELECT sqlite_version()")
                    db_version = cursor.fetchone()[0] if cursor.rowcount > 0 else 'N/A'
                    table_stats = []  # SQLite não tem estatísticas detalhadas facilmente acessíveis
                elif connection.vendor == 'postgresql':
                    cursor.execute("SELECT version()")
                    db_version = cursor.fetchone()[0] if cursor.rowcount > 0 else 'N/A'

                    # Estatísticas das tabelas para PostgreSQL
                    cursor.execute("""
                        SELECT
                            schemaname,
                            tablename,
                            n_tup_ins as inserts,
                            n_tup_upd as updates,
                            n_tup_del as deletes
                        FROM pg_stat_user_tables
                        ORDER BY n_tup_ins + n_tup_upd + n_tup_del DESC
                        LIMIT 10
                    """)
                    table_stats = cursor.fetchall()
                else:
                    # Para outros bancos de dados
                    cursor.execute("SELECT 1")
                    db_version = 'N/A'
                    table_stats = []

            return {
                'engine': connection.vendor,
                'version': db_version,
                'name': connection.settings_dict['NAME'],
                'host': connection.settings_dict.get('HOST', 'localhost'),
                'port': connection.settings_dict.get('PORT', 'default'),
                'table_stats': table_stats,
            }
        except Exception as e:
            return {
                'engine': connection.vendor,
                'error': str(e)
            }

# Método categorize_configs removido - não mais necessário

    def get_system_stats(self):
        """Obtém estatísticas do sistema"""
        from django.contrib.auth import get_user_model
        from django.contrib.auth.models import Group

        User = get_user_model()

        stats = {
            'total_users': User.objects.count(),
            'active_users': User.objects.filter(is_active=True).count(),
            'staff_users': User.objects.filter(is_staff=True).count(),
            'superusers': User.objects.filter(is_superuser=True).count(),
            'total_groups': Group.objects.count(),
        }

        # Configurações avançadas disponíveis
        stats['advanced_configs'] = 3  # Banco, Email, Variáveis

        # Logs de atividade (pode não existir ainda)
        try:
            from apps.config.models import UserActivityLog
            stats['total_logs'] = UserActivityLog.objects.count()
            stats['recent_logs'] = UserActivityLog.objects.filter(
                created_at__gte=datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            ).count()
        except:
            stats['total_logs'] = 0
            stats['recent_logs'] = 0

        return stats

    def format_bytes(self, bytes_value):
        """Formata bytes em unidades legíveis"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.1f} PB"


class SystemConfigListView(ConfigPermissionMixin, PermissionHelperMixin, View):
    """View para listar e gerenciar configurações do sistema"""
    template_name = 'config/system_config_list.html'

    def get(self, request):
        """Lista todas as configurações"""
        from apps.config.models import SystemConfiguration

        # Busca
        search = request.GET.get('search', '')
        category = request.GET.get('category', '')

        configs = SystemConfiguration.objects.all()

        if search:
            configs = configs.filter(
                Q(key__icontains=search) |
                Q(description__icontains=search) |
                Q(value__icontains=search)
            )

        if category:
            configs = configs.filter(category=category)

        configs = configs.order_by('category', 'key')

        # Categorias disponíveis
        categories = SystemConfiguration.objects.values_list('category', flat=True).distinct()

        context = {
            'configs': configs,
            'categories': categories,
            'search': search,
            'current_category': category,
            'total_configs': configs.count(),
        }

        return render(request, self.template_name, context)

    def post(self, request):
        """Atualiza configurações em lote"""
        from apps.config.models import SystemConfiguration

        updated_count = 0

        for key, value in request.POST.items():
            if key.startswith('config_'):
                config_id = key.replace('config_', '')
                try:
                    config = SystemConfiguration.objects.get(id=config_id)
                    if config.value != value:
                        config.value = value
                        config.save()
                        updated_count += 1
                        logger.info(f"Configuração atualizada: {config.key} = {value}")
                except SystemConfiguration.DoesNotExist:
                    continue

        if updated_count > 0:
            messages.success(request, f'{updated_count} configuração(ões) atualizada(s) com sucesso!')
        else:
            messages.info(request, 'Nenhuma configuração foi alterada.')

        return redirect('config:system_config')


class SystemConfigCreateView(ConfigPermissionMixin, PermissionHelperMixin, View):
    """View para criar nova configuração"""
    template_name = 'config/system_config_form.html'

    def get(self, request):
        """Exibe formulário de criação"""
        context = {
            'form_title': 'Nova Configuração',
            'submit_text': 'Criar Configuração',
        }
        return render(request, self.template_name, context)

    def post(self, request):
        """Cria nova configuração"""
        from apps.config.models import SystemConfiguration

        key = request.POST.get('key', '').strip()
        value = request.POST.get('value', '').strip()
        description = request.POST.get('description', '').strip()
        category = request.POST.get('category', '').strip()
        value_type = request.POST.get('value_type', 'string')
        is_sensitive = request.POST.get('is_sensitive') == 'on'

        # Validações
        if not key:
            messages.error(request, 'A chave é obrigatória.')
            return render(request, self.template_name, {'form_title': 'Nova Configuração'})

        if SystemConfiguration.objects.filter(key=key).exists():
            messages.error(request, f'Já existe uma configuração com a chave "{key}".')
            return render(request, self.template_name, {'form_title': 'Nova Configuração'})

        # Cria configuração
        config = SystemConfiguration.objects.create(
            key=key,
            value=value,
            description=description,
            category=category or 'geral',
            value_type=value_type,
            is_sensitive=is_sensitive
        )

        logger.info(f"Nova configuração criada: {key} por {request.user.username}")
        messages.success(request, f'Configuração "{key}" criada com sucesso!')

        return redirect('config:system_config')


class SystemConfigUpdateView(ConfigPermissionMixin, PermissionHelperMixin, View):
    """View para editar configuração"""
    template_name = 'config/system_config_form.html'

    def get(self, request, config_id):
        """Exibe formulário de edição"""
        from apps.config.models import SystemConfiguration

        config = get_object_or_404(SystemConfiguration, id=config_id)

        context = {
            'config': config,
            'form_title': f'Editar: {config.key}',
            'submit_text': 'Salvar Alterações',
            'is_edit': True,
        }
        return render(request, self.template_name, context)

    def post(self, request, config_id):
        """Atualiza configuração"""
        from apps.config.models import SystemConfiguration

        config = get_object_or_404(SystemConfiguration, id=config_id)

        value = request.POST.get('value', '').strip()
        description = request.POST.get('description', '').strip()
        category = request.POST.get('category', '').strip()
        value_type = request.POST.get('value_type', 'string')
        is_sensitive = request.POST.get('is_sensitive') == 'on'

        # Atualiza configuração
        config.value = value
        config.description = description
        config.category = category or 'geral'
        config.value_type = value_type
        config.is_sensitive = is_sensitive
        config.save()

        logger.info(f"Configuração atualizada: {config.key} por {request.user.username}")
        messages.success(request, f'Configuração "{config.key}" atualizada com sucesso!')

        return redirect('config:system_config')


class SystemConfigDeleteView(ConfigPermissionMixin, PermissionHelperMixin, View):
    """View para deletar configuração"""

    def post(self, request, config_id):
        """Deleta configuração"""
        from apps.config.models import SystemConfiguration

        config = get_object_or_404(SystemConfiguration, id=config_id)
        config_key = config.key

        config.delete()

        logger.info(f"Configuração deletada: {config_key} por {request.user.username}")
        messages.success(request, f'Configuração "{config_key}" deletada com sucesso!')

        return redirect('config:system_config')


def system_config_api(request):
    """API para configurações do sistema"""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Acesso negado'}, status=403)

    from apps.config.models import SystemConfiguration

    if request.method == 'GET':
        configs = SystemConfiguration.objects.all().values('id', 'key', 'value', 'category', 'value_type')
        return JsonResponse({'configs': list(configs)})

    elif request.method == 'POST':
        import json
        data = json.loads(request.body)

        key = data.get('key')
        value = data.get('value')

        if not key:
            return JsonResponse({'error': 'Chave é obrigatória'}, status=400)

        try:
            config = SystemConfiguration.objects.get(key=key)
            config.value = value
            config.save()

            return JsonResponse({
                'success': True,
                'message': f'Configuração {key} atualizada'
            })
        except SystemConfiguration.DoesNotExist:
            return JsonResponse({'error': 'Configuração não encontrada'}, status=404)

    return JsonResponse({'error': 'Método não permitido'}, status=405)
