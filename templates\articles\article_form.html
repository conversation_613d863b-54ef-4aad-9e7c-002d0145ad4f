{% extends "base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{{ form_title|default:"Criar Artigo" }} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
.form-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    padding: 2rem;
    margin: 2rem 0;
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    text-align: center;
}

.form-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.form-section h5 {
    color: #495057;
    margin-bottom: 1rem;
    font-weight: 600;
}

.editor-container {
    border: 1px solid #dee2e6;
    border-radius: 10px;
    overflow: hidden;
}

.editor-toolbar {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0.5rem;
}

.editor-btn {
    background: none;
    border: none;
    padding: 0.5rem;
    margin: 0.2rem;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.editor-btn:hover {
    background: #e9ecef;
}

.editor-btn.active {
    background: #007bff;
    color: white;
}

.preview-container {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 1.5rem;
    min-height: 300px;
    margin-top: 1rem;
}

.tag-input-container {
    position: relative;
}

.tag-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #dee2e6;
    border-top: none;
    border-radius: 0 0 10px 10px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.tag-suggestion {
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.tag-suggestion:hover {
    background: #f8f9fa;
}

.selected-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.tag-item {
    background: #007bff;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tag-remove {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
}

.tag-remove:hover {
    background: rgba(255,255,255,0.2);
}

.image-preview {
    max-width: 100%;
    max-height: 300px;
    border-radius: 10px;
    margin-top: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.form-actions {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 2rem;
    text-align: center;
}

.btn-save {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    transition: transform 0.2s ease;
}

.btn-save:hover {
    transform: translateY(-2px);
    color: white;
}

.btn-preview {
    background: linear-gradient(45deg, #17a2b8, #007bff);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    margin-right: 1rem;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    margin-left: 1rem;
}

@media (max-width: 768px) {
    .form-container {
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .form-header {
        padding: 1.5rem;
    }
    
    .form-section {
        padding: 1rem;
    }
    
    .form-actions .btn {
        display: block;
        width: 100%;
        margin: 0.5rem 0;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Header -->
    <div class="form-header">
        <h1><i class="fas fa-edit me-2"></i>{{ form_title|default:"Criar Novo Artigo" }}</h1>
        <p class="mb-0">
            {% if is_edit %}
                Edite as informações do seu artigo abaixo
            {% else %}
                Preencha as informações para criar um novo artigo
            {% endif %}
        </p>
    </div>

    <form method="post" enctype="multipart/form-data" id="articleForm">
        {% csrf_token %}
        
        <div class="row">
            <!-- Coluna Principal -->
            <div class="col-lg-8">
                <!-- Informações Básicas -->
                <div class="form-section">
                    <h5><i class="fas fa-info-circle me-2"></i>Informações Básicas</h5>
                    
                    <!-- Título -->
                    <div class="mb-3">
                        <label for="{{ form.title.id_for_label }}" class="form-label">
                            <i class="fas fa-heading me-1"></i>Título *
                        </label>
                        {{ form.title|add_class:"form-control form-control-lg" }}
                        {% if form.title.errors %}
                            <div class="text-danger mt-1">{{ form.title.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <!-- Excerpt -->
                    <div class="mb-3">
                        <label for="{{ form.excerpt.id_for_label }}" class="form-label">
                            <i class="fas fa-quote-left me-1"></i>Resumo
                        </label>
                        {{ form.excerpt|add_class:"form-control" }}
                        <div class="form-text">Breve descrição do artigo (opcional)</div>
                        {% if form.excerpt.errors %}
                            <div class="text-danger mt-1">{{ form.excerpt.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>

                <!-- Conteúdo -->
                <div class="form-section">
                    <h5><i class="fas fa-file-alt me-2"></i>Conteúdo</h5>
                    
                    <!-- Editor de Texto -->
                    <div class="editor-container">
                        <div class="editor-toolbar">
                            <button type="button" class="editor-btn" data-command="bold" title="Negrito">
                                <i class="fas fa-bold"></i>
                            </button>
                            <button type="button" class="editor-btn" data-command="italic" title="Itálico">
                                <i class="fas fa-italic"></i>
                            </button>
                            <button type="button" class="editor-btn" data-command="underline" title="Sublinhado">
                                <i class="fas fa-underline"></i>
                            </button>
                            <span class="mx-2">|</span>
                            <button type="button" class="editor-btn" data-command="formatBlock" data-value="h2" title="Título 2">
                                H2
                            </button>
                            <button type="button" class="editor-btn" data-command="formatBlock" data-value="h3" title="Título 3">
                                H3
                            </button>
                            <button type="button" class="editor-btn" data-command="formatBlock" data-value="p" title="Parágrafo">
                                P
                            </button>
                            <span class="mx-2">|</span>
                            <button type="button" class="editor-btn" data-command="insertUnorderedList" title="Lista">
                                <i class="fas fa-list-ul"></i>
                            </button>
                            <button type="button" class="editor-btn" data-command="insertOrderedList" title="Lista Numerada">
                                <i class="fas fa-list-ol"></i>
                            </button>
                            <span class="mx-2">|</span>
                            <button type="button" class="editor-btn" id="togglePreview" title="Visualizar">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        
                        {{ form.content|add_class:"form-control" }}
                        
                        <!-- Preview -->
                        <div class="preview-container" id="contentPreview" style="display: none;">
                            <h6>Visualização:</h6>
                            <div id="previewContent"></div>
                        </div>
                    </div>
                    
                    {% if form.content.errors %}
                        <div class="text-danger mt-1">{{ form.content.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Configurações -->
                <div class="form-section">
                    <h5><i class="fas fa-cog me-2"></i>Configurações</h5>
                    
                    <!-- Status -->
                    <div class="mb-3">
                        <label for="{{ form.status.id_for_label }}" class="form-label">
                            <i class="fas fa-toggle-on me-1"></i>Status
                        </label>
                        {{ form.status|add_class:"form-select" }}
                        {% if form.status.errors %}
                            <div class="text-danger mt-1">{{ form.status.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <!-- Categoria -->
                    <div class="mb-3">
                        <label for="{{ form.category.id_for_label }}" class="form-label">
                            <i class="fas fa-folder me-1"></i>Categoria
                        </label>
                        {{ form.category|add_class:"form-select" }}
                        {% if form.category.errors %}
                            <div class="text-danger mt-1">{{ form.category.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <!-- Tags -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-tags me-1"></i>Tags
                        </label>
                        <div class="tag-input-container">
                            <input type="text" 
                                   class="form-control" 
                                   id="tagInput" 
                                   placeholder="Digite uma tag e pressione Enter">
                            <div class="tag-suggestions" id="tagSuggestions"></div>
                        </div>
                        <div class="selected-tags" id="selectedTags"></div>
                        {{ form.tags|add_class:"d-none" }}
                    </div>
                </div>

                <!-- Imagem Destacada -->
                <div class="form-section">
                    <h5><i class="fas fa-image me-2"></i>Imagem Destacada</h5>
                    
                    <div class="mb-3">
                        {{ form.featured_image|add_class:"form-control" }}
                        {% if form.featured_image.errors %}
                            <div class="text-danger mt-1">{{ form.featured_image.errors.0 }}</div>
                        {% endif %}
                        
                        <!-- Preview da Imagem -->
                        <div id="imagePreview">
                            {% if form.instance.featured_image %}
                            <img src="{{ form.instance.featured_image.url }}" 
                                 class="image-preview" 
                                 alt="Imagem atual">
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Informações Adicionais -->
                {% if is_edit %}
                <div class="form-section">
                    <h5><i class="fas fa-info me-2"></i>Informações</h5>
                    <div class="small text-muted">
                        <div><strong>Criado:</strong> {{ form.instance.created_at|date:"d/m/Y H:i" }}</div>
                        <div><strong>Atualizado:</strong> {{ form.instance.updated_at|date:"d/m/Y H:i" }}</div>
                        <div><strong>Visualizações:</strong> {{ form.instance.views|default:0 }}</div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Ações -->
        <div class="form-actions">
            <button type="button" class="btn btn-preview" id="previewBtn">
                <i class="fas fa-eye me-1"></i>Visualizar
            </button>
            <button type="submit" class="btn btn-save">
                <i class="fas fa-save me-1"></i>{{ submit_text|default:"Salvar Artigo" }}
            </button>
            <a href="{% if is_edit %}{% url 'articles:detail' form.instance.slug %}{% else %}{% url 'articles:list' %}{% endif %}" 
               class="btn btn-cancel">
                <i class="fas fa-times me-1"></i>Cancelar
            </a>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Editor de texto simples
    const contentField = document.getElementById('{{ form.content.id_for_label }}');
    const previewContainer = document.getElementById('contentPreview');
    const previewContent = document.getElementById('previewContent');
    const togglePreviewBtn = document.getElementById('togglePreview');
    
    // Toggle preview
    togglePreviewBtn.addEventListener('click', function() {
        if (previewContainer.style.display === 'none') {
            previewContent.innerHTML = contentField.value.replace(/\n/g, '<br>');
            previewContainer.style.display = 'block';
            this.innerHTML = '<i class="fas fa-edit"></i>';
        } else {
            previewContainer.style.display = 'none';
            this.innerHTML = '<i class="fas fa-eye"></i>';
        }
    });

    // Sistema de tags
    const tagInput = document.getElementById('tagInput');
    const selectedTagsContainer = document.getElementById('selectedTags');
    const tagsField = document.getElementById('{{ form.tags.id_for_label }}');
    const tagSuggestions = document.getElementById('tagSuggestions');
    
    let selectedTags = [];
    
    // Carrega tags existentes
    {% if form.instance.tags.all %}
    {% for tag in form.instance.tags.all %}
    selectedTags.push('{{ tag.name }}');
    {% endfor %}
    updateTagsDisplay();
    {% endif %}
    
    tagInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            addTag(this.value.trim());
            this.value = '';
        }
    });
    
    function addTag(tagName) {
        if (tagName && !selectedTags.includes(tagName)) {
            selectedTags.push(tagName);
            updateTagsDisplay();
        }
    }
    
    function removeTag(tagName) {
        selectedTags = selectedTags.filter(tag => tag !== tagName);
        updateTagsDisplay();
    }
    
    function updateTagsDisplay() {
        selectedTagsContainer.innerHTML = '';
        selectedTags.forEach(tag => {
            const tagElement = document.createElement('div');
            tagElement.className = 'tag-item';
            tagElement.innerHTML = `
                ${tag}
                <button type="button" class="tag-remove" onclick="removeTag('${tag}')">
                    <i class="fas fa-times"></i>
                </button>
            `;
            selectedTagsContainer.appendChild(tagElement);
        });
        
        // Atualiza campo hidden
        tagsField.value = selectedTags.join(',');
    }
    
    // Torna removeTag global
    window.removeTag = removeTag;

    // Preview de imagem
    const imageInput = document.getElementById('{{ form.featured_image.id_for_label }}');
    const imagePreview = document.getElementById('imagePreview');
    
    imageInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.innerHTML = `<img src="${e.target.result}" class="image-preview" alt="Preview">`;
            };
            reader.readAsDataURL(file);
        }
    });

    // Auto-save (opcional)
    let autoSaveTimer;
    const form = document.getElementById('articleForm');
    
    function autoSave() {
        const formData = new FormData(form);
        // Implementar auto-save via AJAX se necessário
        console.log('Auto-save triggered');
    }
    
    // Auto-save a cada 30 segundos
    contentField.addEventListener('input', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(autoSave, 30000);
    });
});
</script>
{% endblock %}
