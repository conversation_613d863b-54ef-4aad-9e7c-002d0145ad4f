{% load static %}
<!DOCTYPE html>
<html lang="pt-br" class="h-100">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Havoc - Sistema de Gerenciamento{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" 
          integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link href="{% static 'css/main.css' %}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body class="d-flex flex-column h-100">
    <!-- Skip to main content -->
    <a class="visually-hidden-focusable" href="#main-content">Pular para o conteúdo principal</a>

    <!-- Header -->
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container">
                <a class="navbar-brand" href="{% url 'pages:home' %}">
                    <i class="fas fa-rocket me-2"></i>Havoc
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'pages:home' %}">
                                <i class="fas fa-home me-1"></i>Início
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'articles:list' %}">
                                <i class="fas fa-newspaper me-1"></i>Artigos
                            </a>
                        </li>
                    </ul>
                    
                    <ul class="navbar-nav">
                        {% if user.is_authenticated %}
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user me-1"></i>{{ user.get_full_name|default:user.username }}
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">
                                        <i class="fas fa-user me-2"></i>Perfil
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'accounts:settings' %}">
                                        <i class="fas fa-cog me-2"></i>Configurações
                                    </a></li>
                                    {% if user.is_staff %}
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="{% url 'config:dashboard' %}">
                                            <i class="fas fa-tools me-2"></i>Admin
                                        </a></li>
                                    {% endif %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">
                                        <i class="fas fa-sign-out-alt me-2"></i>Sair
                                    </a></li>
                                </ul>
                            </li>
                        {% else %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'accounts:login' %}">
                                    <i class="fas fa-sign-in-alt me-1"></i>Entrar
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'accounts:register' %}">
                                    <i class="fas fa-user-plus me-1"></i>Registrar
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Messages -->
    {% if messages %}
        <div class="container mt-3">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {% if message.tags == 'error' %}
                        <i class="fas fa-exclamation-triangle me-2"></i>
                    {% elif message.tags == 'warning' %}
                        <i class="fas fa-exclamation-circle me-2"></i>
                    {% elif message.tags == 'success' %}
                        <i class="fas fa-check-circle me-2"></i>
                    {% elif message.tags == 'info' %}
                        <i class="fas fa-info-circle me-2"></i>
                    {% endif %}
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <main id="main-content" class="flex-shrink-0">
        {% block content %}
        <!-- Default content if no content block is provided -->
        <div class="container my-5">
            <div class="row">
                <div class="col-12 text-center">
                    <h1>Bem-vindo ao Havoc</h1>
                    <p class="lead">Sistema de gerenciamento de conteúdo moderno</p>
                </div>
            </div>
        </div>
        {% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer mt-auto py-3 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <span class="text-muted">© {% now "Y" %} Havoc - Sistema de Gerenciamento</span>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="{% url 'pages:about' %}" class="text-muted text-decoration-none me-3">Sobre</a>
                    <a href="{% url 'pages:contact' %}" class="text-muted text-decoration-none">Contato</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" 
            integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" 
            crossorigin="anonymous"></script>

    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>

    <!-- Extra JavaScript -->
    {% block extra_js %}{% endblock %}
</body>
</html>
