{% extends "base.html" %}
{% load static %}

{% block title %}Páginas{% if query %} - {{ query }}{% endif %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
.pages-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0 2rem;
    margin-bottom: 3rem;
}

.page-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    margin-bottom: 2rem;
    overflow: hidden;
    text-decoration: none;
    color: inherit;
    display: block;
}

.page-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    text-decoration: none;
    color: inherit;
}

.page-card-image {
    height: 200px;
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 3rem;
    position: relative;
    overflow: hidden;
}

.page-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.page-card-body {
    padding: 1.5rem;
}

.page-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    line-height: 1.3;
}

.page-card-excerpt {
    color: #6c757d;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.page-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    color: #6c757d;
}

.page-card-author {
    display: flex;
    align-items: center;
}

.page-card-author i {
    margin-right: 0.5rem;
}

.page-card-date {
    display: flex;
    align-items: center;
}

.page-card-date i {
    margin-right: 0.5rem;
}

.search-form {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 3rem;
}

.search-input {
    border: 2px solid #e9ecef;
    border-radius: 50px;
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
    transition: all 0.2s ease;
}

.search-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.search-btn {
    border-radius: 50px;
    padding: 0.75rem 2rem;
    font-weight: 600;
}

.sidebar-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.sidebar-card h5 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-weight: 600;
}

.popular-page {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: 8px;
    transition: background-color 0.2s ease;
    text-decoration: none;
    color: inherit;
    margin-bottom: 0.5rem;
}

.popular-page:hover {
    background-color: #f8f9fa;
    text-decoration: none;
    color: inherit;
}

.popular-page-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.popular-page-content h6 {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
    color: #2c3e50;
}

.popular-page-content small {
    color: #6c757d;
}

.pagination-custom {
    margin-top: 3rem;
}

.pagination-custom .page-link {
    border: none;
    border-radius: 50px;
    margin: 0 0.25rem;
    padding: 0.75rem 1rem;
    color: #007bff;
    transition: all 0.2s ease;
}

.pagination-custom .page-link:hover {
    background-color: #007bff;
    color: white;
    transform: translateY(-2px);
}

.pagination-custom .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

.no-results {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.no-results i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #dee2e6;
}

.stats-bar {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-around;
    text-align: center;
}

.stat-item h4 {
    margin-bottom: 0.25rem;
    font-weight: bold;
}

.stat-item small {
    opacity: 0.8;
}

@media (max-width: 768px) {
    .pages-header {
        padding: 2rem 0 1rem;
        margin-bottom: 2rem;
    }
    
    .search-form {
        padding: 1.5rem;
    }
    
    .page-card-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .stats-bar {
        flex-direction: column;
        gap: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="pages-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 mb-2">
                    <i class="fas fa-file-alt me-3"></i>Páginas
                </h1>
                <p class="lead mb-0">
                    {% if query %}
                        Resultados para "{{ query }}"
                    {% else %}
                        Explore todas as páginas do site
                    {% endif %}
                </p>
            </div>
            <div class="col-md-4">
                <!-- Estatísticas -->
                <div class="stats-bar">
                    <div class="stat-item">
                        <h4>{{ page_obj.paginator.count }}</h4>
                        <small>Páginas</small>
                    </div>
                    {% if popular_pages %}
                    <div class="stat-item">
                        <h4>{{ popular_pages.count }}</h4>
                        <small>Populares</small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Formulário de Busca -->
    <div class="search-form">
        <form method="get" class="row g-3">
            <div class="col-md-9">
                <input type="text" 
                       name="q" 
                       value="{{ query }}" 
                       class="form-control search-input" 
                       placeholder="Buscar páginas..."
                       autocomplete="off">
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary search-btn w-100">
                    <i class="fas fa-search me-2"></i>Buscar
                </button>
            </div>
        </form>
        
        {% if query %}
        <div class="mt-3">
            <a href="{% url 'pages:list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-times me-2"></i>Limpar Busca
            </a>
        </div>
        {% endif %}
    </div>

    <div class="row">
        <!-- Lista de Páginas -->
        <div class="col-lg-8">
            {% if pages %}
                <div class="row">
                    {% for page in pages %}
                    <div class="col-md-6">
                        <a href="{{ page.get_absolute_url }}" class="page-card">
                            <div class="page-card-image">
                                {% if page.featured_image %}
                                    <img src="{{ page.featured_image.url }}" alt="{{ page.title }}">
                                {% else %}
                                    <i class="fas fa-file-alt"></i>
                                {% endif %}
                            </div>
                            <div class="page-card-body">
                                <h3 class="page-card-title">{{ page.title }}</h3>
                                
                                {% if page.excerpt %}
                                <p class="page-card-excerpt">
                                    {{ page.excerpt|truncatewords:20 }}
                                </p>
                                {% endif %}
                                
                                <div class="page-card-meta">
                                    <div class="page-card-author">
                                        <i class="fas fa-user"></i>
                                        {{ page.author.get_full_name|default:page.author.username }}
                                    </div>
                                    <div class="page-card-date">
                                        <i class="fas fa-calendar"></i>
                                        {{ page.created_at|date:"d/m/Y" }}
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    {% endfor %}
                </div>

                <!-- Paginação -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Navegação de páginas" class="pagination-custom">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if query %}&q={{ query }}{% endif %}">
                                <i class="fas fa-angle-double-left"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if query %}&q={{ query }}{% endif %}">
                                <i class="fas fa-angle-left"></i>
                            </a>
                        </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if query %}&q={{ query }}{% endif %}">{{ num }}</a>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if query %}&q={{ query }}{% endif %}">
                                <i class="fas fa-angle-right"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if query %}&q={{ query }}{% endif %}">
                                <i class="fas fa-angle-double-right"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

            {% else %}
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <h3>Nenhuma página encontrada</h3>
                    {% if query %}
                    <p>Não encontramos páginas para "{{ query }}".</p>
                    <a href="{% url 'pages:list' %}" class="btn btn-primary">
                        <i class="fas fa-list me-2"></i>Ver Todas as Páginas
                    </a>
                    {% else %}
                    <p>Ainda não há páginas publicadas.</p>
                    {% endif %}
                </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Páginas Populares -->
            {% if popular_pages %}
            <div class="sidebar-card">
                <h5><i class="fas fa-fire me-2"></i>Páginas Populares</h5>
                {% for popular in popular_pages %}
                <a href="{{ popular.get_absolute_url }}" class="popular-page">
                    <div class="popular-page-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="popular-page-content">
                        <h6>{{ popular.title|truncatechars:30 }}</h6>
                        <small>{{ popular.views_count|default:0 }} visualizações</small>
                    </div>
                </a>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Navegação Rápida -->
            <div class="sidebar-card">
                <h5><i class="fas fa-compass me-2"></i>Navegação</h5>
                <a href="{% url 'pages:home' %}" class="popular-page">
                    <div class="popular-page-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="popular-page-content">
                        <h6>Página Inicial</h6>
                        <small>Voltar ao início</small>
                    </div>
                </a>
                
                {% if user.is_staff %}
                <a href="{% url 'pages:admin_list' %}" class="popular-page">
                    <div class="popular-page-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="popular-page-content">
                        <h6>Gerenciar Páginas</h6>
                        <small>Painel administrativo</small>
                    </div>
                </a>
                {% endif %}
            </div>

            <!-- Dicas de Busca -->
            <div class="sidebar-card">
                <h5><i class="fas fa-lightbulb me-2"></i>Dicas de Busca</h5>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Use palavras-chave específicas
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Busque por títulos ou conteúdo
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Combine múltiplas palavras
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animação de entrada para os cards
    const cards = document.querySelectorAll('.page-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Auto-focus no campo de busca
    const searchInput = document.querySelector('.search-input');
    if (searchInput && !searchInput.value) {
        searchInput.focus();
    }

    // Busca em tempo real (opcional)
    let searchTimeout;
    searchInput?.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            if (this.value.length >= 3) {
                // Implementar busca AJAX aqui se necessário
                console.log('Buscando por:', this.value);
            }
        }, 500);
    });

    // Lazy loading para imagens
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
});
</script>
{% endblock %}
