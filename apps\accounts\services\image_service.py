from PIL import Image
import os
import logging
from typing import Optional
from django.core.files.uploadedfile import InMemoryUploadedFile
from io import BytesIO
from django.core.files.base import ContentFile

logger = logging.getLogger(__name__)

class ImageService:
    """Serviço para manipulação de imagens"""
    
    @staticmethod
    def resize_avatar(image_path: str, max_size: tuple = (300, 300)) -> bool:
        """
        Redimensiona uma imagem de avatar
        :param image_path: Caminho para a imagem
        :param max_size: <PERSON><PERSON><PERSON> máxi<PERSON> (largura, altura)
        :return: True se redimensionado com sucesso
        """
        try:
            if not os.path.exists(image_path):
                return False
                
            with Image.open(image_path) as img:
                # Verifica se precisa redimensionar
                if img.height > max_size[1] or img.width > max_size[0]:
                    # Mantém proporção
                    img.thumbnail(max_size, Image.Resampling.LANCZOS)
                    
                    # Converte para RGB se necessário (para JPEG)
                    if img.mode in ('RGBA', 'P'):
                        img = img.convert('RGB')
                    
                    # Salva a imagem redimensionada
                    img.save(image_path, optimize=True, quality=85)
                    logger.info(f"Avatar redimensionado: {image_path}")
                    
            return True
        except Exception as e:
            logger.error(f"Erro ao redimensionar avatar {image_path}: {str(e)}")
            return False
    
    @staticmethod
    def process_uploaded_avatar(uploaded_file: InMemoryUploadedFile, max_size: tuple = (300, 300)) -> Optional[ContentFile]:
        """
        Processa um arquivo de avatar enviado
        :param uploaded_file: Arquivo enviado
        :param max_size: Tamanho máximo
        :return: ContentFile processado ou None
        """
        try:
            # Abre a imagem
            img = Image.open(uploaded_file)
            
            # Converte para RGB se necessário
            if img.mode in ('RGBA', 'P'):
                img = img.convert('RGB')
            
            # Redimensiona se necessário
            if img.height > max_size[1] or img.width > max_size[0]:
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # Salva em buffer
            buffer = BytesIO()
            img.save(buffer, format='JPEG', optimize=True, quality=85)
            buffer.seek(0)
            
            # Cria ContentFile
            return ContentFile(
                buffer.getvalue(),
                name=f"{uploaded_file.name.split('.')[0]}.jpg"
            )
            
        except Exception as e:
            logger.error(f"Erro ao processar avatar: {str(e)}")
            return None
    
    @staticmethod
    def validate_image_file(uploaded_file: InMemoryUploadedFile) -> tuple[bool, str]:
        """
        Valida um arquivo de imagem
        :param uploaded_file: Arquivo enviado
        :return: (é_válido, mensagem_erro)
        """
        try:
            # Verifica tamanho do arquivo (max 5MB)
            if uploaded_file.size > 5 * 1024 * 1024:
                return False, "Arquivo muito grande. Máximo 5MB."
            
            # Verifica tipo de arquivo
            allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
            if uploaded_file.content_type not in allowed_types:
                return False, "Tipo de arquivo não permitido. Use JPEG, PNG ou GIF."
            
            # Tenta abrir a imagem para verificar se é válida
            img = Image.open(uploaded_file)
            img.verify()
            
            # Reseta o ponteiro do arquivo
            uploaded_file.seek(0)
            
            return True, ""
            
        except Exception as e:
            logger.error(f"Erro na validação de imagem: {str(e)}")
            return False, "Arquivo de imagem inválido."
    
    @staticmethod
    def delete_image_file(image_path: str) -> bool:
        """
        Deleta um arquivo de imagem
        :param image_path: Caminho para a imagem
        :return: True se deletado com sucesso
        """
        try:
            if os.path.exists(image_path):
                os.remove(image_path)
                logger.info(f"Imagem deletada: {image_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Erro ao deletar imagem {image_path}: {str(e)}")
            return False
