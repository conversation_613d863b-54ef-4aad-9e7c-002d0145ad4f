from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from apps.accounts.models.verification import VerificationCode
from django.utils import timezone
from datetime import timedelta
import tempfile
import os
from PIL import Image

User = get_user_model()


class UserModelTest(TestCase):
    """Testes para o modelo User"""

    def setUp(self):
        """Configuração inicial para os testes"""
        self.user_data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'testpass123'
        }

    def test_create_user(self):
        """Testa criação de usuário"""
        user = User.objects.create_user(**self.user_data)

        self.assertEqual(user.email, self.user_data['email'])
        self.assertEqual(user.username, self.user_data['username'])
        self.assertEqual(user.first_name, self.user_data['first_name'])
        self.assertEqual(user.last_name, self.user_data['last_name'])
        self.assertFalse(user.is_verified)
        self.assertTrue(user.check_password(self.user_data['password']))
        self.assertTrue(user.slug)  # Slug deve ser gerado automaticamente

    def test_create_superuser(self):
        """Testa criação de superusuário"""
        user = User.objects.create_superuser(
            email='<EMAIL>',
            password='adminpass123',
            username='admin'
        )

        self.assertTrue(user.is_staff)
        self.assertTrue(user.is_superuser)
        self.assertTrue(user.is_verified)

    def test_email_unique_constraint(self):
        """Testa constraint de email único"""
        User.objects.create_user(**self.user_data)

        with self.assertRaises(IntegrityError):
            User.objects.create_user(
                email=self.user_data['email'],
                username='anotheruser',
                password='pass123'
            )

    def test_slug_generation(self):
        """Testa geração automática de slug"""
        user = User.objects.create_user(**self.user_data)
        self.assertTrue(user.slug)
        self.assertIn('test', user.slug.lower())

    def test_slug_uniqueness(self):
        """Testa unicidade do slug"""
        user1 = User.objects.create_user(**self.user_data)

        user2_data = self.user_data.copy()
        user2_data['email'] = '<EMAIL>'
        user2_data['username'] = 'testuser2'
        user2 = User.objects.create_user(**user2_data)

        self.assertNotEqual(user1.slug, user2.slug)

    def test_get_full_name(self):
        """Testa método get_full_name"""
        user = User.objects.create_user(**self.user_data)
        expected_name = f"{self.user_data['first_name']} {self.user_data['last_name']}"
        self.assertEqual(user.get_full_name(), expected_name)

    def test_get_initials(self):
        """Testa método get_initials"""
        user = User.objects.create_user(**self.user_data)
        expected_initials = f"{self.user_data['first_name'][0]}{self.user_data['last_name'][0]}".upper()
        self.assertEqual(user.get_initials(), expected_initials)

    def test_get_initials_email_fallback(self):
        """Testa fallback de iniciais para email"""
        user_data = self.user_data.copy()
        user_data['first_name'] = ''
        user_data['last_name'] = ''
        user = User.objects.create_user(**user_data)

        expected_initial = self.user_data['email'][0].upper()
        self.assertEqual(user.get_initials(), expected_initial)

    def test_get_default_avatar(self):
        """Testa geração de avatar padrão"""
        user = User.objects.create_user(**self.user_data)
        avatar_url = user.get_default_avatar()

        self.assertIn('ui-avatars.com', avatar_url)
        self.assertIn(user.get_initials(), avatar_url)

    def test_get_avatar_url_without_avatar(self):
        """Testa URL de avatar quando não há avatar"""
        user = User.objects.create_user(**self.user_data)
        avatar_url = user.get_avatar_url()

        # Deve retornar o avatar padrão
        self.assertIn('ui-avatars.com', avatar_url)

    def test_str_representation(self):
        """Testa representação string do usuário"""
        user = User.objects.create_user(**self.user_data)
        self.assertEqual(str(user), self.user_data['email'])

    def test_get_absolute_url(self):
        """Testa URL absoluta do usuário"""
        user = User.objects.create_user(**self.user_data)
        url = user.get_absolute_url()
        self.assertIn('/accounts/perfil/', url)


class VerificationCodeModelTest(TestCase):
    """Testes para o modelo VerificationCode"""

    def setUp(self):
        """Configuração inicial para os testes"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )

    def test_create_verification_code(self):
        """Testa criação de código de verificação"""
        code = VerificationCode.objects.create(
            user=self.user,
            code='123456',
            code_type=VerificationCode.REGISTRATION
        )

        self.assertEqual(code.user, self.user)
        self.assertEqual(code.code, '123456')
        self.assertEqual(code.code_type, VerificationCode.REGISTRATION)
        self.assertTrue(code.created_at)
        self.assertTrue(code.expires_at)

    def test_auto_expiration_setting(self):
        """Testa configuração automática de expiração"""
        code = VerificationCode.objects.create(
            user=self.user,
            code='123456',
            code_type=VerificationCode.REGISTRATION
        )

        # Deve expirar em 10 minutos
        expected_expiry = code.created_at + timedelta(minutes=10)
        self.assertAlmostEqual(
            code.expires_at,
            expected_expiry,
            delta=timedelta(seconds=1)
        )

    def test_is_expired_false(self):
        """Testa código não expirado"""
        code = VerificationCode.objects.create(
            user=self.user,
            code='123456',
            code_type=VerificationCode.REGISTRATION
        )

        self.assertFalse(code.is_expired())

    def test_is_expired_true(self):
        """Testa código expirado"""
        code = VerificationCode.objects.create(
            user=self.user,
            code='123456',
            code_type=VerificationCode.REGISTRATION
        )

        # Força expiração
        code.expires_at = timezone.now() - timedelta(minutes=1)
        code.save()

        self.assertTrue(code.is_expired())

    def test_unique_together_constraint(self):
        """Testa constraint unique_together"""
        VerificationCode.objects.create(
            user=self.user,
            code='123456',
            code_type=VerificationCode.REGISTRATION
        )

        with self.assertRaises(IntegrityError):
            VerificationCode.objects.create(
                user=self.user,
                code='654321',
                code_type=VerificationCode.REGISTRATION
            )

    def test_different_code_types_allowed(self):
        """Testa que diferentes tipos de código são permitidos"""
        code1 = VerificationCode.objects.create(
            user=self.user,
            code='123456',
            code_type=VerificationCode.REGISTRATION
        )

        code2 = VerificationCode.objects.create(
            user=self.user,
            code='654321',
            code_type=VerificationCode.PASSWORD_RESET
        )

        self.assertNotEqual(code1.code_type, code2.code_type)

    def test_str_representation(self):
        """Testa representação string do código"""
        code = VerificationCode.objects.create(
            user=self.user,
            code='123456',
            code_type=VerificationCode.REGISTRATION
        )

        expected_str = f"{self.user.email} - Registro de Conta - 123456"
        self.assertEqual(str(code), expected_str)