2025-06-06 21:17:14,100 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:17:27,666 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\core\settings.py changed, reloading.
2025-06-06 21:17:28,116 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:17:38,183 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:38,601 INFO apps.config.signals signals:log_user_creation:14 - Novo usu<PERSON>rio criado: <EMAIL> (ID: 1)
2025-06-06 21:17:39,419 INFO apps.config.signals signals:log_user_creation:14 - Novo usu<PERSON>rio criado: <EMAIL> (ID: 1)
2025-06-06 21:17:40,223 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:40,629 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:41,029 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:41,428 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:41,832 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:42,232 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:42,631 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:43,039 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:43,438 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 2)
2025-06-06 21:17:43,839 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:44,240 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:44,641 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:45,046 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:45,444 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:45,848 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:46,248 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:46,657 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:55,102 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:55,607 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:56,433 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:57,246 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:57,697 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:58,108 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:58,511 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:58,909 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:59,352 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:59,753 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:00,153 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:00,552 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 2)
2025-06-06 21:18:00,951 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:01,356 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:01,760 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:02,162 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:02,562 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:02,963 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:03,365 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:03,766 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:04,172 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:05,007 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:05,420 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:05,428 INFO apps.config.signals signals:log_user_deletion:21 - Usuário deletado: <EMAIL> (ID: 1)
2025-06-06 21:18:05,833 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 2)
2025-06-06 21:18:06,242 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:06,650 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:07,062 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:07,063 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:18:07,472 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:07,883 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:08,298 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:08,718 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:09,129 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:09,540 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:09,952 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:10,362 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:10,365 INFO apps.accounts.auth_service logging_utils:log_operation_start:23 - [accounts.auth_service] Iniciando operação: authenticate_user
2025-06-06 21:18:10,365 INFO havoc.security logging_utils:log_login_attempt:248 - Login attempt: <EMAIL> - success
2025-06-06 21:18:10,365 INFO apps.accounts.auth_service logging_utils:log_operation_success:39 - [accounts.auth_service] Operação concluída com sucesso: authenticate_user
2025-06-06 21:18:10,776 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:10,777 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:18:10,778 INFO apps.accounts.auth_service logging_utils:log_operation_start:23 - [accounts.auth_service] Iniciando operação: authenticate_user
2025-06-06 21:18:10,778 INFO havoc.security logging_utils:log_login_attempt:248 - Login attempt: <EMAIL> - failed
2025-06-06 21:18:10,778 WARNING havoc.security logging_utils:log_login_attempt:260 - Failed login attempt for: <EMAIL>
2025-06-06 21:18:10,778 ERROR apps.accounts.auth_service logging_utils:log_operation_error:56 - [accounts.auth_service] Erro na operação: authenticate_user - Este usuário não está verificado. Por favor, verifique seu e-mail.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Projetos\havoc\apps\core\utils\logging_utils.py", line 136, in wrapper
    result = func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\apps\accounts\services\auth_service.py", line 34, in authenticate_user
    raise ValueError("Este usuário não está verificado. Por favor, verifique seu e-mail.")
ValueError: Este usuário não está verificado. Por favor, verifique seu e-mail.
2025-06-06 21:18:11,187 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:11,188 INFO apps.accounts.auth_service logging_utils:log_operation_start:23 - [accounts.auth_service] Iniciando operação: authenticate_user
2025-06-06 21:18:11,188 INFO havoc.security logging_utils:log_login_attempt:248 - Login attempt: <EMAIL> - failed
2025-06-06 21:18:11,188 WARNING havoc.security logging_utils:log_login_attempt:260 - Failed login attempt for: <EMAIL>
2025-06-06 21:18:11,188 INFO apps.accounts.auth_service logging_utils:log_operation_success:39 - [accounts.auth_service] Operação concluída com sucesso: authenticate_user
2025-06-06 21:18:11,599 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:11,600 INFO apps.accounts.auth_service logging_utils:log_operation_start:23 - [accounts.auth_service] Iniciando operação: authenticate_user
2025-06-06 21:18:11,600 INFO havoc.security logging_utils:log_login_attempt:248 - Login attempt: <EMAIL> - failed
2025-06-06 21:18:11,600 WARNING havoc.security logging_utils:log_login_attempt:260 - Failed login attempt for: <EMAIL>
2025-06-06 21:18:11,601 INFO apps.accounts.auth_service logging_utils:log_operation_success:39 - [accounts.auth_service] Operação concluída com sucesso: authenticate_user (0.001s)
2025-06-06 21:18:11,626 INFO apps.accounts.services.image_service image_service:resize_avatar:38 - Avatar redimensionado: C:\Users\<USER>\AppData\Local\Temp\tmphpfvmqhq\test_image.jpg
2025-06-06 21:18:11,635 INFO apps.accounts.services.registration_service registration_service:register_user:36 - Iniciando registro para o email: <EMAIL>
2025-06-06 21:18:11,636 INFO apps.accounts.services.registration_service registration_service:register_user:47 - Novo registro para email: <EMAIL>
2025-06-06 21:18:11,636 INFO apps.accounts.services.registration_service registration_service:register_user:36 - Iniciando registro para o email: <EMAIL>
2025-06-06 21:18:11,637 INFO apps.accounts.services.registration_service registration_service:register_user:47 - Novo registro para email: <EMAIL>
2025-06-06 21:18:12,044 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:12,462 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:12,878 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:13,292 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:13,701 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:14,110 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:14,516 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:14,921 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:15,329 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:15,740 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:16,148 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:16,560 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:16,968 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:17,379 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:17,788 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 2)
2025-06-06 21:18:18,188 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:18,596 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:19,002 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:19,405 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:19,814 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:20,222 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:20,630 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:21,080 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:21,488 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:18:21,941 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:21,945 WARNING django.request log:log_response:253 - Not Found: /config/dashboard/
2025-06-06 21:18:22,347 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:22,766 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:22,767 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:18:23,253 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:23,669 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
