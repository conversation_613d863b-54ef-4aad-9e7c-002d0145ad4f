2025-06-06 21:17:14,100 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:17:27,666 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\core\settings.py changed, reloading.
2025-06-06 21:17:28,116 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:17:38,183 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:38,601 INFO apps.config.signals signals:log_user_creation:14 - Novo usu<PERSON>rio criado: <EMAIL> (ID: 1)
2025-06-06 21:17:39,419 INFO apps.config.signals signals:log_user_creation:14 - Novo usu<PERSON>rio criado: <EMAIL> (ID: 1)
2025-06-06 21:17:40,223 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:40,629 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:41,029 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:41,428 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:41,832 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:42,232 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:42,631 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:43,039 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:43,438 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 2)
2025-06-06 21:17:43,839 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:44,240 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:44,641 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:45,046 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:45,444 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:45,848 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:46,248 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:46,657 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:55,102 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:55,607 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:56,433 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:57,246 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:57,697 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:58,108 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:58,511 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:58,909 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:59,352 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:17:59,753 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:00,153 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:00,552 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 2)
2025-06-06 21:18:00,951 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:01,356 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:01,760 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:02,162 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:02,562 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:02,963 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:03,365 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:03,766 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:04,172 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:05,007 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:05,420 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:05,428 INFO apps.config.signals signals:log_user_deletion:21 - Usuário deletado: <EMAIL> (ID: 1)
2025-06-06 21:18:05,833 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 2)
2025-06-06 21:18:06,242 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:06,650 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:07,062 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:07,063 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:18:07,472 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:07,883 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:08,298 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:08,718 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:09,129 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:09,540 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:09,952 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:10,362 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:10,365 INFO apps.accounts.auth_service logging_utils:log_operation_start:23 - [accounts.auth_service] Iniciando operação: authenticate_user
2025-06-06 21:18:10,365 INFO havoc.security logging_utils:log_login_attempt:248 - Login attempt: <EMAIL> - success
2025-06-06 21:18:10,365 INFO apps.accounts.auth_service logging_utils:log_operation_success:39 - [accounts.auth_service] Operação concluída com sucesso: authenticate_user
2025-06-06 21:18:10,776 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:10,777 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:18:10,778 INFO apps.accounts.auth_service logging_utils:log_operation_start:23 - [accounts.auth_service] Iniciando operação: authenticate_user
2025-06-06 21:18:10,778 INFO havoc.security logging_utils:log_login_attempt:248 - Login attempt: <EMAIL> - failed
2025-06-06 21:18:10,778 WARNING havoc.security logging_utils:log_login_attempt:260 - Failed login attempt for: <EMAIL>
2025-06-06 21:18:10,778 ERROR apps.accounts.auth_service logging_utils:log_operation_error:56 - [accounts.auth_service] Erro na operação: authenticate_user - Este usuário não está verificado. Por favor, verifique seu e-mail.
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Projetos\havoc\apps\core\utils\logging_utils.py", line 136, in wrapper
    result = func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\apps\accounts\services\auth_service.py", line 34, in authenticate_user
    raise ValueError("Este usuário não está verificado. Por favor, verifique seu e-mail.")
ValueError: Este usuário não está verificado. Por favor, verifique seu e-mail.
2025-06-06 21:18:11,187 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:11,188 INFO apps.accounts.auth_service logging_utils:log_operation_start:23 - [accounts.auth_service] Iniciando operação: authenticate_user
2025-06-06 21:18:11,188 INFO havoc.security logging_utils:log_login_attempt:248 - Login attempt: <EMAIL> - failed
2025-06-06 21:18:11,188 WARNING havoc.security logging_utils:log_login_attempt:260 - Failed login attempt for: <EMAIL>
2025-06-06 21:18:11,188 INFO apps.accounts.auth_service logging_utils:log_operation_success:39 - [accounts.auth_service] Operação concluída com sucesso: authenticate_user
2025-06-06 21:18:11,599 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:11,600 INFO apps.accounts.auth_service logging_utils:log_operation_start:23 - [accounts.auth_service] Iniciando operação: authenticate_user
2025-06-06 21:18:11,600 INFO havoc.security logging_utils:log_login_attempt:248 - Login attempt: <EMAIL> - failed
2025-06-06 21:18:11,600 WARNING havoc.security logging_utils:log_login_attempt:260 - Failed login attempt for: <EMAIL>
2025-06-06 21:18:11,601 INFO apps.accounts.auth_service logging_utils:log_operation_success:39 - [accounts.auth_service] Operação concluída com sucesso: authenticate_user (0.001s)
2025-06-06 21:18:11,626 INFO apps.accounts.services.image_service image_service:resize_avatar:38 - Avatar redimensionado: C:\Users\<USER>\AppData\Local\Temp\tmphpfvmqhq\test_image.jpg
2025-06-06 21:18:11,635 INFO apps.accounts.services.registration_service registration_service:register_user:36 - Iniciando registro para o email: <EMAIL>
2025-06-06 21:18:11,636 INFO apps.accounts.services.registration_service registration_service:register_user:47 - Novo registro para email: <EMAIL>
2025-06-06 21:18:11,636 INFO apps.accounts.services.registration_service registration_service:register_user:36 - Iniciando registro para o email: <EMAIL>
2025-06-06 21:18:11,637 INFO apps.accounts.services.registration_service registration_service:register_user:47 - Novo registro para email: <EMAIL>
2025-06-06 21:18:12,044 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:12,462 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:12,878 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:13,292 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:13,701 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:14,110 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:14,516 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:14,921 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:15,329 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:15,740 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:16,148 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:16,560 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:16,968 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:17,379 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:17,788 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 2)
2025-06-06 21:18:18,188 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:18,596 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:19,002 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:19,405 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:19,814 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:20,222 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:20,630 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:21,080 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:21,488 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:18:21,941 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:21,945 WARNING django.request log:log_response:253 - Not Found: /config/dashboard/
2025-06-06 21:18:22,347 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:22,766 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:22,767 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:18:23,253 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:18:23,669 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:25:08,997 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\core\settings.py changed, reloading.
2025-06-06 21:25:09,448 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:26:27,917 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\core\settings.py changed, reloading.
2025-06-06 21:29:24,919 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:29:36,447 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\core\settings.py changed, reloading.
2025-06-06 21:29:36,901 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:29:47,368 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:47,822 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:48,244 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:48,667 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:49,070 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:49,472 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 2)
2025-06-06 21:29:49,874 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:50,274 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 2)
2025-06-06 21:29:50,674 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:51,081 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 2)
2025-06-06 21:29:51,487 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:51,893 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 2)
2025-06-06 21:29:52,340 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:52,738 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 2)
2025-06-06 21:29:53,139 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:53,141 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /accounts/login/
2025-06-06 21:29:53,173 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /accounts/login/ - 200
2025-06-06 21:29:53,576 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:53,577 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/login/
2025-06-06 21:29:53,583 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/login/ - 200
2025-06-06 21:29:53,989 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:53,990 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/login/
2025-06-06 21:29:53,994 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/login/ - 200
2025-06-06 21:29:54,399 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:54,400 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:29:54,400 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/login/
2025-06-06 21:29:54,404 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/login/ - 200
2025-06-06 21:29:54,806 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:55,209 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:29:55,210 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/logout/
2025-06-06 21:29:55,211 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/logout/ - 302
2025-06-06 21:29:55,212 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /
2025-06-06 21:29:55,215 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET / - 200
2025-06-06 21:29:55,621 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:56,025 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:56,444 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:56,861 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:57,273 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:57,685 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:57,686 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /accounts/redefinir-senha/
2025-06-06 21:29:57,688 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /accounts/redefinir-senha/ - 200
2025-06-06 21:29:58,095 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:58,096 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/redefinir-senha/
2025-06-06 21:29:58,099 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/redefinir-senha/ - 200
2025-06-06 21:29:58,506 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:58,507 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:29:58,507 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/redefinir-senha/
2025-06-06 21:29:58,509 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/redefinir-senha/ - 200
2025-06-06 21:29:58,909 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:58,911 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/redefinir-senha/
2025-06-06 21:29:58,926 INFO apps.accounts.services.email_service email_service:send_password_reset_code:157 - Código de redefinição de senha enviado para: <EMAIL>
2025-06-06 21:29:58,927 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/redefinir-senha/ - 302
2025-06-06 21:29:59,329 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:29:59,330 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /accounts/perfil/
2025-06-06 21:29:59,331 ERROR django.request log:log_response:253 - Internal Server Error: /accounts/perfil/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\core\handlers\base.py", line 185, in _get_response
    response = middleware_method(
               ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\apps\accounts\middleware.py", line 331, in process_view
    return self.redirect_to_smart_login(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\apps\accounts\middleware.py", line 352, in redirect_to_smart_login
    area_info = self._get_area_info_static(request.path)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SmartRedirectMiddleware' object has no attribute '_get_area_info_static'
2025-06-06 21:29:59,332 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /accounts/perfil/ - 500
2025-06-06 21:29:59,736 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:30:00,140 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:30:00,141 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /accounts/perfil/
2025-06-06 21:30:00,146 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /accounts/perfil/ - 200
2025-06-06 21:30:00,548 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:30:00,950 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:30:00,951 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/configuracoes/
2025-06-06 21:30:00,953 INFO havoc.audit logging_middleware:process_response:254 - Atividade do usuário: POST /accounts/configuracoes/
2025-06-06 21:30:00,953 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/configuracoes/ - 302
2025-06-06 21:30:01,358 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:30:01,761 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:30:01,762 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/configuracoes/
2025-06-06 21:30:01,764 INFO havoc.audit logging_middleware:process_response:254 - Atividade do usuário: POST /accounts/configuracoes/
2025-06-06 21:30:01,764 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/configuracoes/ - 302
2025-06-06 21:30:02,166 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:30:02,570 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:30:02,572 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /accounts/configuracoes/
2025-06-06 21:30:02,587 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /accounts/configuracoes/ - 200
2025-06-06 21:30:02,588 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /accounts/registro/
2025-06-06 21:30:02,595 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /accounts/registro/ - 200
2025-06-06 21:30:02,597 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/registro/
2025-06-06 21:30:02,629 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/registro/ - 200
2025-06-06 21:30:02,630 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/registro/
2025-06-06 21:30:02,637 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/registro/ - 200
2025-06-06 21:30:02,640 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/registro/
2025-06-06 21:30:02,648 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/registro/ - 200
2025-06-06 21:30:03,049 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:30:03,452 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:30:03,855 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:30:04,255 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:30:13,264 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #1
2025-06-06 21:30:13,264 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #2
2025-06-06 21:30:13,264 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #3
2025-06-06 21:30:13,264 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #4
2025-06-06 21:30:13,265 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #5
2025-06-06 21:30:13,265 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #6
2025-06-06 21:30:13,265 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #7
2025-06-06 21:30:13,265 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #8
2025-06-06 21:30:13,265 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #9
2025-06-06 21:30:13,266 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #10
2025-06-06 21:30:13,266 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #11
2025-06-06 21:30:13,266 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #12
2025-06-06 21:30:13,266 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #13
2025-06-06 21:30:13,266 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #14
2025-06-06 21:30:13,266 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #15
2025-06-06 21:30:13,267 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #1
2025-06-06 21:30:13,267 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #2
2025-06-06 21:30:13,268 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #3
2025-06-06 21:30:13,268 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #4
2025-06-06 21:30:13,268 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #5
2025-06-06 21:30:13,268 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #6
2025-06-06 21:30:13,268 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #7
2025-06-06 21:30:13,268 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #8
2025-06-06 21:30:13,268 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #9
2025-06-06 21:30:13,268 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #10
2025-06-06 21:30:13,268 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #11
2025-06-06 21:30:13,269 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #12
2025-06-06 21:30:13,281 WARNING havoc.alerts alert_system:_trigger_alert:183 - ALERTA DISPARADO: Test alert
2025-06-06 21:30:33,067 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\core\settings.py changed, reloading.
2025-06-06 21:30:33,537 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:30:42,523 ERROR havoc.test_alerts test_alerts:_generate_event:66 - Erro de teste #1 - Simulação de erro para teste de alertas
2025-06-06 21:30:43,024 ERROR havoc.test_alerts test_alerts:_generate_event:66 - Erro de teste #2 - Simulação de erro para teste de alertas
2025-06-06 21:30:43,525 ERROR havoc.test_alerts test_alerts:_generate_event:66 - Erro de teste #3 - Simulação de erro para teste de alertas
2025-06-06 21:30:44,026 ERROR havoc.test_alerts test_alerts:_generate_event:66 - Erro de teste #4 - Simulação de erro para teste de alertas
2025-06-06 21:30:44,527 ERROR havoc.test_alerts test_alerts:_generate_event:66 - Erro de teste #5 - Simulação de erro para teste de alertas
2025-06-06 21:30:45,027 ERROR havoc.test_alerts test_alerts:_generate_event:66 - Erro de teste #6 - Simulação de erro para teste de alertas
2025-06-06 21:30:45,528 ERROR havoc.test_alerts test_alerts:_generate_event:66 - Erro de teste #7 - Simulação de erro para teste de alertas
2025-06-06 21:30:46,029 ERROR havoc.test_alerts test_alerts:_generate_event:66 - Erro de teste #8 - Simulação de erro para teste de alertas
2025-06-06 21:30:46,530 ERROR havoc.test_alerts test_alerts:_generate_event:66 - Erro de teste #9 - Simulação de erro para teste de alertas
2025-06-06 21:30:47,031 ERROR havoc.test_alerts test_alerts:_generate_event:66 - Erro de teste #10 - Simulação de erro para teste de alertas
2025-06-06 21:30:47,531 ERROR havoc.test_alerts test_alerts:_generate_event:66 - Erro de teste #11 - Simulação de erro para teste de alertas
2025-06-06 21:30:48,033 ERROR havoc.test_alerts test_alerts:_generate_event:66 - Erro de teste #12 - Simulação de erro para teste de alertas
2025-06-06 21:30:56,443 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:30:56,912 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:30:57,726 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:30:58,537 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:30:58,997 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:30:59,404 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:30:59,821 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:00,228 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:00,676 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:01,080 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:01,490 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:01,896 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 2)
2025-06-06 21:31:02,307 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:02,721 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:03,131 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:03,533 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:03,927 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:04,327 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:04,727 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:05,128 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:05,535 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:06,367 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:06,783 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:06,791 INFO apps.config.signals signals:log_user_deletion:21 - Usuário deletado: <EMAIL> (ID: 1)
2025-06-06 21:31:07,195 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 2)
2025-06-06 21:31:07,607 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:08,016 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:08,431 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:08,431 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:31:08,838 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:09,246 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:09,655 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:10,072 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:10,494 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:10,904 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:11,309 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:11,717 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:11,720 INFO apps.accounts.auth_service logging_utils:log_operation_start:23 - [accounts.auth_service] Iniciando operação: authenticate_user
2025-06-06 21:31:11,720 INFO havoc.security logging_utils:log_login_attempt:248 - Login attempt: <EMAIL> - success
2025-06-06 21:31:11,720 INFO apps.accounts.auth_service logging_utils:log_operation_success:39 - [accounts.auth_service] Operação concluída com sucesso: authenticate_user
2025-06-06 21:31:12,126 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:12,127 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:31:12,127 INFO apps.accounts.auth_service logging_utils:log_operation_start:23 - [accounts.auth_service] Iniciando operação: authenticate_user
2025-06-06 21:31:12,128 INFO havoc.security logging_utils:log_login_attempt:248 - Login attempt: <EMAIL> - failed
2025-06-06 21:31:12,128 WARNING havoc.security logging_utils:log_login_attempt:260 - Failed login attempt for: <EMAIL>
2025-06-06 21:31:12,128 ERROR apps.accounts.auth_service logging_utils:log_operation_error:56 - [accounts.auth_service] Erro na operação: authenticate_user - Este usuário não está verificado. Por favor, verifique seu e-mail. (0.001s)
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Projetos\havoc\apps\core\utils\logging_utils.py", line 136, in wrapper
    result = func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\apps\accounts\services\auth_service.py", line 34, in authenticate_user
    raise ValueError("Este usuário não está verificado. Por favor, verifique seu e-mail.")
ValueError: Este usuário não está verificado. Por favor, verifique seu e-mail.
2025-06-06 21:31:12,536 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:12,536 INFO apps.accounts.auth_service logging_utils:log_operation_start:23 - [accounts.auth_service] Iniciando operação: authenticate_user
2025-06-06 21:31:12,537 INFO havoc.security logging_utils:log_login_attempt:248 - Login attempt: <EMAIL> - failed
2025-06-06 21:31:12,537 WARNING havoc.security logging_utils:log_login_attempt:260 - Failed login attempt for: <EMAIL>
2025-06-06 21:31:12,537 INFO apps.accounts.auth_service logging_utils:log_operation_success:39 - [accounts.auth_service] Operação concluída com sucesso: authenticate_user (0.001s)
2025-06-06 21:31:12,941 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:12,943 INFO apps.accounts.auth_service logging_utils:log_operation_start:23 - [accounts.auth_service] Iniciando operação: authenticate_user
2025-06-06 21:31:12,943 INFO havoc.security logging_utils:log_login_attempt:248 - Login attempt: <EMAIL> - failed
2025-06-06 21:31:12,943 WARNING havoc.security logging_utils:log_login_attempt:260 - Failed login attempt for: <EMAIL>
2025-06-06 21:31:12,944 INFO apps.accounts.auth_service logging_utils:log_operation_success:39 - [accounts.auth_service] Operação concluída com sucesso: authenticate_user (0.001s)
2025-06-06 21:31:12,969 INFO apps.accounts.services.image_service image_service:resize_avatar:38 - Avatar redimensionado: C:\Users\<USER>\AppData\Local\Temp\tmpjv5r__8_\test_image.jpg
2025-06-06 21:31:12,987 INFO apps.accounts.services.registration_service registration_service:register_user:36 - Iniciando registro para o email: <EMAIL>
2025-06-06 21:31:12,987 INFO apps.accounts.services.registration_service registration_service:register_user:47 - Novo registro para email: <EMAIL>
2025-06-06 21:31:12,988 INFO apps.accounts.services.registration_service registration_service:register_user:36 - Iniciando registro para o email: <EMAIL>
2025-06-06 21:31:12,988 INFO apps.accounts.services.registration_service registration_service:register_user:47 - Novo registro para email: <EMAIL>
2025-06-06 21:31:13,392 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:13,810 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:14,224 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:14,638 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:15,043 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:15,448 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:15,851 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:16,255 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:16,660 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:17,066 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:17,471 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:17,877 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:18,284 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:18,690 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:19,095 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 2)
2025-06-06 21:31:19,496 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:19,908 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:20,316 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:20,720 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:21,121 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:21,525 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:21,929 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:21,933 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/redefinir-senha/
2025-06-06 21:31:21,979 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/redefinir-senha/ - 200
2025-06-06 21:31:22,379 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:22,784 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:31:22,786 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /accounts/perfil/
2025-06-06 21:31:22,795 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /accounts/perfil/ - 200
2025-06-06 21:31:22,796 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /accounts/configuracoes/
2025-06-06 21:31:22,835 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /accounts/configuracoes/ - 200
2025-06-06 21:31:22,836 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/configuracoes/
2025-06-06 21:31:22,839 INFO havoc.audit logging_middleware:process_response:254 - Atividade do usuário: POST /accounts/configuracoes/
2025-06-06 21:31:22,839 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/configuracoes/ - 302
2025-06-06 21:31:23,236 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:23,237 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /config/dashboard/
2025-06-06 21:31:23,240 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /config/dashboard/ - 404
2025-06-06 21:31:23,241 WARNING django.request log:log_response:253 - Not Found: /config/dashboard/
2025-06-06 21:31:23,642 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:23,643 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /accounts/login/
2025-06-06 21:31:23,652 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /accounts/login/ - 200
2025-06-06 21:31:23,653 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/login/
2025-06-06 21:31:23,660 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/login/ - 200
2025-06-06 21:31:24,070 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:24,071 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:31:24,073 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/login/
2025-06-06 21:31:24,080 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/login/ - 200
2025-06-06 21:31:24,083 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /accounts/registro/
2025-06-06 21:31:24,093 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /accounts/registro/ - 200
2025-06-06 21:31:24,094 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/registro/
2025-06-06 21:31:24,157 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/registro/ - 200
2025-06-06 21:31:24,561 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:24,563 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/registro/
2025-06-06 21:31:24,575 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/registro/ - 200
2025-06-06 21:31:24,975 INFO apps.config.signals signals:log_user_creation:14 - Novo usuário criado: <EMAIL> (ID: 1)
2025-06-06 21:31:24,976 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/registro/
2025-06-06 21:31:24,988 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/registro/ - 200
2025-06-06 21:31:24,992 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #1
2025-06-06 21:31:24,992 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #2
2025-06-06 21:31:24,993 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #3
2025-06-06 21:31:24,993 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #4
2025-06-06 21:31:24,993 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #5
2025-06-06 21:31:24,993 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #6
2025-06-06 21:31:24,993 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #7
2025-06-06 21:31:24,993 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #8
2025-06-06 21:31:24,994 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #9
2025-06-06 21:31:24,994 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #10
2025-06-06 21:31:24,994 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #11
2025-06-06 21:31:24,994 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #12
2025-06-06 21:31:24,994 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #13
2025-06-06 21:31:24,994 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #14
2025-06-06 21:31:24,994 ERROR havoc.cooldown_test test_alerts:test_alert_cooldown:372 - Erro de cooldown #15
2025-06-06 21:31:24,995 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #1
2025-06-06 21:31:24,995 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #2
2025-06-06 21:31:24,995 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #3
2025-06-06 21:31:24,995 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #4
2025-06-06 21:31:24,995 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #5
2025-06-06 21:31:24,995 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #6
2025-06-06 21:31:24,996 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #7
2025-06-06 21:31:24,996 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #8
2025-06-06 21:31:24,996 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #9
2025-06-06 21:31:24,996 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #10
2025-06-06 21:31:24,996 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #11
2025-06-06 21:31:24,996 ERROR havoc.integration_test test_alerts:test_full_alert_flow:351 - Erro de integração #12
2025-06-06 21:31:25,013 WARNING havoc.alerts alert_system:_trigger_alert:183 - ALERTA DISPARADO: Test alert
2025-06-06 21:36:45,104 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\views\article_views.py changed, reloading.
2025-06-06 21:36:45,540 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:37:08,225 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\views\article_views.py changed, reloading.
2025-06-06 21:37:08,661 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:37:29,343 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\views\article_views.py changed, reloading.
2025-06-06 21:37:29,786 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:37:50,453 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\views\article_views.py changed, reloading.
2025-06-06 21:37:50,900 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:38:15,628 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\views\article_views.py changed, reloading.
2025-06-06 21:38:16,093 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:44:35,153 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\views\article_views.py changed, reloading.
2025-06-06 21:44:35,598 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:45:04,645 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:45:41,353 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /artigos/
2025-06-06 21:45:41,356 ERROR havoc.requests logging_middleware:process_exception:98 - Exceção no request: GET /artigos/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\views\generic\list.py", line 158, in get
    self.object_list = self.get_queryset()
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\views\article_views.py", line 30, in get_queryset
    queryset = Article.objects.published().select_related(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Manager' object has no attribute 'published'
2025-06-06 21:45:41,385 ERROR django.request log:log_response:253 - Internal Server Error: /artigos/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\views\generic\list.py", line 158, in get
    self.object_list = self.get_queryset()
                       ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\views\article_views.py", line 30, in get_queryset
    queryset = Article.objects.published().select_related(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Manager' object has no attribute 'published'
2025-06-06 21:45:41,386 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /artigos/ - 500
2025-06-06 21:45:41,386 ERROR django.server basehttp:log_message:213 - "GET /artigos/ HTTP/1.1" 500 80623
2025-06-06 21:45:56,778 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\models\article.py changed, reloading.
2025-06-06 21:45:57,246 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:45:57,322 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\models\article.py changed, reloading.
2025-06-06 21:45:57,794 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:46:09,330 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\models\article.py changed, reloading.
2025-06-06 21:46:09,793 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\models\article.py changed, reloading.
2025-06-06 21:46:09,808 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:46:10,321 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:46:21,859 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\views\article_views.py changed, reloading.
2025-06-06 21:46:22,304 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:46:22,393 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\views\article_views.py changed, reloading.
2025-06-06 21:46:22,855 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:46:38,435 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\models\category.py changed, reloading.
2025-06-06 21:46:38,900 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:46:38,903 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\models\category.py changed, reloading.
2025-06-06 21:46:39,372 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:46:50,907 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\models\category.py changed, reloading.
2025-06-06 21:46:51,355 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:46:51,469 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\models\category.py changed, reloading.
2025-06-06 21:46:51,963 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:47:09,586 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\models\comment.py changed, reloading.
2025-06-06 21:47:09,994 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\models\comment.py changed, reloading.
2025-06-06 21:47:10,041 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:47:10,463 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:47:20,563 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\models\comment.py changed, reloading.
2025-06-06 21:47:20,973 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\articles\models\comment.py changed, reloading.
2025-06-06 21:47:21,019 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:47:21,438 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:47:36,241 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:47:55,108 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /artigos/
2025-06-06 21:47:55,128 ERROR havoc.requests logging_middleware:process_exception:98 - Exceção no request: GET /artigos/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'article_list' not found. 'article_list' is not a valid view function or pattern name.
2025-06-06 21:47:55,218 ERROR django.request log:log_response:253 - Internal Server Error: /artigos/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'article_list' not found. 'article_list' is not a valid view function or pattern name.
2025-06-06 21:47:55,221 INFO havoc.performance logging_middleware:process_response:205 - Database queries: 98 queries executadas
2025-06-06 21:47:55,221 WARNING havoc.performance logging_middleware:process_response:217 - Muitas queries detectadas: 98
2025-06-06 21:47:55,221 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /artigos/ - 500
2025-06-06 21:47:55,222 ERROR django.server basehttp:log_message:213 - "GET /artigos/ HTTP/1.1" 500 168435
2025-06-06 21:47:55,222 INFO django.server basehttp:handle_error:81 - - Broken pipe from ('127.0.0.1', 59353)
2025-06-06 21:48:21,770 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /artigos/
2025-06-06 21:48:21,784 INFO havoc.performance logging_middleware:process_response:205 - Database queries: 6 queries executadas
2025-06-06 21:48:21,784 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /artigos/ - 200
2025-06-06 21:48:21,785 INFO django.server basehttp:log_message:213 - "GET /artigos/ HTTP/1.1" 200 14115
2025-06-06 21:48:26,881 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /artigos/
2025-06-06 21:48:26,889 INFO havoc.performance logging_middleware:process_response:205 - Database queries: 6 queries executadas
2025-06-06 21:48:26,889 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /artigos/ - 200
2025-06-06 21:48:26,889 INFO django.server basehttp:log_message:213 - "GET /artigos/ HTTP/1.1" 200 14115
2025-06-06 21:48:26,947 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /media/articles/images/alice.jpg
2025-06-06 21:48:26,949 INFO django.server basehttp:log_message:213 - "GET /static/css/main.css HTTP/1.1" 304 0
2025-06-06 21:48:26,949 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /media/articles/images/alice.jpg - 304
2025-06-06 21:48:26,949 INFO django.server basehttp:log_message:213 - "GET /media/articles/images/alice.jpg HTTP/1.1" 304 0
2025-06-06 21:48:26,953 INFO django.server basehttp:log_message:213 - "GET /static/js/main.js HTTP/1.1" 304 0
2025-06-06 21:48:35,447 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /artigos/categoria/animes/
2025-06-06 21:48:35,458 INFO havoc.performance logging_middleware:process_response:205 - Database queries: 5 queries executadas
2025-06-06 21:48:35,459 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /artigos/categoria/animes/ - 200
2025-06-06 21:48:35,459 INFO django.server basehttp:log_message:213 - "GET /artigos/categoria/animes/ HTTP/1.1" 200 14039
2025-06-06 21:48:41,847 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /
2025-06-06 21:48:41,852 INFO havoc.performance logging_middleware:process_response:205 - Database queries: 2 queries executadas
2025-06-06 21:48:41,852 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET / - 200
2025-06-06 21:48:41,853 INFO django.server basehttp:log_message:213 - "GET / HTTP/1.1" 200 7741
2025-06-06 21:48:43,006 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /artigos/
2025-06-06 21:48:43,013 INFO havoc.performance logging_middleware:process_response:205 - Database queries: 6 queries executadas
2025-06-06 21:48:43,013 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /artigos/ - 200
2025-06-06 21:48:43,014 INFO django.server basehttp:log_message:213 - "GET /artigos/ HTTP/1.1" 200 14115
2025-06-06 21:48:46,350 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /artigos/categoria/animes/
2025-06-06 21:48:46,357 INFO havoc.performance logging_middleware:process_response:205 - Database queries: 5 queries executadas
2025-06-06 21:48:46,357 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /artigos/categoria/animes/ - 200
2025-06-06 21:48:46,358 INFO django.server basehttp:log_message:213 - "GET /artigos/categoria/animes/ HTTP/1.1" 200 14039
2025-06-06 21:48:51,246 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /artigos/testew/
2025-06-06 21:48:51,256 INFO apps.articles.views article_views:get_object:121 - Artigo visualizado: testew (Views: 7)
2025-06-06 21:48:51,263 INFO havoc.performance logging_middleware:process_response:205 - Database queries: 6 queries executadas
2025-06-06 21:48:51,263 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /artigos/testew/ - 200
2025-06-06 21:48:51,264 INFO django.server basehttp:log_message:213 - "GET /artigos/testew/ HTTP/1.1" 200 13163
2025-06-06 21:49:32,352 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\config\views\dashboard.py changed, reloading.
2025-06-06 21:49:32,534 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\config\views\dashboard.py changed, reloading.
2025-06-06 21:49:32,839 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:49:33,024 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:50:00,507 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\config\views\dashboard.py changed, reloading.
2025-06-06 21:50:00,559 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\config\views\dashboard.py changed, reloading.
2025-06-06 21:50:00,992 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:50:01,047 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:53:40,104 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\config\views\dashboard.py changed, reloading.
2025-06-06 21:53:40,566 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:53:50,088 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\config\views\dashboard.py changed, reloading.
2025-06-06 21:53:50,550 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:54:02,098 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\config\views\system_config_views.py changed, reloading.
2025-06-06 21:54:02,614 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:54:16,200 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\config\views\system_config_views.py changed, reloading.
2025-06-06 21:54:16,657 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:54:30,232 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\config\views\system_config_views.py changed, reloading.
2025-06-06 21:54:30,678 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:54:41,240 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\config\views\system_config_views.py changed, reloading.
2025-06-06 21:54:41,704 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:54:52,234 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\config\views\system_config_views.py changed, reloading.
2025-06-06 21:54:52,717 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:54:53,401 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /accounts/login/
2025-06-06 21:54:53,441 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /accounts/login/ - 200
2025-06-06 21:54:53,441 INFO django.server basehttp:log_message:213 - "GET /accounts/login/ HTTP/1.1" 200 11825
2025-06-06 21:54:56,533 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/login/
2025-06-06 21:54:56,970 INFO havoc.performance logging_middleware:process_response:205 - Database queries: 1 queries executadas
2025-06-06 21:54:56,970 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/login/ - 200
2025-06-06 21:54:56,971 INFO django.server basehttp:log_message:213 - "POST /accounts/login/ HTTP/1.1" 200 12000
2025-06-06 21:55:03,830 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /accounts/redefinir-senha/
2025-06-06 21:55:03,833 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /accounts/redefinir-senha/ - 200
2025-06-06 21:55:03,834 INFO django.server basehttp:log_message:213 - "GET /accounts/redefinir-senha/ HTTP/1.1" 200 6819
2025-06-06 21:55:04,266 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\config\views\system_config_views.py changed, reloading.
2025-06-06 21:55:04,736 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:55:04,891 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /accounts/login/
2025-06-06 21:55:04,937 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /accounts/login/ - 200
2025-06-06 21:55:04,937 INFO django.server basehttp:log_message:213 - "GET /accounts/login/ HTTP/1.1" 200 11825
2025-06-06 21:55:07,170 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /accounts/registro/
2025-06-06 21:55:07,177 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /accounts/registro/ - 200
2025-06-06 21:55:07,178 INFO django.server basehttp:log_message:213 - "GET /accounts/registro/ HTTP/1.1" 200 12714
2025-06-06 21:55:14,281 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /accounts/login/
2025-06-06 21:55:14,285 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /accounts/login/ - 200
2025-06-06 21:55:14,286 INFO django.server basehttp:log_message:213 - "GET /accounts/login/ HTTP/1.1" 200 11825
2025-06-06 21:55:15,289 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /artigos/
2025-06-06 21:55:15,304 INFO havoc.performance logging_middleware:process_response:205 - Database queries: 6 queries executadas
2025-06-06 21:55:15,304 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /artigos/ - 200
2025-06-06 21:55:15,304 INFO django.server basehttp:log_message:213 - "GET /artigos/ HTTP/1.1" 200 14115
2025-06-06 21:55:16,332 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\config\views\system_config_views.py changed, reloading.
2025-06-06 21:55:16,787 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:55:26,683 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /artigos/
2025-06-06 21:55:26,723 INFO havoc.performance logging_middleware:process_response:205 - Database queries: 6 queries executadas
2025-06-06 21:55:26,723 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /artigos/ - 200
2025-06-06 21:55:26,724 INFO django.server basehttp:log_message:213 - "GET /artigos/?order=created_at HTTP/1.1" 200 14115
2025-06-06 21:55:27,486 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\config\views\system_config_views.py changed, reloading.
2025-06-06 21:55:27,969 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:55:28,508 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /artigos/
2025-06-06 21:55:28,546 INFO havoc.performance logging_middleware:process_response:205 - Database queries: 6 queries executadas
2025-06-06 21:55:28,546 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /artigos/ - 200
2025-06-06 21:55:28,547 INFO django.server basehttp:log_message:213 - "GET /artigos/?order=-title HTTP/1.1" 200 14115
2025-06-06 21:55:30,539 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /artigos/
2025-06-06 21:55:30,547 INFO havoc.performance logging_middleware:process_response:205 - Database queries: 6 queries executadas
2025-06-06 21:55:30,547 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /artigos/ - 200
2025-06-06 21:55:30,548 INFO django.server basehttp:log_message:213 - "GET /artigos/?order=-created_at HTTP/1.1" 200 14115
2025-06-06 21:55:35,849 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /artigos/categoria/animes/
2025-06-06 21:55:35,858 INFO havoc.performance logging_middleware:process_response:205 - Database queries: 5 queries executadas
2025-06-06 21:55:35,859 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /artigos/categoria/animes/ - 200
2025-06-06 21:55:35,859 INFO django.server basehttp:log_message:213 - "GET /artigos/categoria/animes/ HTTP/1.1" 200 14039
2025-06-06 21:55:39,977 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /artigos/
2025-06-06 21:55:39,987 INFO havoc.performance logging_middleware:process_response:205 - Database queries: 6 queries executadas
2025-06-06 21:55:39,987 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /artigos/ - 200
2025-06-06 21:55:39,987 INFO django.server basehttp:log_message:213 - "GET /artigos/ HTTP/1.1" 200 14115
2025-06-06 21:55:42,549 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\config\views\system_config_views.py changed, reloading.
2025-06-06 21:55:43,018 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:55:56,122 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:56:16,216 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /config/
2025-06-06 21:56:16,243 ERROR django.request log:log_response:253 - Internal Server Error: /config/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\core\handlers\base.py", line 185, in _get_response
    response = middleware_method(
               ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\apps\accounts\middleware.py", line 331, in process_view
    return self.redirect_to_smart_login(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\apps\accounts\middleware.py", line 352, in redirect_to_smart_login
    area_info = self._get_area_info_static(request.path)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SmartRedirectMiddleware' object has no attribute '_get_area_info_static'
2025-06-06 21:56:16,244 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /config/ - 500
2025-06-06 21:56:16,245 ERROR django.server basehttp:log_message:213 - "GET /config/ HTTP/1.1" 500 71176
2025-06-06 21:56:41,385 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\accounts\middleware.py changed, reloading.
2025-06-06 21:56:41,851 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:56:42,154 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\accounts\middleware.py changed, reloading.
2025-06-06 21:56:42,610 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:56:53,669 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /config/
2025-06-06 21:56:53,679 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /config/ - 302
2025-06-06 21:56:53,680 INFO django.server basehttp:log_message:213 - "GET /config/ HTTP/1.1" 302 0
2025-06-06 21:56:53,680 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /accounts/login/
2025-06-06 21:56:53,710 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /accounts/login/ - 200
2025-06-06 21:56:53,711 INFO django.server basehttp:log_message:213 - "GET /accounts/login/?next=/config/ HTTP/1.1" 200 12334
2025-06-06 21:56:59,880 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /config/
2025-06-06 21:56:59,882 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /config/ - 302
2025-06-06 21:56:59,882 INFO django.server basehttp:log_message:213 - "GET /config/ HTTP/1.1" 302 0
2025-06-06 21:56:59,903 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /accounts/login/
2025-06-06 21:56:59,907 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /accounts/login/ - 200
2025-06-06 21:56:59,908 INFO django.server basehttp:log_message:213 - "GET /accounts/login/?next=/config/ HTTP/1.1" 200 12334
2025-06-06 21:56:59,941 INFO django.server basehttp:log_message:213 - "GET /static/css/main.css HTTP/1.1" 304 0
2025-06-06 21:56:59,941 INFO django.server basehttp:log_message:213 - "GET /static/js/main.js HTTP/1.1" 304 0
2025-06-06 21:57:11,841 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: POST /accounts/login/
2025-06-06 21:57:12,304 INFO apps.config.signals signals:log_user_creation:16 - Usuário atualizado: <EMAIL> (ID: 1)
2025-06-06 21:57:12,305 INFO havoc.performance logging_middleware:process_response:205 - Database queries: 6 queries executadas
2025-06-06 21:57:12,305 INFO havoc.audit logging_middleware:process_response:254 - Atividade do usuário: POST /accounts/login/
2025-06-06 21:57:12,312 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: POST /accounts/login/ - 302
2025-06-06 21:57:12,312 INFO django.server basehttp:log_message:213 - "POST /accounts/login/?next=/config/ HTTP/1.1" 302 0
2025-06-06 21:57:12,315 INFO havoc.requests logging_middleware:process_request:31 - Request iniciado: GET /config/
2025-06-06 21:57:12,325 INFO apps.config.views dashboard:get:149 - Dashboard acessado por yurymenezes
2025-06-06 21:57:12,329 ERROR havoc.requests logging_middleware:process_exception:98 - Exceção no request: GET /config/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\apps\config\mixins.py", line 181, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\contrib\auth\mixins.py", line 73, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\contrib\auth\mixins.py", line 135, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\apps\config\views\dashboard.py", line 151, in get
    return render(request, self.template_name, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'user_management' not found. 'user_management' is not a valid view function or pattern name.
2025-06-06 21:57:12,383 ERROR django.request log:log_response:253 - Internal Server Error: /config/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\apps\config\mixins.py", line 181, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\contrib\auth\mixins.py", line 73, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\contrib\auth\mixins.py", line 135, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\apps\config\views\dashboard.py", line 151, in get
    return render(request, self.template_name, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\Projetos\havoc\env\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'user_management' not found. 'user_management' is not a valid view function or pattern name.
2025-06-06 21:57:12,385 INFO havoc.performance logging_middleware:process_response:205 - Database queries: 58 queries executadas
2025-06-06 21:57:12,385 WARNING havoc.performance logging_middleware:process_response:217 - Muitas queries detectadas: 58
2025-06-06 21:57:12,385 INFO havoc.requests logging_middleware:process_response:63 - Request finalizado: GET /config/ - 500
2025-06-06 21:57:12,386 ERROR django.server basehttp:log_message:213 - "GET /config/ HTTP/1.1" 500 233035
2025-06-06 21:57:37,830 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\pages\views\page_views.py changed, reloading.
2025-06-06 21:57:38,283 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:57:38,386 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\pages\views\page_views.py changed, reloading.
2025-06-06 21:57:38,868 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:58:14,765 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\pages\views\page_views.py changed, reloading.
2025-06-06 21:58:15,203 INFO django.utils.autoreload autoreload:trigger_reload:265 - C:\Users\<USER>\Desktop\Projetos\havoc\apps\pages\views\page_views.py changed, reloading.
2025-06-06 21:58:15,216 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
2025-06-06 21:58:15,681 INFO django.utils.autoreload autoreload:run_with_reloader:667 - Watching for file changes with StatReloader
