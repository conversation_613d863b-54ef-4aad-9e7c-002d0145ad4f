from django.contrib.auth import authenticate
from apps.accounts.interfaces.services import IAuthService
from django.core.exceptions import ObjectDoesNotExist
from apps.core.utils.logging_utils import log_service_operation, security_logger

class AuthService(IAuthService):
    """Serviço para autenticação de usuários"""
    
    def __init__(self, user_repository):
        self.user_repository = user_repository
    
    @log_service_operation("authenticate_user", log_args=False)
    def authenticate_user(self, email: str, password: str, ip_address: str = None, user_agent: str = None):
        """
        Autentica um usuário
        :return: Instância do usuário se autenticação for válida, None caso contrário
        :raises ValueError: Se o usuário não estiver verificado
        """
        user = authenticate(email=email, password=password)

        # Log da tentativa de login
        success = user is not None and user.is_verified
        security_logger.log_login_attempt(
            email=email,
            success=success,
            ip_address=ip_address or 'unknown',
            user_agent=user_agent or 'unknown'
        )

        if user is None:
            return None

        if not user.is_verified:
            raise ValueError("Este usuário não está verificado. Por favor, verifique seu e-mail.")

        return user