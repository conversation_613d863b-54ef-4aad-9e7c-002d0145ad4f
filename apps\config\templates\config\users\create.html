{% extends 'config/base_config.html' %}
{% load crispy_forms_tags %}

{% block config_title %}Criar <PERSON>{% endblock %}

{% block breadcrumbs %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'config:dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'config:user_list' %}">Usuários</a></li>
            <li class="breadcrumb-item active">Criar Usuário</li>
        </ol>
    </nav>
</div>
{% endblock %}

{% block config_content %}
<!-- Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-user-plus me-2 text-primary"></i>Criar Novo Usuário
                </h1>
                <p class="text-muted mb-0">Adicione um novo usuário ao sistema</p>
            </div>
            <div>
                <a href="{% url 'config:user_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Voltar
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Formulário Principal -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light border-0">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" style="width: 40px; height: 40px;">
                            <i class="fas fa-user-plus"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="mb-0">Informações do Usuário</h5>
                        <small class="text-muted">Preencha os dados do novo usuário</small>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                {% crispy form %}
            </div>
        </div>
    </div>

    <!-- Sidebar com Informações -->
    <div class="col-lg-4">
        <!-- Dicas de Criação -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-info text-white border-0">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Dicas de Criação
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-start mb-3">
                    <i class="fas fa-envelope text-primary me-2 mt-1"></i>
                    <div>
                        <strong>Email</strong>
                        <div class="small text-muted">Será usado como login principal</div>
                    </div>
                </div>
                <div class="d-flex align-items-start mb-3">
                    <i class="fas fa-user text-success me-2 mt-1"></i>
                    <div>
                        <strong>Username</strong>
                        <div class="small text-muted">Identificador único do usuário</div>
                    </div>
                </div>
                <div class="d-flex align-items-start mb-3">
                    <i class="fas fa-key text-warning me-2 mt-1"></i>
                    <div>
                        <strong>Senha</strong>
                        <div class="small text-muted">Mínimo 8 caracteres, com letras e números</div>
                    </div>
                </div>
                <div class="d-flex align-items-start">
                    <i class="fas fa-shield-alt text-danger me-2 mt-1"></i>
                    <div>
                        <strong>Permissões</strong>
                        <div class="small text-muted">Configure com cuidado os níveis de acesso</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Níveis de Permissão -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-warning text-dark border-0">
                <h6 class="mb-0">
                    <i class="fas fa-users-cog me-2"></i>Níveis de Permissão
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-check-circle text-success me-2"></i>
                        <strong>Ativo</strong>
                    </div>
                    <div class="small text-muted">Usuário pode fazer login no sistema</div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-cog text-primary me-2"></i>
                        <strong>Staff</strong>
                    </div>
                    <div class="small text-muted">Acesso ao painel administrativo</div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-crown text-warning me-2"></i>
                        <strong>Superusuário</strong>
                    </div>
                    <div class="small text-muted">Todas as permissões do sistema</div>
                </div>
                
                <div class="alert alert-warning small mb-0">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    <strong>Atenção:</strong> Superusuários têm acesso total ao sistema.
                </div>
            </div>
        </div>

        <!-- Grupos Disponíveis -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light border-0">
                <h6 class="mb-0">
                    <i class="fas fa-users me-2"></i>Grupos de Permissão
                </h6>
            </div>
            <div class="card-body">
                <div class="small text-muted mb-3">
                    Os grupos facilitam o gerenciamento de permissões, aplicando um conjunto pré-definido de permissões ao usuário.
                </div>
                
                {% if form.fields.groups.queryset %}
                    <div class="list-group list-group-flush">
                        {% for group in form.fields.groups.queryset %}
                        <div class="list-group-item border-0 px-0 py-2">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-users text-muted me-2"></i>
                                <div>
                                    <div class="fw-semibold">{{ group.name }}</div>
                                    <div class="small text-muted">{{ group.permissions.count }} permissões</div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <div class="small">Nenhum grupo criado ainda</div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.form-control:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.card {
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.list-group-item {
    transition: background-color 0.2s ease;
}

.list-group-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus no primeiro campo
    const firstInput = document.querySelector('input[name="first_name"]');
    if (firstInput) {
        firstInput.focus();
    }
    
    // Validação de senha em tempo real
    const password1 = document.querySelector('input[name="password1"]');
    const password2 = document.querySelector('input[name="password2"]');
    
    if (password1 && password2) {
        function validatePasswords() {
            const pass1 = password1.value;
            const pass2 = password2.value;
            
            // Limpa classes anteriores
            password1.classList.remove('is-valid', 'is-invalid');
            password2.classList.remove('is-valid', 'is-invalid');
            
            // Valida primeira senha
            if (pass1.length >= 8) {
                password1.classList.add('is-valid');
            } else if (pass1.length > 0) {
                password1.classList.add('is-invalid');
            }
            
            // Valida confirmação
            if (pass2.length > 0) {
                if (pass1 === pass2 && pass1.length >= 8) {
                    password2.classList.add('is-valid');
                } else {
                    password2.classList.add('is-invalid');
                }
            }
        }
        
        password1.addEventListener('input', validatePasswords);
        password2.addEventListener('input', validatePasswords);
    }
    
    // Geração automática de username baseado no email
    const emailField = document.querySelector('input[name="email"]');
    const usernameField = document.querySelector('input[name="username"]');
    
    if (emailField && usernameField) {
        emailField.addEventListener('input', function() {
            if (!usernameField.value) {
                const email = this.value;
                const username = email.split('@')[0].toLowerCase().replace(/[^a-z0-9]/g, '');
                if (username) {
                    usernameField.value = username;
                }
            }
        });
    }
    
    // Tooltip para campos de permissão
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
