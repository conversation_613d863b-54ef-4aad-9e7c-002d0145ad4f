{% extends 'base.html' %}

{% block title %}{% if query %}Busca por "{{ query }}"{% else %}Busca{% endif %} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="display-6">
                    <i class="fas fa-search me-2"></i>
                    {% if query %}
                        Resultados para "{{ query }}"
                    {% else %}
                        Busca no Site
                    {% endif %}
                </h1>
                
                {% if query and total_results %}
                    <p class="lead text-muted">
                        {{ total_results }} resultado{{ total_results|pluralize }} encontrado{{ total_results|pluralize }}
                    </p>
                {% endif %}
            </div>

            <!-- Search Form -->
            <div class="card mb-5">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-9">
                            <input type="search" class="form-control form-control-lg" name="q" 
                                   value="{{ query }}" placeholder="Digite sua busca..." autofocus>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-search me-1"></i>Buscar
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Results -->
            {% if query %}
                {% if pages %}
                    <div class="search-results">
                        {% for page in pages %}
                            <div class="card mb-4">
                                <div class="card-body">
                                    <div class="row">
                                        {% if page.featured_image %}
                                            <div class="col-md-3">
                                                <img src="{{ page.featured_image.url }}" class="img-fluid rounded" 
                                                     alt="{{ page.title }}" style="height: 120px; object-fit: cover; width: 100%;">
                                            </div>
                                            <div class="col-md-9">
                                        {% else %}
                                            <div class="col-12">
                                        {% endif %}
                                            <h5 class="card-title">
                                                <a href="{% url 'pages:page_detail' page.slug %}" class="text-decoration-none">
                                                    {{ page.title }}
                                                </a>
                                            </h5>
                                            
                                            {% if page.excerpt %}
                                                <p class="card-text text-muted">
                                                    {{ page.excerpt|truncatewords:30 }}
                                                </p>
                                            {% endif %}
                                            
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <small class="text-muted">
                                                        <i class="fas fa-calendar me-1"></i>
                                                        {{ page.created_at|date:"d/m/Y" }}
                                                    </small>
                                                    
                                                    {% if page.views_count %}
                                                        <small class="text-muted ms-3">
                                                            <i class="fas fa-eye me-1"></i>
                                                            {{ page.views_count }} visualizações
                                                        </small>
                                                    {% endif %}
                                                </div>
                                                
                                                <a href="{% url 'pages:page_detail' page.slug %}" class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-arrow-right me-1"></i>Ler mais
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>

                    <!-- Pagination -->
                    {% if page_obj.has_other_pages %}
                        <nav aria-label="Navegação de resultados">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1&q={{ query }}">
                                            <i class="fas fa-angle-double-left"></i>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}&q={{ query }}">
                                            <i class="fas fa-angle-left"></i>
                                        </a>
                                    </li>
                                {% endif %}

                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ num }}&q={{ query }}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}&q={{ query }}">
                                            <i class="fas fa-angle-right"></i>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}&q={{ query }}">
                                            <i class="fas fa-angle-double-right"></i>
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                {% else %}
                    <!-- No Results -->
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-4x text-muted mb-4"></i>
                        <h3>Nenhum resultado encontrado</h3>
                        <p class="text-muted mb-4">
                            Não encontramos nenhuma página para "{{ query }}".
                        </p>
                        
                        <div class="row justify-content-center">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">Dicas para melhorar sua busca:</h5>
                                        <ul class="list-unstyled text-start">
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                Verifique a ortografia das palavras
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                Tente usar palavras-chave diferentes
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                Use termos mais gerais
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                Reduza o número de palavras
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <a href="{% url 'pages:home' %}" class="btn btn-primary me-2">
                                <i class="fas fa-home me-1"></i>Voltar ao início
                            </a>
                            <a href="{% url 'pages:page_list' %}" class="btn btn-outline-primary">
                                <i class="fas fa-file-alt me-1"></i>Ver todas as páginas
                            </a>
                        </div>
                    </div>
                {% endif %}
            {% else %}
                <!-- Search Instructions -->
                <div class="text-center py-5">
                    <i class="fas fa-search fa-4x text-primary mb-4"></i>
                    <h3>Busque por conteúdo</h3>
                    <p class="text-muted mb-4">
                        Digite palavras-chave no campo acima para encontrar páginas e artigos.
                    </p>
                    
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Sugestões populares:</h5>
                                    <div class="d-flex flex-wrap justify-content-center gap-2">
                                        <a href="?q=sobre" class="badge bg-light text-dark text-decoration-none">sobre</a>
                                        <a href="?q=contato" class="badge bg-light text-dark text-decoration-none">contato</a>
                                        <a href="?q=serviços" class="badge bg-light text-dark text-decoration-none">serviços</a>
                                        <a href="?q=produtos" class="badge bg-light text-dark text-decoration-none">produtos</a>
                                        <a href="?q=ajuda" class="badge bg-light text-dark text-decoration-none">ajuda</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Highlight search terms in results
    const query = '{{ query|escapejs }}';
    if (query) {
        const searchResults = document.querySelector('.search-results');
        if (searchResults) {
            const regex = new RegExp(`(${query})`, 'gi');
            const walker = document.createTreeWalker(
                searchResults,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );
            
            const textNodes = [];
            let node;
            while (node = walker.nextNode()) {
                textNodes.push(node);
            }
            
            textNodes.forEach(function(textNode) {
                if (regex.test(textNode.textContent)) {
                    const highlightedText = textNode.textContent.replace(regex, '<mark>$1</mark>');
                    const wrapper = document.createElement('span');
                    wrapper.innerHTML = highlightedText;
                    textNode.parentNode.replaceChild(wrapper, textNode);
                }
            });
        }
    }
});
</script>
{% endblock %}
