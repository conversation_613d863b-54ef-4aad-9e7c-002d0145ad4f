<!-- Toast Container -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
    <!-- Toasts serão inseridos aqui via JavaScript -->
</div>

<!-- Toast Template (hidden) -->
<div id="toast-template" class="toast align-items-center border-0 d-none" role="alert" aria-live="assertive" aria-atomic="true">
    <div class="d-flex">
        <div class="toast-body">
            <!-- Mensagem será inserida aqui -->
        </div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
</div>

<script>
// Função para mostrar toast
function showToast(message, type = 'info', duration = 5000) {
    const container = document.querySelector('.toast-container');
    const template = document.getElementById('toast-template');
    
    if (!container || !template) return;
    
    // Clonar template
    const toast = template.cloneNode(true);
    toast.id = 'toast-' + Date.now();
    toast.classList.remove('d-none');
    
    // Definir cor baseada no tipo
    const typeClasses = {
        'success': 'text-bg-success',
        'error': 'text-bg-danger',
        'warning': 'text-bg-warning',
        'info': 'text-bg-info',
        'primary': 'text-bg-primary'
    };
    
    toast.classList.add(typeClasses[type] || typeClasses['info']);
    
    // Definir mensagem
    const body = toast.querySelector('.toast-body');
    body.innerHTML = message;
    
    // Adicionar ao container
    container.appendChild(toast);
    
    // Inicializar e mostrar
    const bsToast = new bootstrap.Toast(toast, {
        delay: duration
    });
    
    bsToast.show();
    
    // Remover do DOM após esconder
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

// Mostrar toasts das mensagens do Django
document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            if (alert && alert.parentNode) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    });
});
</script>
