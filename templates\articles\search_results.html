{% extends "articles/base_articles.html" %}
{% load static %}

{% block title %}
    {% if query %}
        Busca: {{ query }} - {{ block.super }}
    {% else %}
        Buscar Artigos - {{ block.super }}
    {% endif %}
{% endblock %}

{% block articles_content %}
<!-- Header da Bus<PERSON> -->
<div class="search-header mb-4">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="h2 mb-1">
                <i class="fas fa-search me-2"></i>
                {% if query %}
                    Resultados para "{{ query }}"
                {% else %}
                    Buscar Artigos
                {% endif %}
            </h1>
            {% if query %}
            <p class="text-muted mb-0">
                {{ total_results }} resultado{{ total_results|pluralize }} encontrado{{ total_results|pluralize }}
                {% if total_results > 0 %}
                    em {{ page_obj.paginator.num_pages }} página{{ page_obj.paginator.num_pages|pluralize }}
                {% endif %}
            </p>
            {% endif %}
        </div>
        <div class="col-md-4 text-md-end">
            <a href="{% url 'articles:list' %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i>Todos os Artigos
            </a>
        </div>
    </div>
</div>

<!-- Formulário de Busca Avançada -->
<div class="search-form-container mb-4">
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>Busca Avançada
                <button class="btn btn-sm btn-outline-secondary float-end" 
                        type="button" 
                        data-bs-toggle="collapse" 
                        data-bs-target="#advancedSearch">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </h5>
        </div>
        <div class="card-body">
            <!-- Busca Simples -->
            <form method="get" class="mb-3">
                <div class="input-group input-group-lg">
                    <input type="text" 
                           name="q" 
                           class="form-control" 
                           placeholder="Digite sua busca..."
                           value="{{ query|default:'' }}"
                           autofocus>
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search me-1"></i>Buscar
                    </button>
                </div>
            </form>

            <!-- Busca Avançada (Colapsável) -->
            <div class="collapse {% if request.GET.category or request.GET.tag %}show{% endif %}" id="advancedSearch">
                <hr>
                <form method="get">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="searchQuery" class="form-label">Termo de Busca</label>
                            <input type="text" 
                                   name="q" 
                                   id="searchQuery"
                                   class="form-control" 
                                   placeholder="Palavras-chave..."
                                   value="{{ query|default:'' }}">
                        </div>
                        <div class="col-md-4">
                            <label for="searchCategory" class="form-label">Categoria</label>
                            <select name="category" id="searchCategory" class="form-select">
                                <option value="">Todas as categorias</option>
                                {% for category in categories %}
                                <option value="{{ category.slug }}" 
                                        {% if request.GET.category == category.slug %}selected{% endif %}>
                                    {{ category.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="searchTag" class="form-label">Tag</label>
                            <select name="tag" id="searchTag" class="form-select">
                                <option value="">Todas as tags</option>
                                {% for tag in popular_tags %}
                                <option value="{{ tag.slug }}" 
                                        {% if request.GET.tag == tag.slug %}selected{% endif %}>
                                    {{ tag.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label for="searchOrder" class="form-label">Ordenar por</label>
                            <select name="order" id="searchOrder" class="form-select">
                                <option value="-created_at" {% if request.GET.order == '-created_at' or not request.GET.order %}selected{% endif %}>
                                    Mais Recentes
                                </option>
                                <option value="created_at" {% if request.GET.order == 'created_at' %}selected{% endif %}>
                                    Mais Antigos
                                </option>
                                <option value="title" {% if request.GET.order == 'title' %}selected{% endif %}>
                                    A-Z
                                </option>
                                <option value="-title" {% if request.GET.order == '-title' %}selected{% endif %}>
                                    Z-A
                                </option>
                            </select>
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>Buscar
                            </button>
                            <a href="{% url 'articles:search' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Limpar
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Filtros Ativos -->
{% if query or request.GET.category or request.GET.tag %}
<div class="active-filters mb-4">
    <h6><i class="fas fa-filter me-2"></i>Filtros Ativos:</h6>
    <div class="d-flex flex-wrap gap-2">
        {% if query %}
        <span class="badge bg-primary">
            Busca: "{{ query }}"
            <a href="{% url 'articles:search' %}{% if request.GET.category %}?category={{ request.GET.category }}{% endif %}{% if request.GET.tag %}{% if request.GET.category %}&{% else %}?{% endif %}tag={{ request.GET.tag }}{% endif %}" 
               class="text-white ms-1">
                <i class="fas fa-times"></i>
            </a>
        </span>
        {% endif %}
        
        {% if request.GET.category %}
        <span class="badge bg-info">
            Categoria: {{ request.GET.category }}
            <a href="{% url 'articles:search' %}{% if query %}?q={{ query }}{% endif %}{% if request.GET.tag %}{% if query %}&{% else %}?{% endif %}tag={{ request.GET.tag }}{% endif %}" 
               class="text-white ms-1">
                <i class="fas fa-times"></i>
            </a>
        </span>
        {% endif %}
        
        {% if request.GET.tag %}
        <span class="badge bg-success">
            Tag: {{ request.GET.tag }}
            <a href="{% url 'articles:search' %}{% if query %}?q={{ query }}{% endif %}{% if request.GET.category %}{% if query %}&{% else %}?{% endif %}category={{ request.GET.category }}{% endif %}" 
               class="text-white ms-1">
                <i class="fas fa-times"></i>
            </a>
        </span>
        {% endif %}
        
        <a href="{% url 'articles:search' %}" class="badge bg-secondary text-decoration-none">
            <i class="fas fa-times me-1"></i>Limpar Todos
        </a>
    </div>
</div>
{% endif %}

<!-- Resultados -->
{% if articles %}
<div class="row">
    {% for article in articles %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card article-card h-100 position-relative">
            <!-- Ações do Artigo (para autores) -->
            {% if user.is_authenticated and user == article.author or user.is_staff %}
            <div class="article-actions">
                <a href="{% url 'articles:update' article.slug %}" 
                   class="action-btn text-primary" 
                   title="Editar">
                    <i class="fas fa-edit"></i>
                </a>
                <a href="{% url 'articles:delete' article.slug %}" 
                   class="action-btn text-danger" 
                   title="Deletar">
                    <i class="fas fa-trash"></i>
                </a>
            </div>
            {% endif %}

            <!-- Imagem do Artigo -->
            {% if article.featured_image %}
            <img src="{{ article.featured_image.url }}" 
                 class="article-image" 
                 alt="{{ article.title }}">
            {% else %}
            <div class="article-image bg-light d-flex align-items-center justify-content-center">
                <i class="fas fa-newspaper fa-3x text-muted"></i>
            </div>
            {% endif %}

            <div class="card-body d-flex flex-column">
                <!-- Categoria -->
                {% if article.category %}
                <a href="{% url 'articles:category_detail' article.category.slug %}" 
                   class="category-badge">
                    {{ article.category.name }}
                </a>
                {% endif %}

                <!-- Título -->
                <h5 class="card-title">
                    <a href="{% url 'articles:detail' article.slug %}" 
                       class="text-decoration-none text-dark">
                        {{ article.title }}
                    </a>
                </h5>

                <!-- Excerpt -->
                {% if article.excerpt %}
                <p class="card-text article-excerpt flex-grow-1">
                    {{ article.excerpt|truncatewords:20 }}
                </p>
                {% endif %}

                <!-- Tags -->
                {% if article.tags.all %}
                <div class="mb-2">
                    {% for tag in article.tags.all %}
                    <a href="{% url 'articles:search' %}?tag={{ tag.slug }}" 
                       class="tag-badge">
                        {{ tag.name }}
                    </a>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Meta informações -->
                <div class="article-meta mt-auto">
                    <div class="d-flex justify-content-between align-items-center">
                        <small>
                            <i class="fas fa-user me-1"></i>{{ article.author.get_full_name|default:article.author.username }}
                        </small>
                        <small>
                            <i class="fas fa-calendar me-1"></i>{{ article.created_at|date:"d/m/Y" }}
                        </small>
                    </div>
                    {% if article.views %}
                    <div class="mt-1">
                        <small class="text-muted">
                            <i class="fas fa-eye me-1"></i>{{ article.views }} visualizaç{{ article.views|pluralize:"ão,ões" }}
                        </small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Paginação -->
{% if page_obj.has_other_pages %}
<nav aria-label="Paginação dos resultados" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
        <li class="page-item">
            <a class="page-link" href="?page=1{% if query %}&q={{ query }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.tag %}&tag={{ request.GET.tag }}{% endif %}">
                <i class="fas fa-angle-double-left"></i>
            </a>
        </li>
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if query %}&q={{ query }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.tag %}&tag={{ request.GET.tag }}{% endif %}">
                <i class="fas fa-angle-left"></i>
            </a>
        </li>
        {% endif %}

        {% for num in page_obj.paginator.page_range %}
        {% if page_obj.number == num %}
        <li class="page-item active">
            <span class="page-link">{{ num }}</span>
        </li>
        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
        <li class="page-item">
            <a class="page-link" href="?page={{ num }}{% if query %}&q={{ query }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.tag %}&tag={{ request.GET.tag }}{% endif %}">
                {{ num }}
            </a>
        </li>
        {% endif %}
        {% endfor %}

        {% if page_obj.has_next %}
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if query %}&q={{ query }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.tag %}&tag={{ request.GET.tag }}{% endif %}">
                <i class="fas fa-angle-right"></i>
            </a>
        </li>
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if query %}&q={{ query }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.tag %}&tag={{ request.GET.tag }}{% endif %}">
                <i class="fas fa-angle-double-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<!-- Nenhum resultado encontrado -->
<div class="text-center py-5">
    <i class="fas fa-search fa-4x text-muted mb-3"></i>
    <h3 class="text-muted">
        {% if query %}
            Nenhum resultado encontrado
        {% else %}
            Digite algo para buscar
        {% endif %}
    </h3>
    <p class="text-muted">
        {% if query %}
            Não encontramos artigos para "{{ query }}". Tente:
            <ul class="list-unstyled mt-2">
                <li>• Verificar a ortografia</li>
                <li>• Usar palavras-chave diferentes</li>
                <li>• Usar termos mais gerais</li>
                <li>• Remover filtros de categoria ou tag</li>
            </ul>
        {% else %}
            Use o formulário acima para buscar artigos por título, conteúdo, categoria ou tags.
        {% endif %}
    </p>
    
    {% if query %}
    <div class="mt-3">
        <a href="{% url 'articles:search' %}" class="btn btn-primary">
            <i class="fas fa-search me-1"></i>Nova Busca
        </a>
        <a href="{% url 'articles:list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-list me-1"></i>Ver Todos os Artigos
        </a>
    </div>
    {% endif %}
</div>
{% endif %}
{% endblock %}
