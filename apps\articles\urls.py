from django.urls import path
from apps.articles.views.article_views import (
    ArticleListView, ArticleDetailView, ArticleCreateView,
    ArticleUpdateView, ArticleDeleteView, ArticleSearchView,
    CategoryListView, CategoryDetailView, toggle_article_status,
    article_preview
)

app_name = 'articles'

urlpatterns = [
    # Artigos
    path('', ArticleListView.as_view(), name='list'),
    path('criar/', ArticleCreateView.as_view(), name='create'),
    path('buscar/', ArticleSearchView.as_view(), name='search'),

    # Categorias
    path('categorias/', CategoryListView.as_view(), name='category_list'),
    path('categoria/<slug:slug>/', CategoryDetailView.as_view(), name='category_detail'),

    # Artigo específico (deve vir por último para não conflitar)
    path('<slug:slug>/', ArticleDetailView.as_view(), name='detail'),
    path('<slug:slug>/editar/', ArticleUpdateView.as_view(), name='update'),
    path('<slug:slug>/deletar/', ArticleDeleteView.as_view(), name='delete'),
    path('<slug:slug>/preview/', article_preview, name='preview'),
    path('<slug:slug>/toggle-status/', toggle_article_status, name='toggle_status'),
]
