{% extends "articles/base_articles.html" %}
{% load static %}

{% block title %}
    {% if current_category %}
        Artigos - {{ current_category }}
    {% elif search_query %}
        Busca: {{ search_query }}
    {% else %}
        Todos os Artigos
    {% endif %} - {{ block.super }}
{% endblock %}

{% block articles_content %}
<!-- Header da Página -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h2 mb-1">
            {% if current_category %}
                <i class="fas fa-folder me-2"></i>{{ current_category }}
            {% elif search_query %}
                <i class="fas fa-search me-2"></i>Resultados para "{{ search_query }}"
            {% else %}
                <i class="fas fa-newspaper me-2"></i>Todos os Artigos
            {% endif %}
        </h1>
        <p class="text-muted mb-0">
            {% if page_obj %}
                {{ page_obj.paginator.count }} artigo{{ page_obj.paginator.count|pluralize }} encontrado{{ page_obj.paginator.count|pluralize }}
            {% endif %}
        </p>
    </div>
    
    {% if user.is_authenticated %}
    <div>
        <a href="{% url 'articles:create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Novo Artigo
        </a>
    </div>
    {% endif %}
</div>

<!-- Filtros -->
<div class="filter-section">
    <div class="row align-items-center">
        <div class="col-md-6">
            <h6><i class="fas fa-filter me-2"></i>Filtros</h6>
            <div class="d-flex flex-wrap">
                <a href="{% url 'articles:list' %}" 
                   class="btn btn-outline-primary {% if not current_category and not current_tag %}active{% endif %}">
                    Todos
                </a>
                {% for category in categories %}
                <a href="{% url 'articles:category_detail' category.slug %}" 
                   class="btn btn-outline-primary {% if current_category == category.slug %}active{% endif %}">
                    {{ category.name }}
                </a>
                {% endfor %}
            </div>
        </div>
        
        <div class="col-md-6">
            <h6><i class="fas fa-sort me-2"></i>Ordenar por</h6>
            <form method="get" class="d-flex">
                {% if search_query %}
                    <input type="hidden" name="search" value="{{ search_query }}">
                {% endif %}
                {% if current_category %}
                    <input type="hidden" name="category" value="{{ current_category }}">
                {% endif %}
                <select name="order" class="form-select form-select-sm me-2" onchange="this.form.submit()">
                    <option value="-created_at" {% if current_order == '-created_at' %}selected{% endif %}>
                        Mais Recentes
                    </option>
                    <option value="created_at" {% if current_order == 'created_at' %}selected{% endif %}>
                        Mais Antigos
                    </option>
                    <option value="title" {% if current_order == 'title' %}selected{% endif %}>
                        A-Z
                    </option>
                    <option value="-title" {% if current_order == '-title' %}selected{% endif %}>
                        Z-A
                    </option>
                </select>
            </form>
        </div>
    </div>
</div>

<!-- Lista de Artigos -->
{% if articles %}
<div class="row">
    {% for article in articles %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card article-card h-100 position-relative">
            <!-- Ações do Artigo (para autores) -->
            {% if user.is_authenticated and user == article.author or user.is_staff %}
            <div class="article-actions">
                <a href="{% url 'articles:update' article.slug %}" 
                   class="action-btn text-primary" 
                   title="Editar">
                    <i class="fas fa-edit"></i>
                </a>
                <a href="{% url 'articles:delete' article.slug %}" 
                   class="action-btn text-danger" 
                   title="Deletar">
                    <i class="fas fa-trash"></i>
                </a>
            </div>
            {% endif %}

            <!-- Imagem do Artigo -->
            {% if article.featured_image %}
            <img src="{{ article.featured_image.url }}" 
                 class="article-image" 
                 alt="{{ article.title }}">
            {% else %}
            <div class="article-image bg-light d-flex align-items-center justify-content-center">
                <i class="fas fa-newspaper fa-3x text-muted"></i>
            </div>
            {% endif %}

            <div class="card-body d-flex flex-column">
                <!-- Categoria -->
                {% if article.category %}
                <a href="{% url 'articles:category_detail' article.category.slug %}" 
                   class="category-badge">
                    {{ article.category.name }}
                </a>
                {% endif %}

                <!-- Título -->
                <h5 class="card-title">
                    <a href="{% url 'articles:detail' article.slug %}" 
                       class="text-decoration-none text-dark">
                        {{ article.title }}
                    </a>
                </h5>

                <!-- Excerpt -->
                {% if article.excerpt %}
                <p class="card-text article-excerpt flex-grow-1">
                    {{ article.excerpt|truncatewords:20 }}
                </p>
                {% endif %}

                <!-- Tags -->
                {% if article.tags.all %}
                <div class="mb-2">
                    {% for tag in article.tags.all %}
                    <a href="{% url 'articles:search' %}?tag={{ tag.slug }}" 
                       class="tag-badge">
                        {{ tag.name }}
                    </a>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Meta informações -->
                <div class="article-meta mt-auto">
                    <div class="d-flex justify-content-between align-items-center">
                        <small>
                            <i class="fas fa-user me-1"></i>{{ article.author.get_full_name|default:article.author.username }}
                        </small>
                        <small>
                            <i class="fas fa-calendar me-1"></i>{{ article.created_at|date:"d/m/Y" }}
                        </small>
                    </div>
                    {% if article.views %}
                    <div class="mt-1">
                        <small class="text-muted">
                            <i class="fas fa-eye me-1"></i>{{ article.views }} visualizaç{{ article.views|pluralize:"ão,ões" }}
                        </small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Paginação -->
{% if page_obj.has_other_pages %}
<nav aria-label="Paginação dos artigos" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
        <li class="page-item">
            <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}">
                <i class="fas fa-angle-double-left"></i>
            </a>
        </li>
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}">
                <i class="fas fa-angle-left"></i>
            </a>
        </li>
        {% endif %}

        {% for num in page_obj.paginator.page_range %}
        {% if page_obj.number == num %}
        <li class="page-item active">
            <span class="page-link">{{ num }}</span>
        </li>
        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
        <li class="page-item">
            <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}">
                {{ num }}
            </a>
        </li>
        {% endif %}
        {% endfor %}

        {% if page_obj.has_next %}
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}">
                <i class="fas fa-angle-right"></i>
            </a>
        </li>
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}">
                <i class="fas fa-angle-double-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<!-- Nenhum artigo encontrado -->
<div class="text-center py-5">
    <i class="fas fa-newspaper fa-4x text-muted mb-3"></i>
    <h3 class="text-muted">Nenhum artigo encontrado</h3>
    <p class="text-muted">
        {% if search_query %}
            Não encontramos artigos para "{{ search_query }}". Tente uma busca diferente.
        {% elif current_category %}
            Não há artigos nesta categoria ainda.
        {% else %}
            Não há artigos publicados ainda.
        {% endif %}
    </p>
    {% if user.is_authenticated %}
    <a href="{% url 'articles:create' %}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Criar Primeiro Artigo
    </a>
    {% endif %}
</div>
{% endif %}
{% endblock %}
