{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Verificação - {{ block.super }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm mt-5">
                <div class="card-header bg-info text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>Verificação
                    </h4>
                </div>
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <i class="fas fa-envelope fa-3x text-info mb-3"></i>
                        <h5>Código de Verificação</h5>
                        <p class="text-muted">
                            Enviamos um código de 6 dígitos para seu e-mail. 
                            Digite o código abaixo para verificar sua conta.
                        </p>
                    </div>

                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    {% crispy form %}

                    <div class="text-center mt-4">
                        <div class="alert alert-light">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                O código expira em <span id="countdown">10:00</span> minutos
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Countdown timer
let timeLeft = 600; // 10 minutes in seconds

function updateCountdown() {
    const minutes = Math.floor(timeLeft / 60);
    const seconds = timeLeft % 60;
    const display = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    
    const countdownElement = document.getElementById('countdown');
    if (countdownElement) {
        countdownElement.textContent = display;
    }
    
    if (timeLeft <= 0) {
        // Code expired
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-warning';
        alertDiv.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>O código expirou. Solicite um novo código.';
        
        const form = document.querySelector('form');
        if (form) {
            form.parentNode.insertBefore(alertDiv, form);
            form.style.display = 'none';
        }
    } else {
        timeLeft--;
        setTimeout(updateCountdown, 1000);
    }
}

// Start countdown
document.addEventListener('DOMContentLoaded', function() {
    updateCountdown();
    
    // Auto-focus on code input
    const codeInput = document.getElementById('id_code');
    if (codeInput) {
        codeInput.focus();
        
        // Auto-submit when 6 digits are entered
        codeInput.addEventListener('input', function() {
            if (this.value.length === 6 && /^\d{6}$/.test(this.value)) {
                // Optional: auto-submit form
                // this.form.submit();
            }
        });
    }
});

// Resend code function
function resendCode() {
    // Redirect to registration page to start over
    window.location.href = '{% url "accounts:register" %}';
}
</script>
{% endblock %}
