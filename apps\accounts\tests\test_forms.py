from django.test import TestCase
from django.contrib.auth import get_user_model
from apps.accounts.forms.registration import RegistrationForm, VerificationForm
from apps.accounts.forms.auth import LoginForm

User = get_user_model()


class RegistrationFormTest(TestCase):
    """Testes para RegistrationForm"""
    
    def setUp(self):
        """Configuração inicial para os testes"""
        self.valid_data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'testpass123',
            'password_confirm': 'testpass123'
        }
    
    def test_form_valid_data(self):
        """Testa formulário com dados válidos"""
        form = RegistrationForm(data=self.valid_data)
        self.assertTrue(form.is_valid())
    
    def test_form_missing_required_fields(self):
        """Testa formulário com campos obrigatórios faltando"""
        required_fields = ['email', 'username', 'password', 'password_confirm']
        
        for field in required_fields:
            data = self.valid_data.copy()
            del data[field]
            
            form = RegistrationForm(data=data)
            self.assertFalse(form.is_valid())
            self.assertIn(field, form.errors)
    
    def test_form_password_mismatch(self):
        """Testa formulário com senhas que não coincidem"""
        data = self.valid_data.copy()
        data['password_confirm'] = 'different_password'
        
        form = RegistrationForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('password_confirm', form.errors)
    
    def test_form_invalid_email(self):
        """Testa formulário com email inválido"""
        data = self.valid_data.copy()
        data['email'] = 'invalid-email'
        
        form = RegistrationForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)
    
    def test_form_duplicate_email(self):
        """Testa formulário com email já em uso"""
        # Cria usuário verificado com o email
        User.objects.create_user(
            email=self.valid_data['email'],
            username='existing',
            password='pass123',
            is_verified=True
        )
        
        form = RegistrationForm(data=self.valid_data)
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)
    
    def test_form_duplicate_username(self):
        """Testa formulário com username já em uso"""
        # Cria usuário com o username
        User.objects.create_user(
            email='<EMAIL>',
            username=self.valid_data['username'],
            password='pass123'
        )
        
        form = RegistrationForm(data=self.valid_data)
        self.assertFalse(form.is_valid())
        self.assertIn('username', form.errors)
    
    def test_form_weak_password(self):
        """Testa formulário com senha fraca"""
        data = self.valid_data.copy()
        data['password'] = 'weak'
        data['password_confirm'] = 'weak'
        
        form = RegistrationForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('password', form.errors)
    
    def test_form_save_method(self):
        """Testa método save do formulário"""
        form = RegistrationForm(data=self.valid_data)
        self.assertTrue(form.is_valid())
        
        # O save deve retornar os dados limpos, não criar usuário
        cleaned_data = form.save()
        
        self.assertEqual(cleaned_data['email'], self.valid_data['email'])
        self.assertEqual(cleaned_data['username'], self.valid_data['username'])
        self.assertNotIn('password_confirm', cleaned_data)
    
    def test_form_replaces_unverified_user(self):
        """Testa que formulário permite substituir usuário não verificado"""
        # Cria usuário não verificado com o email
        User.objects.create_user(
            email=self.valid_data['email'],
            username='existing',
            password='pass123',
            is_verified=False
        )
        
        form = RegistrationForm(data=self.valid_data)
        self.assertTrue(form.is_valid())


class VerificationFormTest(TestCase):
    """Testes para VerificationForm"""
    
    def test_form_valid_code(self):
        """Testa formulário com código válido"""
        form = VerificationForm(data={'code': '123456'})
        self.assertTrue(form.is_valid())
    
    def test_form_missing_code(self):
        """Testa formulário sem código"""
        form = VerificationForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('code', form.errors)
    
    def test_form_invalid_code_length(self):
        """Testa formulário com código de tamanho inválido"""
        invalid_codes = ['12345', '1234567', 'abc123', '']
        
        for code in invalid_codes:
            form = VerificationForm(data={'code': code})
            self.assertFalse(form.is_valid())
            self.assertIn('code', form.errors)
    
    def test_form_code_not_numeric(self):
        """Testa formulário com código não numérico"""
        form = VerificationForm(data={'code': 'abcdef'})
        self.assertFalse(form.is_valid())
        self.assertIn('code', form.errors)
    
    def test_form_code_with_spaces(self):
        """Testa formulário com código contendo espaços"""
        form = VerificationForm(data={'code': '12 34 56'})
        self.assertFalse(form.is_valid())
        self.assertIn('code', form.errors)


class LoginFormTest(TestCase):
    """Testes para LoginForm"""
    
    def setUp(self):
        """Configuração inicial para os testes"""
        self.valid_data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123',
            is_verified=True
        )
    
    def test_form_valid_data(self):
        """Testa formulário com dados válidos"""
        form = LoginForm(data=self.valid_data)
        self.assertTrue(form.is_valid())
    
    def test_form_missing_email(self):
        """Testa formulário sem email"""
        data = self.valid_data.copy()
        del data['email']
        
        form = LoginForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)
    
    def test_form_missing_password(self):
        """Testa formulário sem senha"""
        data = self.valid_data.copy()
        del data['password']
        
        form = LoginForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('password', form.errors)
    
    def test_form_invalid_email_format(self):
        """Testa formulário com formato de email inválido"""
        data = self.valid_data.copy()
        data['email'] = 'invalid-email'
        
        form = LoginForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)
    
    def test_form_empty_fields(self):
        """Testa formulário com campos vazios"""
        form = LoginForm(data={'email': '', 'password': ''})
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)
        self.assertIn('password', form.errors)
    
    def test_form_whitespace_handling(self):
        """Testa tratamento de espaços em branco"""
        data = {
            'email': '  <EMAIL>  ',
            'password': '  testpass123  '
        }
        
        form = LoginForm(data=data)
        self.assertTrue(form.is_valid())
        
        # Verifica se espaços foram removidos
        self.assertEqual(form.cleaned_data['email'], '<EMAIL>')
        self.assertEqual(form.cleaned_data['password'], 'testpass123')
    
    def test_form_case_insensitive_email(self):
        """Testa que email é case insensitive"""
        data = self.valid_data.copy()
        data['email'] = '<EMAIL>'
        
        form = LoginForm(data=data)
        self.assertTrue(form.is_valid())
        
        # Email deve ser convertido para lowercase
        self.assertEqual(form.cleaned_data['email'], '<EMAIL>')
    
    def test_form_max_length_validation(self):
        """Testa validação de tamanho máximo"""
        data = {
            'email': 'a' * 300 + '@example.com',  # Email muito longo
            'password': 'a' * 200  # Senha muito longa
        }
        
        form = LoginForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)


class ProfileUpdateFormTest(TestCase):
    """Testes para formulário de atualização de perfil"""

    def setUp(self):
        """Configuração inicial para os testes"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123',
            is_verified=True
        )

        self.valid_data = {
            'first_name': 'Updated',
            'last_name': 'User',
            'bio': 'Updated bio',
            'location': 'New Location'
        }

    def test_form_valid_data(self):
        """Testa formulário com dados válidos"""
        from apps.accounts.forms.profile_forms import ProfileUpdateForm
        form = ProfileUpdateForm(data=self.valid_data, instance=self.user)
        self.assertTrue(form.is_valid())

    def test_form_save_updates_user(self):
        """Testa se o save atualiza o usuário corretamente"""
        from apps.accounts.forms.profile_forms import ProfileUpdateForm
        form = ProfileUpdateForm(data=self.valid_data, instance=self.user)

        if form.is_valid():
            updated_user = form.save()

            self.assertEqual(updated_user.first_name, 'Updated')
            self.assertEqual(updated_user.last_name, 'User')
            self.assertEqual(updated_user.bio, 'Updated bio')

    def test_form_optional_fields(self):
        """Testa que campos opcionais podem estar vazios"""
        from apps.accounts.forms.profile_forms import ProfileUpdateForm
        minimal_data = {
            'first_name': 'Test',
            'last_name': 'User'
        }

        form = ProfileUpdateForm(data=minimal_data, instance=self.user)
        self.assertTrue(form.is_valid())

    def test_form_max_length_validation(self):
        """Testa validação de tamanho máximo"""
        from apps.accounts.forms.profile_forms import ProfileUpdateForm
        invalid_data = self.valid_data.copy()
        invalid_data['first_name'] = 'A' * 200  # Muito longo

        form = ProfileUpdateForm(data=invalid_data, instance=self.user)
        self.assertFalse(form.is_valid())
        self.assertIn('first_name', form.errors)


class PasswordChangeFormTest(TestCase):
    """Testes para formulário de mudança de senha"""

    def setUp(self):
        """Configuração inicial para os testes"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='oldpass123',
            is_verified=True
        )

        self.valid_data = {
            'old_password': 'oldpass123',
            'new_password1': 'newpass123',
            'new_password2': 'newpass123'
        }

    def test_form_valid_data(self):
        """Testa formulário com dados válidos"""
        from django.contrib.auth.forms import PasswordChangeForm
        form = PasswordChangeForm(user=self.user, data=self.valid_data)
        self.assertTrue(form.is_valid())

    def test_form_wrong_old_password(self):
        """Testa formulário com senha antiga incorreta"""
        from django.contrib.auth.forms import PasswordChangeForm
        invalid_data = self.valid_data.copy()
        invalid_data['old_password'] = 'wrongpass'

        form = PasswordChangeForm(user=self.user, data=invalid_data)
        self.assertFalse(form.is_valid())
        self.assertIn('old_password', form.errors)

    def test_form_password_mismatch(self):
        """Testa formulário com senhas novas que não coincidem"""
        from django.contrib.auth.forms import PasswordChangeForm
        invalid_data = self.valid_data.copy()
        invalid_data['new_password2'] = 'differentpass'

        form = PasswordChangeForm(user=self.user, data=invalid_data)
        self.assertFalse(form.is_valid())
        self.assertIn('new_password2', form.errors)

    def test_form_weak_new_password(self):
        """Testa formulário com senha nova fraca"""
        from django.contrib.auth.forms import PasswordChangeForm
        invalid_data = self.valid_data.copy()
        invalid_data['new_password1'] = 'weak'
        invalid_data['new_password2'] = 'weak'

        form = PasswordChangeForm(user=self.user, data=invalid_data)
        self.assertFalse(form.is_valid())


class AvatarUploadFormTest(TestCase):
    """Testes para formulário de upload de avatar"""

    def setUp(self):
        """Configuração inicial para os testes"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123',
            is_verified=True
        )

    def test_form_valid_image(self):
        """Testa upload de imagem válida"""
        from apps.accounts.forms.profile_forms import AvatarUploadForm
        from django.core.files.uploadedfile import SimpleUploadedFile
        from PIL import Image
        import io

        # Cria uma imagem de teste
        img = Image.new('RGB', (100, 100), color='red')
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='JPEG')
        img_buffer.seek(0)

        uploaded_file = SimpleUploadedFile(
            'test.jpg',
            img_buffer.getvalue(),
            content_type='image/jpeg'
        )

        form = AvatarUploadForm(files={'avatar': uploaded_file})
        self.assertTrue(form.is_valid())

    def test_form_invalid_file_type(self):
        """Testa upload de arquivo inválido"""
        from apps.accounts.forms.profile_forms import AvatarUploadForm
        from django.core.files.uploadedfile import SimpleUploadedFile

        text_file = SimpleUploadedFile(
            'test.txt',
            b'This is not an image',
            content_type='text/plain'
        )

        form = AvatarUploadForm(files={'avatar': text_file})
        self.assertFalse(form.is_valid())
        self.assertIn('avatar', form.errors)

    def test_form_no_file(self):
        """Testa formulário sem arquivo"""
        from apps.accounts.forms.profile_forms import AvatarUploadForm
        form = AvatarUploadForm(files={})
        self.assertFalse(form.is_valid())
        self.assertIn('avatar', form.errors)
