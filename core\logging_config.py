"""
Configuração avançada de logging para o projeto Havoc
"""

import os
import logging
from pathlib import Path

# Build paths
BASE_DIR = Path(__file__).resolve().parent.parent
LOGS_DIR = BASE_DIR / 'logs'

# Cria diretório de logs se não existir
LOGS_DIR.mkdir(exist_ok=True)

class ColoredFormatter(logging.Formatter):
    """Formatter colorido para console"""
    
    # Cores ANSI
    COLORS = {
        'DEBUG': '\033[36m',      # Cyan
        'INFO': '\033[32m',       # Green
        'WARNING': '\033[33m',    # Yellow
        'ERROR': '\033[31m',      # Red
        'CRITICAL': '\033[35m',   # Magenta
        'RESET': '\033[0m'        # Reset
    }
    
    def format(self, record):
        # Adiciona cor baseada no nível
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        record.levelname = f"{color}{record.levelname}{self.COLORS['RESET']}"
        
        # Adiciona informações extras
        if hasattr(record, 'user_id'):
            record.user_info = f"[User:{record.user_id}]"
        else:
            record.user_info = ""
            
        if hasattr(record, 'request_id'):
            record.request_info = f"[Req:{record.request_id}]"
        else:
            record.request_info = ""
        
        return super().format(record)

class StructuredFormatter(logging.Formatter):
    """Formatter estruturado para arquivos JSON"""
    
    def format(self, record):
        import json
        from datetime import datetime
        
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }
        
        # Adiciona informações extras se disponíveis
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
            
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
            
        if hasattr(record, 'ip_address'):
            log_entry['ip_address'] = record.ip_address
            
        if hasattr(record, 'user_agent'):
            log_entry['user_agent'] = record.user_agent
            
        if hasattr(record, 'execution_time'):
            log_entry['execution_time'] = record.execution_time
            
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        return json.dumps(log_entry, ensure_ascii=False)

# Configuração de logging
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    
    'formatters': {
        'verbose': {
            'format': '{asctime} {levelname} {name} {user_info} {request_info} {module}:{funcName}:{lineno} - {message}',
            'style': '{',
        },
        'colored': {
            '()': ColoredFormatter,
            'format': '{asctime} {levelname} {name} {user_info} {request_info} {module}:{funcName}:{lineno} - {message}',
            'style': '{',
        },
        'structured': {
            '()': StructuredFormatter,
        },
        'simple': {
            'format': '{levelname} {name} - {message}',
            'style': '{',
        },
    },
    
    'filters': {
        'require_debug_false': {
            '()': 'django.utils.log.RequireDebugFalse',
        },
        'require_debug_true': {
            '()': 'django.utils.log.RequireDebugTrue',
        },
    },
    
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'colored',
            'filters': ['require_debug_true'],
        },
        'console_production': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
            'filters': ['require_debug_false'],
        },
        'file_debug': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOGS_DIR / 'debug.log',
            'maxBytes': 1024 * 1024 * 10,  # 10MB
            'backupCount': 5,
            'formatter': 'verbose',
            'encoding': 'utf-8',
        },
        'file_info': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOGS_DIR / 'info.log',
            'maxBytes': 1024 * 1024 * 15,  # 15MB
            'backupCount': 10,
            'formatter': 'structured',
            'encoding': 'utf-8',
        },
        'file_error': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOGS_DIR / 'error.log',
            'maxBytes': 1024 * 1024 * 20,  # 20MB
            'backupCount': 10,
            'formatter': 'structured',
            'encoding': 'utf-8',
        },
        'file_security': {
            'level': 'WARNING',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOGS_DIR / 'security.log',
            'maxBytes': 1024 * 1024 * 25,  # 25MB
            'backupCount': 15,
            'formatter': 'structured',
            'encoding': 'utf-8',
        },
        'mail_admins': {
            'level': 'ERROR',
            'class': 'django.utils.log.AdminEmailHandler',
            'filters': ['require_debug_false'],
            'formatter': 'verbose',
        },
    },
    
    'loggers': {
        # Logger raiz
        '': {
            'handlers': ['console', 'console_production', 'file_info'],
            'level': 'INFO',
            'propagate': False,
        },
        
        # Django core
        'django': {
            'handlers': ['console', 'file_info'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.request': {
            'handlers': ['file_error', 'mail_admins'],
            'level': 'ERROR',
            'propagate': False,
        },
        'django.security': {
            'handlers': ['file_security', 'mail_admins'],
            'level': 'WARNING',
            'propagate': False,
        },
        
        # Apps do projeto
        'apps.accounts': {
            'handlers': ['console', 'file_debug', 'file_info'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'apps.articles': {
            'handlers': ['console', 'file_debug', 'file_info'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'apps.config': {
            'handlers': ['console', 'file_debug', 'file_info'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'apps.pages': {
            'handlers': ['console', 'file_debug', 'file_info'],
            'level': 'DEBUG',
            'propagate': False,
        },
        
        # Loggers específicos
        'havoc.security': {
            'handlers': ['file_security', 'mail_admins'],
            'level': 'WARNING',
            'propagate': False,
        },
        'havoc.performance': {
            'handlers': ['file_info'],
            'level': 'INFO',
            'propagate': False,
        },
        'havoc.audit': {
            'handlers': ['file_info', 'file_security'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

def setup_logging():
    """Configura o sistema de logging"""
    import logging.config
    logging.config.dictConfig(LOGGING_CONFIG)
    
    # Log inicial
    logger = logging.getLogger('havoc.startup')
    logger.info("Sistema de logging configurado com sucesso")
    logger.info(f"Logs sendo salvos em: {LOGS_DIR}")

def get_logger(name):
    """
    Retorna um logger configurado
    
    Args:
        name: Nome do logger (ex: 'apps.accounts.services')
    
    Returns:
        Logger configurado
    """
    return logging.getLogger(name)

# Utilitários para logging estruturado
class LogContext:
    """Context manager para adicionar informações extras aos logs"""
    
    def __init__(self, **kwargs):
        self.context = kwargs
        self.old_factory = logging.getLogRecordFactory()
    
    def __enter__(self):
        def record_factory(*args, **kwargs):
            record = self.old_factory(*args, **kwargs)
            for key, value in self.context.items():
                setattr(record, key, value)
            return record
        
        logging.setLogRecordFactory(record_factory)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        logging.setLogRecordFactory(self.old_factory)

def log_with_context(**context):
    """Decorator para adicionar contexto aos logs de uma função"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with LogContext(**context):
                return func(*args, **kwargs)
        return wrapper
    return decorator
