{% extends "base.html" %}
{% load static %}

{% block title %}{{ article.title }} - {{ block.super }}{% endblock %}

{% block meta_description %}{{ article.excerpt|default:article.seo_description }}{% endblock %}

{% block extra_css %}
<style>
.article-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
}

.article-meta {
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 1rem;
    margin-top: 1.5rem;
}

.article-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #333;
}

.article-content h2,
.article-content h3,
.article-content h4 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.article-content p {
    margin-bottom: 1.5rem;
}

.article-content img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    margin: 1.5rem 0;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.article-content blockquote {
    border-left: 4px solid #007bff;
    padding-left: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0 10px 10px 0;
}

.article-actions {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    margin: 2rem 0;
}

.related-articles {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    margin-top: 3rem;
}

.related-article-card {
    transition: transform 0.2s ease;
    border: none;
    border-radius: 10px;
}

.related-article-card:hover {
    transform: translateY(-3px);
}

.comments-section {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-top: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.comment {
    border-left: 3px solid #007bff;
    padding-left: 1rem;
    margin-bottom: 1.5rem;
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0 10px 10px 0;
}

.comment-author {
    font-weight: 600;
    color: #007bff;
}

.comment-date {
    font-size: 0.875rem;
    color: #6c757d;
}

.tag-badge {
    background-color: #e9ecef;
    color: #495057;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    text-decoration: none;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    display: inline-block;
    transition: all 0.2s ease;
}

.tag-badge:hover {
    background-color: #007bff;
    color: white;
    text-decoration: none;
}

.share-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.share-btn {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    text-decoration: none;
    color: white;
    font-size: 0.875rem;
    transition: transform 0.2s ease;
}

.share-btn:hover {
    transform: scale(1.05);
    color: white;
    text-decoration: none;
}

.share-btn.facebook { background: #3b5998; }
.share-btn.twitter { background: #1da1f2; }
.share-btn.linkedin { background: #0077b5; }
.share-btn.whatsapp { background: #25d366; }

@media (max-width: 768px) {
    .article-header {
        padding: 2rem 0;
    }
    
    .article-content {
        font-size: 1rem;
    }
    
    .related-articles,
    .comments-section {
        padding: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Header do Artigo -->
<div class="article-header">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <!-- Categoria -->
                {% if article.category %}
                <a href="{% url 'articles:category_detail' article.category.slug %}" 
                   class="badge bg-light text-dark text-decoration-none mb-3">
                    <i class="fas fa-folder me-1"></i>{{ article.category.name }}
                </a>
                {% endif %}

                <!-- Título -->
                <h1 class="display-4 fw-bold mb-3">{{ article.title }}</h1>

                <!-- Excerpt -->
                {% if article.excerpt %}
                <p class="lead mb-0">{{ article.excerpt }}</p>
                {% endif %}

                <!-- Meta informações -->
                <div class="article-meta">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <i class="fas fa-user fa-lg mb-2"></i>
                            <div>{{ article.author.get_full_name|default:article.author.username }}</div>
                        </div>
                        <div class="col-md-3">
                            <i class="fas fa-calendar fa-lg mb-2"></i>
                            <div>{{ article.created_at|date:"d/m/Y" }}</div>
                        </div>
                        <div class="col-md-3">
                            <i class="fas fa-clock fa-lg mb-2"></i>
                            <div>{{ article.reading_time|default:"5" }} min</div>
                        </div>
                        <div class="col-md-3">
                            <i class="fas fa-eye fa-lg mb-2"></i>
                            <div>{{ article.views }} visualizaç{{ article.views|pluralize:"ão,ões" }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <!-- Conteúdo Principal -->
        <div class="col-lg-8">
            <!-- Imagem Destacada -->
            {% if article.featured_image %}
            <div class="text-center mb-4">
                <img src="{{ article.featured_image.url }}" 
                     class="img-fluid rounded shadow" 
                     alt="{{ article.title }}"
                     style="max-height: 400px; width: 100%; object-fit: cover;">
            </div>
            {% endif %}

            <!-- Conteúdo do Artigo -->
            <div class="article-content">
                {{ article.content|safe }}
            </div>

            <!-- Tags -->
            {% if article.tags.all %}
            <div class="mt-4 mb-4">
                <h6><i class="fas fa-tags me-2"></i>Tags:</h6>
                {% for tag in article.tags.all %}
                <a href="{% url 'articles:search' %}?tag={{ tag.slug }}" class="tag-badge">
                    {{ tag.name }}
                </a>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Ações do Artigo -->
            {% if can_edit %}
            <div class="article-actions">
                <h6><i class="fas fa-cog me-2"></i>Ações do Artigo</h6>
                <div class="d-flex gap-2 flex-wrap">
                    <a href="{% url 'articles:update' article.slug %}" class="btn btn-outline-primary">
                        <i class="fas fa-edit me-1"></i>Editar
                    </a>
                    <a href="{% url 'articles:delete' article.slug %}" class="btn btn-outline-danger">
                        <i class="fas fa-trash me-1"></i>Deletar
                    </a>
                    {% if article.status == 'published' %}
                    <a href="{% url 'articles:toggle_status' article.slug %}" class="btn btn-outline-warning">
                        <i class="fas fa-eye-slash me-1"></i>Despublicar
                    </a>
                    {% else %}
                    <a href="{% url 'articles:toggle_status' article.slug %}" class="btn btn-outline-success">
                        <i class="fas fa-eye me-1"></i>Publicar
                    </a>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Compartilhamento -->
            <div class="article-actions">
                <h6><i class="fas fa-share-alt me-2"></i>Compartilhar</h6>
                <div class="share-buttons">
                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ request.build_absolute_uri }}" 
                       target="_blank" class="share-btn facebook">
                        <i class="fab fa-facebook-f me-1"></i>Facebook
                    </a>
                    <a href="https://twitter.com/intent/tweet?url={{ request.build_absolute_uri }}&text={{ article.title }}" 
                       target="_blank" class="share-btn twitter">
                        <i class="fab fa-twitter me-1"></i>Twitter
                    </a>
                    <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ request.build_absolute_uri }}" 
                       target="_blank" class="share-btn linkedin">
                        <i class="fab fa-linkedin-in me-1"></i>LinkedIn
                    </a>
                    <a href="https://wa.me/?text={{ article.title }} {{ request.build_absolute_uri }}" 
                       target="_blank" class="share-btn whatsapp">
                        <i class="fab fa-whatsapp me-1"></i>WhatsApp
                    </a>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Informações do Autor -->
            <div class="card mb-4">
                <div class="card-body text-center">
                    <h6 class="card-title">Sobre o Autor</h6>
                    <h5>{{ article.author.get_full_name|default:article.author.username }}</h5>
                    {% if article.author.bio %}
                    <p class="card-text">{{ article.author.bio }}</p>
                    {% endif %}
                    <small class="text-muted">
                        Membro desde {{ article.author.date_joined|date:"M/Y" }}
                    </small>
                </div>
            </div>

            <!-- Navegação -->
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">Navegação</h6>
                    <div class="d-grid gap-2">
                        <a href="{% url 'articles:list' %}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-1"></i>Voltar aos Artigos
                        </a>
                        {% if article.category %}
                        <a href="{% url 'articles:category_detail' article.category.slug %}" class="btn btn-outline-secondary">
                            <i class="fas fa-folder me-1"></i>Mais de {{ article.category.name }}
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Artigos Relacionados -->
    {% if related_articles %}
    <div class="related-articles">
        <h3 class="mb-4"><i class="fas fa-newspaper me-2"></i>Artigos Relacionados</h3>
        <div class="row">
            {% for related in related_articles %}
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card related-article-card h-100">
                    {% if related.featured_image %}
                    <img src="{{ related.featured_image.url }}" 
                         class="card-img-top" 
                         alt="{{ related.title }}"
                         style="height: 150px; object-fit: cover;">
                    {% endif %}
                    <div class="card-body">
                        <h6 class="card-title">
                            <a href="{% url 'articles:detail' related.slug %}" 
                               class="text-decoration-none">
                                {{ related.title|truncatewords:8 }}
                            </a>
                        </h6>
                        <small class="text-muted">
                            {{ related.created_at|date:"d/m/Y" }}
                        </small>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scroll para links internos
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Highlight de código (se houver)
    if (typeof hljs !== 'undefined') {
        hljs.highlightAll();
    }

    // Lazy loading para imagens
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
});
</script>
{% endblock %}
