# This file is distributed under the same license as the Django package.
#
# Translators:
# aki<PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2016
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# Masaya, 2023
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2012-2018,2021,2023
# TANIGUCHI Taichi, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2011
# <AUTHOR> <EMAIL>, 2015
# 余田大輝, 2024
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-05-22 11:46-0300\n"
"PO-Revision-Date: 2024-08-07 07:05+0000\n"
"Last-Translator: 余田大輝, 2024\n"
"Language-Team: Japanese (http://app.transifex.com/django/django/language/"
"ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#, python-format
msgid "Delete selected %(verbose_name_plural)s"
msgstr "選択された %(verbose_name_plural)s の削除"

#, python-format
msgid "Successfully deleted %(count)d %(items)s."
msgstr "%(count)d 個の %(items)s を削除しました。"

#, python-format
msgid "Cannot delete %(name)s"
msgstr "%(name)s が削除できません"

msgid "Are you sure?"
msgstr "よろしいですか？"

msgid "Administration"
msgstr "管理"

msgid "All"
msgstr "全て"

msgid "Yes"
msgstr "はい"

msgid "No"
msgstr "いいえ"

msgid "Unknown"
msgstr "不明"

msgid "Any date"
msgstr "いつでも"

msgid "Today"
msgstr "今日"

msgid "Past 7 days"
msgstr "過去 7 日間"

msgid "This month"
msgstr "今月"

msgid "This year"
msgstr "今年"

msgid "No date"
msgstr "日付なし"

msgid "Has date"
msgstr "日付あり"

msgid "Empty"
msgstr "空"

msgid "Not empty"
msgstr "空でない"

#, python-format
msgid ""
"Please enter the correct %(username)s and password for a staff account. Note "
"that both fields may be case-sensitive."
msgstr ""
"スタッフアカウントの正しい%(username)sとパスワードを入力してください。どちら"
"のフィールドも大文字と小文字は区別されます。"

msgid "Action:"
msgstr "操作:"

#, python-format
msgid "Add another %(verbose_name)s"
msgstr "%(verbose_name)s の追加"

msgid "Remove"
msgstr "削除"

msgid "Addition"
msgstr "追加"

msgid "Change"
msgstr "変更"

msgid "Deletion"
msgstr "削除"

msgid "action time"
msgstr "操作時刻"

msgid "user"
msgstr "ユーザー"

msgid "content type"
msgstr "コンテンツタイプ"

msgid "object id"
msgstr "オブジェクト ID"

#. Translators: 'repr' means representation
#. (https://docs.python.org/library/functions.html#repr)
msgid "object repr"
msgstr "オブジェクトの文字列表現"

msgid "action flag"
msgstr "操作種別"

msgid "change message"
msgstr "変更メッセージ"

msgid "log entry"
msgstr "ログエントリー"

msgid "log entries"
msgstr "ログエントリー"

#, python-format
msgid "Added “%(object)s”."
msgstr "“%(object)s” を追加しました。"

#, python-format
msgid "Changed “%(object)s” — %(changes)s"
msgstr "“%(object)s” を変更しました — %(changes)s"

#, python-format
msgid "Deleted “%(object)s.”"
msgstr "“%(object)s” を削除しました。"

msgid "LogEntry Object"
msgstr "ログエントリー オブジェクト"

#, python-brace-format
msgid "Added {name} “{object}”."
msgstr "{name} “{object}” を追加しました。"

msgid "Added."
msgstr "追加されました。"

msgid "and"
msgstr "と"

#, python-brace-format
msgid "Changed {fields} for {name} “{object}”."
msgstr "{name} “{object}” の {fields} を変更しました。"

#, python-brace-format
msgid "Changed {fields}."
msgstr "{fields} を変更しました。"

#, python-brace-format
msgid "Deleted {name} “{object}”."
msgstr "{name} “{object}” を削除しました。"

msgid "No fields changed."
msgstr "変更はありませんでした。"

msgid "None"
msgstr "None"

msgid "Hold down “Control”, or “Command” on a Mac, to select more than one."
msgstr ""
"複数選択するときには Control キーを押したまま選択してください。Mac は "
"Command キーを使ってください"

msgid "Select this object for an action - {}"
msgstr "アクション用にこのオブジェクトを選択 - {}"

#, python-brace-format
msgid "The {name} “{obj}” was added successfully."
msgstr "{name} “{obj}” を追加しました。"

msgid "You may edit it again below."
msgstr "以下で再度編集できます。"

#, python-brace-format
msgid ""
"The {name} “{obj}” was added successfully. You may add another {name} below."
msgstr "{name} “{obj}” を追加しました。別の {name} を以下から追加できます。"

#, python-brace-format
msgid ""
"The {name} “{obj}” was changed successfully. You may edit it again below."
msgstr "{name} “{obj}” を変更しました。以下から再度編集できます。"

#, python-brace-format
msgid ""
"The {name} “{obj}” was changed successfully. You may add another {name} "
"below."
msgstr "{name} “{obj}” を変更しました。 別の {name} を以下から追加できます。"

#, python-brace-format
msgid "The {name} “{obj}” was changed successfully."
msgstr "{name} “{obj}” を変更しました。"

msgid ""
"Items must be selected in order to perform actions on them. No items have "
"been changed."
msgstr ""
"操作を実行するには、対象を選択する必要があります。何も変更されませんでした。"

msgid "No action selected."
msgstr "操作が選択されていません。"

#, python-format
msgid "The %(name)s “%(obj)s” was deleted successfully."
msgstr "%(name)s “%(obj)s” を削除しました。"

#, python-format
msgid "%(name)s with ID “%(key)s” doesn’t exist. Perhaps it was deleted?"
msgstr ""
"ID “%(key)s” の%(name)sは見つかりませんでした。削除された可能性があります。"

#, python-format
msgid "Add %s"
msgstr "%s を追加"

#, python-format
msgid "Change %s"
msgstr "%s を変更"

#, python-format
msgid "View %s"
msgstr "%sを表示"

msgid "Database error"
msgstr "データベースエラー"

#, python-format
msgid "%(count)s %(name)s was changed successfully."
msgid_plural "%(count)s %(name)s were changed successfully."
msgstr[0] "%(count)s 個の %(name)s を変更しました。"

#, python-format
msgid "%(total_count)s selected"
msgid_plural "All %(total_count)s selected"
msgstr[0] "%(total_count)s 個選択されました"

#, python-format
msgid "0 of %(cnt)s selected"
msgstr "%(cnt)s個の内ひとつも選択されていません"

#, python-format
msgid "Change history: %s"
msgstr "変更履歴: %s"

#. Translators: Model verbose name and instance
#. representation, suitable to be an item in a
#. list.
#, python-format
msgid "%(class_name)s %(instance)s"
msgstr "%(class_name)s %(instance)s"

#, python-format
msgid ""
"Deleting %(class_name)s %(instance)s would require deleting the following "
"protected related objects: %(related_objects)s"
msgstr ""
"%(class_name)s %(instance)s を削除するには以下の保護された関連オブジェクトを"
"削除することになります: %(related_objects)s"

msgid "Django site admin"
msgstr "Django サイト管理"

msgid "Django administration"
msgstr "Django 管理サイト"

msgid "Site administration"
msgstr "サイト管理"

msgid "Log in"
msgstr "ログイン"

#, python-format
msgid "%(app)s administration"
msgstr "%(app)s 管理"

msgid "Page not found"
msgstr "ページが見つかりません"

msgid "We’re sorry, but the requested page could not be found."
msgstr "申し訳ありませんが、お探しのページは見つかりませんでした。"

msgid "Home"
msgstr "ホーム"

msgid "Server error"
msgstr "サーバーエラー"

msgid "Server error (500)"
msgstr "サーバーエラー (500)"

msgid "Server Error <em>(500)</em>"
msgstr "サーバーエラー <em>(500)</em>"

msgid ""
"There’s been an error. It’s been reported to the site administrators via "
"email and should be fixed shortly. Thanks for your patience."
msgstr ""
"エラーが発生しました。サイト管理者にメールで報告されたので、修正されるまでし"
"ばらくお待ちください。"

msgid "Run the selected action"
msgstr "選択された操作を実行"

msgid "Go"
msgstr "実行"

msgid "Click here to select the objects across all pages"
msgstr "全ページの項目を選択するにはここをクリック"

#, python-format
msgid "Select all %(total_count)s %(module_name)s"
msgstr "%(total_count)s個ある%(module_name)s を全て選択"

msgid "Clear selection"
msgstr "選択を解除"

msgid "Breadcrumbs"
msgstr "パンくずリスト"

#, python-format
msgid "Models in the %(name)s application"
msgstr "%(name)s アプリケーション内のモデル"

msgid "Add"
msgstr "追加"

msgid "View"
msgstr "表示"

msgid "You don’t have permission to view or edit anything."
msgstr "表示または変更のためのパーミッションがありません。"

msgid ""
"First, enter a username and password. Then, you’ll be able to edit more user "
"options."
msgstr ""
"まずユーザー名とパスワードを登録してください。その後詳細情報が編集可能になり"
"ます。"

msgid "Enter a username and password."
msgstr "ユーザー名とパスワードを入力してください。"

msgid "Change password"
msgstr "パスワードの変更"

msgid "Set password"
msgstr "パスワードを設定"

msgid "Please correct the error below."
msgid_plural "Please correct the errors below."
msgstr[0] "下記のエラーを修正してください。"

#, python-format
msgid "Enter a new password for the user <strong>%(username)s</strong>."
msgstr ""
"<strong>%(username)s</strong>さんの新しいパスワードを入力してください。"

msgid ""
"This action will <strong>enable</strong> password-based authentication for "
"this user."
msgstr ""
"このアクションは、このユーザーに対するパスワードによる認証を有効にします。"

msgid "Disable password-based authentication"
msgstr "パスワードによる認証の無効化"

msgid "Enable password-based authentication"
msgstr "パスワードによる認証の有効化"

msgid "Skip to main content"
msgstr "スキップしてメインコンテンツへ"

msgid "Welcome,"
msgstr "ようこそ"

msgid "View site"
msgstr "サイトを表示"

msgid "Documentation"
msgstr "ドキュメント"

msgid "Log out"
msgstr "ログアウト"

#, python-format
msgid "Add %(name)s"
msgstr "%(name)s を追加"

msgid "History"
msgstr "履歴"

msgid "View on site"
msgstr "サイト上で表示"

msgid "Filter"
msgstr "フィルター"

msgid "Hide counts"
msgstr "件数を非表示"

msgid "Show counts"
msgstr "件数を表示"

msgid "Clear all filters"
msgstr "全てのフィルターを解除"

msgid "Remove from sorting"
msgstr "ソート条件から外します"

#, python-format
msgid "Sorting priority: %(priority_number)s"
msgstr "ソート優先順位: %(priority_number)s"

msgid "Toggle sorting"
msgstr "昇順降順を切り替えます"

msgid "Toggle theme (current theme: auto)"
msgstr "テーマを切り替え (現在のテーマ: 自動)"

msgid "Toggle theme (current theme: light)"
msgstr "テーマを切り替え (現在のテーマ: ライト)"

msgid "Toggle theme (current theme: dark)"
msgstr "テーマを切り替え (現在のテーマ: ダーク)"

msgid "Delete"
msgstr "削除"

#, python-format
msgid ""
"Deleting the %(object_name)s '%(escaped_object)s' would result in deleting "
"related objects, but your account doesn't have permission to delete the "
"following types of objects:"
msgstr ""
"%(object_name)s '%(escaped_object)s' の削除時に関連づけられたオブジェクトも削"
"除しようとしましたが、あなたのアカウントには以下のタイプのオブジェクトを削除"
"するパーミッションがありません:"

#, python-format
msgid ""
"Deleting the %(object_name)s '%(escaped_object)s' would require deleting the "
"following protected related objects:"
msgstr ""
"%(object_name)s '%(escaped_object)s' を削除するには以下の保護された関連オブ"
"ジェクトを削除することになります:"

#, python-format
msgid ""
"Are you sure you want to delete the %(object_name)s \"%(escaped_object)s\"? "
"All of the following related items will be deleted:"
msgstr ""
"%(object_name)s \"%(escaped_object)s\"を削除しますか？ 関連づけられている以下"
"のオブジェクトも全て削除されます:"

msgid "Objects"
msgstr "オブジェクト"

msgid "Yes, I’m sure"
msgstr "はい、大丈夫です"

msgid "No, take me back"
msgstr "戻る"

msgid "Delete multiple objects"
msgstr "複数のオブジェクトを削除します"

#, python-format
msgid ""
"Deleting the selected %(objects_name)s would result in deleting related "
"objects, but your account doesn't have permission to delete the following "
"types of objects:"
msgstr ""
"選択した %(objects_name)s を削除すると関連するオブジェクトも削除しますが、あ"
"なたのアカウントは以下のオブジェクト型を削除する権限がありません:"

#, python-format
msgid ""
"Deleting the selected %(objects_name)s would require deleting the following "
"protected related objects:"
msgstr ""
"選択した %(objects_name)s を削除すると以下の保護された関連オブジェクトを削除"
"することになります:"

#, python-format
msgid ""
"Are you sure you want to delete the selected %(objects_name)s? All of the "
"following objects and their related items will be deleted:"
msgstr ""
"本当に選択した %(objects_name)s を削除しますか？ 以下の全てのオブジェクトと関"
"連する要素が削除されます:"

msgid "Delete?"
msgstr "削除しますか?"

#, python-format
msgid " By %(filter_title)s "
msgstr "%(filter_title)s で絞り込む"

msgid "Summary"
msgstr "概要"

msgid "Recent actions"
msgstr "最近行った操作"

msgid "My actions"
msgstr "自分の操作"

msgid "None available"
msgstr "利用不可"

msgid "Added:"
msgstr "追加されました:"

msgid "Changed:"
msgstr "変更されました:"

msgid "Deleted:"
msgstr "削除されました:"

msgid "Unknown content"
msgstr "不明なコンテント"

msgid ""
"Something’s wrong with your database installation. Make sure the appropriate "
"database tables have been created, and make sure the database is readable by "
"the appropriate user."
msgstr ""
"データベースのインストールに問題があります。適切なデータベーステーブルが作ら"
"れているか、適切なユーザーがデータベースを読み込み可能かを確認してください。"

#, python-format
msgid ""
"You are authenticated as %(username)s, but are not authorized to access this "
"page. Would you like to login to a different account?"
msgstr ""
"あなたは %(username)s として認証されましたが、このページへのアクセス許可があ"
"りません。他のアカウントでログインしますか?"

msgid "Forgotten your password or username?"
msgstr "パスワードまたはユーザー名を忘れましたか？"

msgid "Toggle navigation"
msgstr "ナビゲーションを切り替えます"

msgid "Sidebar"
msgstr "サイドバー"

msgid "Start typing to filter…"
msgstr "絞り込みの入力..."

msgid "Filter navigation items"
msgstr "ナビゲーション項目の絞り込み"

msgid "Date/time"
msgstr "日付/時刻"

msgid "User"
msgstr "ユーザー"

msgid "Action"
msgstr "操作"

msgid "entry"
msgid_plural "entries"
msgstr[0] "エントリー"

msgid ""
"This object doesn’t have a change history. It probably wasn’t added via this "
"admin site."
msgstr ""
"このオブジェクトには変更履歴がありません。おそらくこの管理サイトで追加したも"
"のではありません。"

msgid "Show all"
msgstr "全件表示"

msgid "Save"
msgstr "保存"

msgid "Popup closing…"
msgstr "ポップアップを閉じています..."

msgid "Search"
msgstr "検索"

#, python-format
msgid "%(counter)s result"
msgid_plural "%(counter)s results"
msgstr[0] "結果 %(counter)s"

#, python-format
msgid "%(full_result_count)s total"
msgstr "全 %(full_result_count)s 件"

msgid "Save as new"
msgstr "新規保存"

msgid "Save and add another"
msgstr "保存してもう一つ追加"

msgid "Save and continue editing"
msgstr "保存して編集を続ける"

msgid "Save and view"
msgstr "保存して表示"

msgid "Close"
msgstr "閉じる"

#, python-format
msgid "Change selected %(model)s"
msgstr "選択された %(model)s の変更"

#, python-format
msgid "Add another %(model)s"
msgstr "%(model)s の追加"

#, python-format
msgid "Delete selected %(model)s"
msgstr "選択された %(model)s を削除"

#, python-format
msgid "View selected %(model)s"
msgstr "選択された %(model)s を表示"

msgid "Thanks for spending some quality time with the web site today."
msgstr "ご利用ありがとうございました。"

msgid "Log in again"
msgstr "もう一度ログイン"

msgid "Password change"
msgstr "パスワードの変更"

msgid "Your password was changed."
msgstr "あなたのパスワードは変更されました"

msgid ""
"Please enter your old password, for security’s sake, and then enter your new "
"password twice so we can verify you typed it in correctly."
msgstr ""
"セキュリティ上の理由から元のパスワードの入力が必要です。新しいパスワードは正"
"しく入力したか確認できるように二度入力してください。"

msgid "Change my password"
msgstr "パスワードの変更"

msgid "Password reset"
msgstr "パスワードをリセット"

msgid "Your password has been set.  You may go ahead and log in now."
msgstr "パスワードがセットされました。ログインしてください。"

msgid "Password reset confirmation"
msgstr "パスワードリセットの確認"

msgid ""
"Please enter your new password twice so we can verify you typed it in "
"correctly."
msgstr "確認のために、新しいパスワードを二回入力してください。"

msgid "New password:"
msgstr "新しいパスワード:"

msgid "Confirm password:"
msgstr "新しいパスワード (確認用) :"

msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a new password reset."
msgstr ""
"パスワードリセットのリンクが不正です。おそらくこのリンクは既に使われていま"
"す。もう一度パスワードリセットしてください。"

msgid ""
"We’ve emailed you instructions for setting your password, if an account "
"exists with the email you entered. You should receive them shortly."
msgstr ""
"入力されたメールアドレスを持つアカウントが存在する場合、パスワードを設定する"
"ためのメールを送信しました。すぐに届くはずです。"

msgid ""
"If you don’t receive an email, please make sure you’ve entered the address "
"you registered with, and check your spam folder."
msgstr ""
"メールが届かない場合は、登録したメールアドレスを入力したか確認し、スパムフォ"
"ルダに入っていないか確認してください。"

#, python-format
msgid ""
"You're receiving this email because you requested a password reset for your "
"user account at %(site_name)s."
msgstr ""
"このメールは %(site_name)s で、あなたのアカウントのパスワードリセットが要求さ"
"れたため、送信されました。"

msgid "Please go to the following page and choose a new password:"
msgstr "次のページで新しいパスワードを選んでください:"

msgid "Your username, in case you’ve forgotten:"
msgstr "あなたのユーザー名 (もし忘れていたら):"

msgid "Thanks for using our site!"
msgstr "ご利用ありがとうございました！"

#, python-format
msgid "The %(site_name)s team"
msgstr " %(site_name)s チーム"

msgid ""
"Forgotten your password? Enter your email address below, and we’ll email "
"instructions for setting a new one."
msgstr ""
"パスワードを忘れましたか? メールアドレスを以下に入力すると、新しいパスワード"
"の設定方法をお知らせします。"

msgid "Email address:"
msgstr "メールアドレス:"

msgid "Reset my password"
msgstr "パスワードをリセット"

msgid "Select all objects on this page for an action"
msgstr "アクション用にこのページのすべてのオブジェクトを選択"

msgid "All dates"
msgstr "いつでも"

#, python-format
msgid "Select %s"
msgstr "%s を選択"

#, python-format
msgid "Select %s to change"
msgstr "変更する %s を選択"

#, python-format
msgid "Select %s to view"
msgstr "表示する%sを選択"

msgid "Date:"
msgstr "日付:"

msgid "Time:"
msgstr "時刻:"

msgid "Lookup"
msgstr "検索"

msgid "Currently:"
msgstr "現在の値:"

msgid "Change:"
msgstr "変更後:"
