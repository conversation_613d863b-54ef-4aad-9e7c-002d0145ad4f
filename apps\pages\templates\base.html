{% load static %}
<!DOCTYPE html>
<html lang="pt-br" class="h-100">
<head>
    {% include 'includes/_head.html' %}
</head>
<body class="d-flex flex-column h-100">
    <!-- Google Tag Manager (noscript) -->
    {% if google_tag_manager_id %}
    <noscript>
        <iframe src="https://www.googletagmanager.com/ns.html?id={{ google_tag_manager_id }}" 
                height="0" width="0" style="display:none;visibility:hidden"></iframe>
    </noscript>
    {% endif %}

    <!-- Skip to main content -->
    <a class="visually-hidden-focusable" href="#main-content">Pular para o conteúdo principal</a>

    <!-- Header -->
    <header>
        {% include 'includes/_nav.html' %}
    </header>

    <!-- Toasts -->
    {% include 'includes/_toasts.html' %}

    <!-- Main Content -->
    <main id="main-content" class="flex-shrink-0">
        {% block content %}
        <!-- Default content if no content block is provided -->
        <div class="container my-5">
            <div class="row">
                <div class="col-12 text-center">
                    <h1>Bem-vindo ao Havoc</h1>
                    <p class="lead">Sistema de gerenciamento de conteúdo moderno</p>
                </div>
            </div>
        </div>
        {% endblock %}
    </main>

    <!-- Footer -->
    {% include 'includes/_footer.html' %}

    <!-- JavaScript -->
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" 
            integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" 
            crossorigin="anonymous"></script>

    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>

    <!-- Extra JavaScript -->
    {% block extra_js %}{% endblock %}

    <!-- Structured Data -->
    {% block structured_data %}
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "Havoc",
        "description": "Sistema de gerenciamento de conteúdo moderno",
        "url": "{{ request.build_absolute_uri }}",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ request.build_absolute_uri }}{% url 'articles:search' %}?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>
    {% endblock %}
</body>
</html>
