from django.db.models.signals import pre_save, post_save, post_delete
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from apps.accounts.services.image_service import ImageService
import logging

User = get_user_model()
logger = logging.getLogger(__name__)

@receiver(pre_save, sender=User)
def handle_user_pre_save(sender, instance, **kwargs):
    """
    Gera slug automaticamente antes de salvar
    """
    if not instance.slug:
        instance.slug = instance.generate_slug()

@receiver(post_save, sender=User)
def handle_user_avatar_processing(sender, instance, created, **kwargs):
    """
    Processa o avatar do usuário após salvar
    """
    if instance.avatar and hasattr(instance.avatar, 'path'):
        try:
            # Redimensiona o avatar se necessário
            ImageService.resize_avatar(instance.avatar.path)
        except Exception as e:
            logger.error(f"Erro ao processar avatar do usuário {instance.id}: {str(e)}")

@receiver(post_delete, sender=User)
def handle_user_avatar_cleanup(sender, instance, **kwargs):
    """
    Remove o arquivo de avatar quando o usuário é deletado
    """
    if instance.avatar and hasattr(instance.avatar, 'path'):
        try:
            ImageService.delete_image_file(instance.avatar.path)
        except Exception as e:
            logger.error(f"Erro ao deletar avatar do usuário {instance.id}: {str(e)}")
