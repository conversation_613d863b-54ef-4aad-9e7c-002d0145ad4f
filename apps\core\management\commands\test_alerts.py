"""
Comando para testar o sistema de alertas
"""

import logging
import time
from django.core.management.base import BaseCommand
from apps.core.alerts.alert_system import alert_manager


class Command(BaseCommand):
    help = 'Testa o sistema de alertas automáticos'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--type',
            type=str,
            choices=['error_rate', 'critical_errors', 'security_events', 'slow_requests', 'database_errors'],
            default='error_rate',
            help='Tipo de alerta para testar'
        )
        
        parser.add_argument(
            '--count',
            type=int,
            default=15,
            help='Número de eventos para gerar'
        )
        
        parser.add_argument(
            '--interval',
            type=float,
            default=1.0,
            help='Intervalo entre eventos em segundos'
        )
    
    def handle(self, *args, **options):
        alert_type = options['type']
        count = options['count']
        interval = options['interval']
        
        self.stdout.write(
            self.style.SUCCESS(f'Testando alertas do tipo: {alert_type}')
        )
        self.stdout.write(f'Gerando {count} eventos com intervalo de {interval}s')
        
        # Configura logger para o teste
        logger = logging.getLogger('havoc.test_alerts')
        
        # Gera eventos baseado no tipo
        for i in range(count):
            self._generate_event(logger, alert_type, i + 1)
            
            if i < count - 1:  # Não espera após o último evento
                time.sleep(interval)
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ {count} eventos gerados com sucesso!')
        )
        self.stdout.write('Verifique os logs e notificações de alerta.')
    
    def _generate_event(self, logger, alert_type, event_number):
        """Gera um evento específico para teste"""
        
        if alert_type == 'error_rate':
            logger.error(
                f"Erro de teste #{event_number} - Simulação de erro para teste de alertas",
                extra={
                    'request_id': f'test_req_{event_number}',
                    'user_id': 1,
                    'ip_address': '127.0.0.1'
                }
            )
            
        elif alert_type == 'critical_errors':
            logger.critical(
                f"Erro crítico de teste #{event_number} - Sistema em estado crítico",
                extra={
                    'request_id': f'test_req_{event_number}',
                    'error_type': 'SystemCriticalError',
                    'component': 'database'
                }
            )
            
        elif alert_type == 'security_events':
            logger.warning(
                f"Evento de segurança #{event_number} - Tentativa de acesso suspeito",
                extra={
                    'security_event': 'suspicious_login_attempt',
                    'ip_address': f'192.168.1.{event_number}',
                    'user_agent': 'suspicious_bot',
                    'attempted_email': '<EMAIL>'
                }
            )
            
        elif alert_type == 'slow_requests':
            logger.warning(
                f"Request lento #{event_number} - Operação demorou muito para completar",
                extra={
                    'performance_issue': 'slow_operation',
                    'execution_time': 5.5 + (event_number * 0.1),
                    'request_id': f'test_req_{event_number}',
                    'path': '/api/slow-endpoint'
                }
            )
            
        elif alert_type == 'database_errors':
            logger.error(
                f"Erro de database #{event_number} - Falha na conexão com o banco de dados",
                extra={
                    'request_id': f'test_req_{event_number}',
                    'database_error': 'connection_timeout',
                    'query': 'SELECT * FROM users WHERE id = %s'
                }
            )
        
        self.stdout.write(f'  📝 Evento {event_number}/{alert_type} gerado')


class TestAlertsCommand(BaseCommand):
    """Comando adicional para testes mais específicos"""
    help = 'Executa testes específicos do sistema de alertas'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--scenario',
            type=str,
            choices=['burst', 'sustained', 'mixed'],
            default='burst',
            help='Cenário de teste'
        )
    
    def handle(self, *args, **options):
        scenario = options['scenario']
        
        self.stdout.write(
            self.style.SUCCESS(f'Executando cenário de teste: {scenario}')
        )
        
        if scenario == 'burst':
            self._test_burst_scenario()
        elif scenario == 'sustained':
            self._test_sustained_scenario()
        elif scenario == 'mixed':
            self._test_mixed_scenario()
    
    def _test_burst_scenario(self):
        """Testa rajada de erros em pouco tempo"""
        self.stdout.write('🔥 Testando rajada de erros...')
        
        logger = logging.getLogger('havoc.test_burst')
        
        # Gera 15 erros em 30 segundos
        for i in range(15):
            logger.error(
                f"Erro em rajada #{i+1}",
                extra={'request_id': f'burst_{i+1}'}
            )
            time.sleep(2)
        
        self.stdout.write('✅ Teste de rajada concluído')
    
    def _test_sustained_scenario(self):
        """Testa erros sustentados ao longo do tempo"""
        self.stdout.write('⏱️ Testando erros sustentados...')
        
        logger = logging.getLogger('havoc.test_sustained')
        
        # Gera 1 erro a cada 30 segundos por 5 minutos
        for i in range(10):
            logger.error(
                f"Erro sustentado #{i+1}",
                extra={'request_id': f'sustained_{i+1}'}
            )
            if i < 9:
                self.stdout.write(f'  Aguardando 30s... ({i+1}/10)')
                time.sleep(30)
        
        self.stdout.write('✅ Teste sustentado concluído')
    
    def _test_mixed_scenario(self):
        """Testa cenário misto com diferentes tipos de eventos"""
        self.stdout.write('🎭 Testando cenário misto...')
        
        logger = logging.getLogger('havoc.test_mixed')
        
        events = [
            ('error', 'Erro de aplicação'),
            ('security', 'Tentativa de login suspeita'),
            ('performance', 'Request lento detectado'),
            ('critical', 'Falha crítica do sistema'),
            ('database', 'Erro de conexão com database'),
        ]
        
        for i, (event_type, message) in enumerate(events * 3):  # Repete 3 vezes
            extra = {'request_id': f'mixed_{i+1}'}
            
            if event_type == 'security':
                extra['security_event'] = 'suspicious_activity'
            elif event_type == 'performance':
                extra['performance_issue'] = 'slow_operation'
                extra['execution_time'] = 6.0
            
            if event_type == 'critical':
                logger.critical(message, extra=extra)
            elif event_type in ['error', 'database']:
                logger.error(message, extra=extra)
            else:
                logger.warning(message, extra=extra)
            
            time.sleep(5)  # 5 segundos entre eventos
        
        self.stdout.write('✅ Teste misto concluído')
