"""
Testes para o sistema de alertas
"""

import logging
import time
from unittest.mock import patch, Mock
from django.test import TestCase, override_settings
from django.core.cache import cache
from apps.core.alerts.alert_system import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, AlertLevel


class AlertManagerTest(TestCase):
    """Testes para AlertManager"""
    
    def setUp(self):
        """Configuração inicial para os testes"""
        self.alert_manager = AlertManager()
        cache.clear()  # Limpa cache entre testes
    
    def test_load_alert_rules(self):
        """Testa carregamento das regras de alerta"""
        rules = self.alert_manager._load_alert_rules()
        
        self.assertIn('error_rate', rules)
        self.assertIn('critical_errors', rules)
        self.assertIn('security_events', rules)
        self.assertEqual(rules['error_rate']['threshold'], 10)
        self.assertEqual(rules['critical_errors']['level'], AlertLevel.CRITICAL)
    
    def test_load_notification_channels(self):
        """Testa carregamento dos canais de notificação"""
        channels = self.alert_manager._load_notification_channels()
        
        self.assertIn('email', channels)
        self.assertIn('webhook', channels)
        self.assertIn('slack', channels)
        self.assertTrue(channels['email']['enabled'])
    
    def test_extract_log_data(self):
        """Testa extração de dados do log"""
        # Cria um log record de teste
        logger = logging.getLogger('test')
        record = logger.makeRecord(
            name='test',
            level=logging.ERROR,
            fn='test.py',
            lno=10,
            msg='Test error message',
            args=(),
            exc_info=None
        )
        
        # Adiciona atributos extras
        record.user_id = 123
        record.request_id = 'req_123'
        record.ip_address = '127.0.0.1'
        
        log_data = self.alert_manager._extract_log_data(record)
        
        self.assertEqual(log_data['level'], 'ERROR')
        self.assertEqual(log_data['message'], 'Test error message')
        self.assertEqual(log_data['user_id'], 123)
        self.assertEqual(log_data['request_id'], 'req_123')
        self.assertEqual(log_data['ip_address'], '127.0.0.1')
    
    def test_should_check_rule_error_rate(self):
        """Testa se deve verificar regra de taxa de erro"""
        log_data = {'level': 'ERROR', 'message': 'Test error'}
        
        should_check = self.alert_manager._should_check_rule('error_rate', log_data)
        self.assertTrue(should_check)
        
        log_data['level'] = 'INFO'
        should_check = self.alert_manager._should_check_rule('error_rate', log_data)
        self.assertFalse(should_check)
    
    def test_should_check_rule_security_events(self):
        """Testa se deve verificar regra de eventos de segurança"""
        log_data = {'security_event': 'suspicious_login'}
        
        should_check = self.alert_manager._should_check_rule('security_events', log_data)
        self.assertTrue(should_check)
        
        log_data = {'level': 'ERROR'}
        should_check = self.alert_manager._should_check_rule('security_events', log_data)
        self.assertFalse(should_check)
    
    @patch('apps.core.alerts.alert_system.AlertManager._trigger_alert')
    def test_check_alert_rule_threshold_reached(self, mock_trigger):
        """Testa disparo de alerta quando threshold é atingido"""
        rule_config = {
            'threshold': 3,
            'window': 300,
            'level': AlertLevel.HIGH,
            'description': 'Test alert'
        }
        
        log_data = {
            'level': 'ERROR',
            'message': 'Test error',
            'timestamp': time.time()
        }
        
        # Gera 3 eventos para atingir o threshold
        for i in range(3):
            log_data['timestamp'] = time.time()
            self.alert_manager._check_alert_rule('test_rule', rule_config, log_data)
            time.sleep(0.1)  # Pequeno delay para timestamps diferentes
        
        # Verifica se alerta foi disparado
        mock_trigger.assert_called_once()
    
    @patch('apps.core.alerts.alert_system.AlertManager._trigger_alert')
    def test_check_alert_rule_threshold_not_reached(self, mock_trigger):
        """Testa que alerta não é disparado quando threshold não é atingido"""
        rule_config = {
            'threshold': 5,
            'window': 300,
            'level': AlertLevel.HIGH,
            'description': 'Test alert'
        }
        
        log_data = {
            'level': 'ERROR',
            'message': 'Test error',
            'timestamp': time.time()
        }
        
        # Gera apenas 2 eventos (menos que o threshold)
        for i in range(2):
            log_data['timestamp'] = time.time()
            self.alert_manager._check_alert_rule('test_rule', rule_config, log_data)
        
        # Verifica que alerta não foi disparado
        mock_trigger.assert_not_called()
    
    @patch('apps.core.alerts.alert_system.AlertManager._send_notifications')
    def test_trigger_alert(self, mock_send):
        """Testa disparo de alerta"""
        rule_config = {
            'level': AlertLevel.HIGH,
            'description': 'Test alert',
            'threshold': 3,
            'window': 300
        }
        
        events = [
            {'timestamp': time.time(), 'data': {'message': 'Error 1'}},
            {'timestamp': time.time(), 'data': {'message': 'Error 2'}},
            {'timestamp': time.time(), 'data': {'message': 'Error 3'}},
        ]
        
        self.alert_manager._trigger_alert('test_rule', rule_config, events)
        
        # Verifica se notificações foram enviadas
        mock_send.assert_called_once()
        
        # Verifica se cooldown foi definido
        cooldown_key = "alert_cooldown_test_rule"
        self.assertTrue(cache.get(cooldown_key))
    
    @patch('smtplib.SMTP')
    def test_send_email_alert(self, mock_smtp):
        """Testa envio de alerta por email"""
        mock_server = Mock()
        mock_smtp.return_value.__enter__.return_value = mock_server
        
        alert_data = {
            'level': AlertLevel.HIGH,
            'description': 'Test alert',
            'rule_name': 'test_rule',
            'event_count': 5,
            'threshold': 3,
            'window': 300,
            'events': [],
            'timestamp': time.time()
        }
        
        config = {
            'recipients': ['<EMAIL>'],
            'smtp_host': 'localhost',
            'smtp_port': 587,
            'smtp_user': 'user',
            'smtp_password': 'pass',
            'use_tls': True
        }
        
        self.alert_manager._send_email_alert(alert_data, config)
        
        # Verifica se SMTP foi configurado corretamente
        mock_smtp.assert_called_once_with('localhost', 587)
        mock_server.starttls.assert_called_once()
        mock_server.login.assert_called_once_with('user', 'pass')
        mock_server.send_message.assert_called_once()
    
    @patch('requests.post')
    def test_send_webhook_alert(self, mock_post):
        """Testa envio de alerta via webhook"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response
        
        alert_data = {
            'level': AlertLevel.HIGH,
            'description': 'Test alert',
            'rule_name': 'test_rule',
            'event_count': 5,
            'threshold': 3,
            'timestamp': time.time(),
            'events': []
        }
        
        config = {
            'url': 'https://webhook.example.com/alerts',
            'headers': {'Authorization': 'Bearer token123'}
        }
        
        self.alert_manager._send_webhook_alert(alert_data, config)
        
        # Verifica se webhook foi chamado
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        
        self.assertEqual(call_args[1]['url'], config['url'])
        self.assertIn('Authorization', call_args[1]['headers'])
        self.assertEqual(call_args[1]['json']['alert_type'], 'havoc_system_alert')
    
    @patch('requests.post')
    def test_send_slack_alert(self, mock_post):
        """Testa envio de alerta para Slack"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response
        
        alert_data = {
            'level': AlertLevel.CRITICAL,
            'description': 'Critical alert',
            'rule_name': 'critical_rule',
            'event_count': 1,
            'threshold': 1,
            'window': 60,
            'timestamp': time.time(),
            'events': []
        }
        
        config = {
            'webhook_url': 'https://hooks.slack.com/services/xxx',
            'channel': '#alerts'
        }
        
        self.alert_manager._send_slack_alert(alert_data, config)
        
        # Verifica se Slack webhook foi chamado
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        
        self.assertEqual(call_args[0][0], config['webhook_url'])
        payload = call_args[1]['json']
        self.assertEqual(payload['channel'], '#alerts')
        self.assertIn('🚨', payload['text'])


class AlertHandlerTest(TestCase):
    """Testes para AlertHandler"""
    
    def setUp(self):
        """Configuração inicial para os testes"""
        self.handler = AlertHandler()
        cache.clear()
    
    @patch('apps.core.alerts.alert_system.AlertManager.process_log_entry')
    def test_emit_warning_level(self, mock_process):
        """Testa que logs de WARNING são processados"""
        logger = logging.getLogger('test')
        record = logger.makeRecord(
            name='test',
            level=logging.WARNING,
            fn='test.py',
            lno=10,
            msg='Test warning',
            args=(),
            exc_info=None
        )
        
        self.handler.emit(record)
        
        mock_process.assert_called_once_with(record)
    
    @patch('apps.core.alerts.alert_system.AlertManager.process_log_entry')
    def test_emit_info_level_ignored(self, mock_process):
        """Testa que logs de INFO são ignorados"""
        logger = logging.getLogger('test')
        record = logger.makeRecord(
            name='test',
            level=logging.INFO,
            fn='test.py',
            lno=10,
            msg='Test info',
            args=(),
            exc_info=None
        )
        
        self.handler.emit(record)
        
        mock_process.assert_not_called()
    
    @patch('apps.core.alerts.alert_system.AlertManager.process_log_entry')
    def test_emit_exception_handling(self, mock_process):
        """Testa que exceções no handler não quebram o logging"""
        mock_process.side_effect = Exception("Test exception")
        
        logger = logging.getLogger('test')
        record = logger.makeRecord(
            name='test',
            level=logging.ERROR,
            fn='test.py',
            lno=10,
            msg='Test error',
            args=(),
            exc_info=None
        )
        
        # Não deve levantar exceção
        try:
            self.handler.emit(record)
        except Exception:
            self.fail("AlertHandler.emit() levantou exceção inesperada")


@override_settings(
    ALERTS_EMAIL_ENABLED=True,
    ALERTS_EMAIL_RECIPIENTS=['<EMAIL>'],
    ALERTS_WEBHOOK_ENABLED=False,
    ALERTS_SLACK_ENABLED=False
)
class AlertIntegrationTest(TestCase):
    """Testes de integração do sistema de alertas"""
    
    def setUp(self):
        """Configuração inicial para os testes"""
        cache.clear()
    
    @patch('apps.core.alerts.alert_system.AlertManager._send_notifications')
    def test_full_alert_flow(self, mock_send):
        """Testa fluxo completo de alerta"""
        logger = logging.getLogger('havoc.integration_test')
        
        # Gera múltiplos erros para disparar alerta
        for i in range(12):  # Mais que o threshold de 10
            logger.error(
                f"Erro de integração #{i+1}",
                extra={
                    'request_id': f'int_test_{i+1}',
                    'user_id': 1
                }
            )
        
        # Verifica se alerta foi disparado
        mock_send.assert_called()
    
    def test_alert_cooldown(self):
        """Testa que cooldown previne spam de alertas"""
        # Define cooldown manualmente
        cache.set("alert_cooldown_error_rate", True, 900)
        
        with patch('apps.core.alerts.alert_system.AlertManager._send_notifications') as mock_send:
            logger = logging.getLogger('havoc.cooldown_test')
            
            # Gera erros suficientes para disparar alerta
            for i in range(15):
                logger.error(f"Erro de cooldown #{i+1}")
            
            # Alerta não deve ser enviado devido ao cooldown
            mock_send.assert_not_called()
