from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.http import HttpResponse
from django.contrib.sessions.middleware import SessionMiddleware
from django.contrib.auth.middleware import AuthenticationMiddleware
from apps.accounts.middleware import (
    AccessControlMiddleware,
    LoginRedirectMiddleware,
    SecurityHeadersMiddleware
)
from unittest.mock import Mock

User = get_user_model()


class AccessControlMiddlewareTest(TestCase):
    """Testes para AccessControlMiddleware"""
    
    def setUp(self):
        """Configuração inicial para os testes"""
        self.factory = RequestFactory()
        self.middleware = AccessControlMiddleware(get_response=Mock())
        
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123',
            is_verified=True
        )
        
        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            username='staffuser',
            password='testpass123',
            is_verified=True,
            is_staff=True
        )
    
    def _add_session_to_request(self, request):
        """Adiciona sessão ao request"""
        middleware = SessionMiddleware(get_response=Mock())
        middleware.process_request(request)
        request.session.save()
    
    def _add_user_to_request(self, request, user=None):
        """Adiciona usuário ao request"""
        self._add_session_to_request(request)
        request.user = user or Mock()
        request.user.is_authenticated = user is not None
    
    def test_get_area_info_config(self):
        """Testa informações da área de configuração"""
        area_info = self.middleware.get_area_info('/config/dashboard/')
        
        self.assertEqual(area_info['type'], 'config')
        self.assertIn('Configurações', area_info['name'])
    
    def test_get_area_info_admin(self):
        """Testa informações da área admin"""
        area_info = self.middleware.get_area_info('/admin/users/')
        
        self.assertEqual(area_info['type'], 'admin')
        self.assertIn('Admin', area_info['name'])
    
    def test_get_area_info_profile(self):
        """Testa informações da área de perfil"""
        area_info = self.middleware.get_area_info('/accounts/perfil/')
        
        self.assertEqual(area_info['type'], 'profile')
        self.assertIn('Perfil', area_info['name'])
    
    def test_get_area_info_general(self):
        """Testa informações de área geral"""
        area_info = self.middleware.get_area_info('/some/other/path/')
        
        self.assertEqual(area_info['type'], 'general')
    
    def test_check_config_access_staff_user(self):
        """Testa acesso à configuração com usuário staff"""
        request = self.factory.get('/config/dashboard/')
        self._add_user_to_request(request, self.staff_user)
        
        has_access, _ = self.middleware.check_config_access(request)
        self.assertTrue(has_access)
    
    def test_check_config_access_regular_user(self):
        """Testa acesso à configuração com usuário regular"""
        request = self.factory.get('/config/dashboard/')
        self._add_user_to_request(request, self.user)
        
        has_access, message = self.middleware.check_config_access(request)
        self.assertFalse(has_access)
        self.assertIn('administrador', message)
    
    def test_check_config_access_anonymous_user(self):
        """Testa acesso à configuração com usuário anônimo"""
        request = self.factory.get('/config/dashboard/')
        self._add_user_to_request(request)
        request.user.is_authenticated = False
        
        has_access, message = self.middleware.check_config_access(request)
        self.assertFalse(has_access)
        self.assertIn('fazer login', message)
    
    def test_check_profile_access_owner(self):
        """Testa acesso ao perfil pelo próprio usuário"""
        request = self.factory.get(f'/accounts/perfil/{self.user.slug}/')
        self._add_user_to_request(request, self.user)
        
        has_access, _ = self.middleware.check_profile_access(request, self.user.slug)
        self.assertTrue(has_access)
    
    def test_check_profile_access_other_user(self):
        """Testa acesso ao perfil de outro usuário"""
        other_user = User.objects.create_user(
            email='<EMAIL>',
            username='otheruser',
            password='testpass123',
            is_verified=True
        )
        
        request = self.factory.get(f'/accounts/perfil/{other_user.slug}/')
        self._add_user_to_request(request, self.user)
        
        has_access, message = self.middleware.check_profile_access(request, other_user.slug)
        self.assertFalse(has_access)
        self.assertIn('próprio perfil', message)
    
    def test_check_profile_access_staff_user(self):
        """Testa acesso ao perfil por usuário staff"""
        request = self.factory.get(f'/accounts/perfil/{self.user.slug}/')
        self._add_user_to_request(request, self.staff_user)
        
        has_access, _ = self.middleware.check_profile_access(request, self.user.slug)
        self.assertTrue(has_access)


class LoginRedirectMiddlewareTest(TestCase):
    """Testes para LoginRedirectMiddleware"""
    
    def setUp(self):
        """Configuração inicial para os testes"""
        self.factory = RequestFactory()
        self.middleware = LoginRedirectMiddleware(get_response=Mock())
        
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123',
            is_verified=True
        )
    
    def _add_session_to_request(self, request):
        """Adiciona sessão ao request"""
        middleware = SessionMiddleware(get_response=Mock())
        middleware.process_request(request)
        request.session.save()
    
    def test_get_area_name(self):
        """Testa obtenção do nome da área"""
        area_name = self.middleware.get_area_name('/config/dashboard/')
        self.assertIn('Configurações', area_name)
    
    def test_save_login_context(self):
        """Testa salvamento do contexto de login"""
        request = self.factory.get('/config/dashboard/')
        self._add_session_to_request(request)
        
        self.middleware.save_login_context(request)
        
        self.assertIn('login_context', request.session)
        context = request.session['login_context']
        self.assertEqual(context['attempted_path'], '/config/dashboard/')
        self.assertIn('attempted_area', context)
        self.assertIn('timestamp', context)


class SecurityHeadersMiddlewareTest(TestCase):
    """Testes para SecurityHeadersMiddleware"""
    
    def setUp(self):
        """Configuração inicial para os testes"""
        self.factory = RequestFactory()
        
        def get_response(request):
            return HttpResponse("Test response")
        
        self.middleware = SecurityHeadersMiddleware(get_response)
    
    def test_security_headers_added(self):
        """Testa se headers de segurança são adicionados"""
        request = self.factory.get('/')
        response = self.middleware(request)
        
        # Verifica headers de segurança
        self.assertIn('X-Content-Type-Options', response)
        self.assertEqual(response['X-Content-Type-Options'], 'nosniff')
        
        self.assertIn('X-Frame-Options', response)
        self.assertEqual(response['X-Frame-Options'], 'DENY')
        
        self.assertIn('X-XSS-Protection', response)
        self.assertEqual(response['X-XSS-Protection'], '1; mode=block')
        
        self.assertIn('Referrer-Policy', response)
        self.assertEqual(response['Referrer-Policy'], 'strict-origin-when-cross-origin')
    
    def test_csp_header_added(self):
        """Testa se header CSP é adicionado"""
        request = self.factory.get('/')
        response = self.middleware(request)
        
        self.assertIn('Content-Security-Policy', response)
        csp = response['Content-Security-Policy']
        
        # Verifica algumas diretivas importantes
        self.assertIn("default-src 'self'", csp)
        self.assertIn("script-src", csp)
        self.assertIn("style-src", csp)
        self.assertIn("img-src", csp)
    
    def test_hsts_header_https(self):
        """Testa header HSTS em conexão HTTPS"""
        request = self.factory.get('/', secure=True)
        response = self.middleware(request)
        
        self.assertIn('Strict-Transport-Security', response)
        hsts = response['Strict-Transport-Security']
        self.assertIn('max-age=', hsts)
        self.assertIn('includeSubDomains', hsts)
    
    def test_no_hsts_header_http(self):
        """Testa que header HSTS não é adicionado em HTTP"""
        request = self.factory.get('/')
        response = self.middleware(request)
        
        self.assertNotIn('Strict-Transport-Security', response)
    
    def test_permissions_policy_header(self):
        """Testa header Permissions Policy"""
        request = self.factory.get('/')
        response = self.middleware(request)
        
        self.assertIn('Permissions-Policy', response)
        permissions = response['Permissions-Policy']
        
        # Verifica algumas políticas importantes
        self.assertIn('geolocation=()', permissions)
        self.assertIn('microphone=()', permissions)
        self.assertIn('camera=()', permissions)


class MiddlewareIntegrationTest(TestCase):
    """Testes de integração dos middlewares"""

    def setUp(self):
        """Configuração inicial para os testes"""
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123',
            is_verified=True
        )

        self.staff_user = User.objects.create_user(
            email='<EMAIL>',
            username='staffuser',
            password='testpass123',
            is_verified=True,
            is_staff=True
        )

    def _process_request_through_middlewares(self, request):
        """Processa request através de todos os middlewares"""
        from django.contrib.sessions.middleware import SessionMiddleware
        from django.contrib.auth.middleware import AuthenticationMiddleware

        # Adiciona sessão
        session_middleware = SessionMiddleware(get_response=Mock())
        session_middleware.process_request(request)
        request.session.save()

        # Adiciona autenticação
        auth_middleware = AuthenticationMiddleware(get_response=Mock())
        auth_middleware.process_request(request)

        return request

    def test_access_control_flow_authenticated_staff(self):
        """Testa fluxo completo para usuário staff autenticado"""
        request = self.factory.get('/config/dashboard/')
        request.user = self.staff_user
        request = self._process_request_through_middlewares(request)

        access_middleware = AccessControlMiddleware(get_response=Mock())

        # Deve permitir acesso
        has_access, _ = access_middleware.check_config_access(request)
        self.assertTrue(has_access)

    def test_access_control_flow_regular_user(self):
        """Testa fluxo completo para usuário regular"""
        request = self.factory.get('/config/dashboard/')
        request.user = self.user
        request = self._process_request_through_middlewares(request)

        access_middleware = AccessControlMiddleware(get_response=Mock())

        # Não deve permitir acesso
        has_access, message = access_middleware.check_config_access(request)
        self.assertFalse(has_access)
        self.assertIn('administrador', message)

    def test_login_redirect_saves_context(self):
        """Testa se contexto de login é salvo corretamente"""
        request = self.factory.get('/config/dashboard/')
        request.user = Mock()
        request.user.is_authenticated = False
        request = self._process_request_through_middlewares(request)

        login_middleware = LoginRedirectMiddleware(get_response=Mock())
        login_middleware.save_login_context(request)

        # Verifica se contexto foi salvo
        self.assertIn('login_context', request.session)
        context = request.session['login_context']
        self.assertEqual(context['attempted_path'], '/config/dashboard/')
        self.assertIn('Configurações', context['attempted_area'])

    def test_security_headers_applied(self):
        """Testa se headers de segurança são aplicados"""
        request = self.factory.get('/')

        def get_response(req):
            return HttpResponse("Test")

        middleware = SecurityHeadersMiddleware(get_response)
        response = middleware(request)

        # Verifica headers críticos
        required_headers = [
            'X-Content-Type-Options',
            'X-Frame-Options',
            'X-XSS-Protection',
            'Referrer-Policy',
            'Content-Security-Policy',
            'Permissions-Policy'
        ]

        for header in required_headers:
            self.assertIn(header, response)

    def test_middleware_chain_order(self):
        """Testa ordem correta dos middlewares"""
        request = self.factory.get('/config/dashboard/')
        request.user = self.user
        request = self._process_request_through_middlewares(request)

        # Simula processamento em ordem
        def get_response(req):
            return HttpResponse("Test")

        # 1. Security headers primeiro
        security_middleware = SecurityHeadersMiddleware(get_response)
        response = security_middleware(request)

        # 2. Access control
        access_middleware = AccessControlMiddleware(get_response)
        has_access, _ = access_middleware.check_config_access(request)

        # 3. Login redirect se necessário
        if not has_access:
            login_middleware = LoginRedirectMiddleware(get_response)
            login_middleware.save_login_context(request)

        # Verifica que tudo funcionou
        self.assertIn('X-Content-Type-Options', response)
        self.assertFalse(has_access)
        self.assertIn('login_context', request.session)


class MiddlewareErrorHandlingTest(TestCase):
    """Testes de tratamento de erros nos middlewares"""

    def setUp(self):
        """Configuração inicial para os testes"""
        self.factory = RequestFactory()

    def test_access_control_handles_missing_user(self):
        """Testa tratamento quando request não tem usuário"""
        request = self.factory.get('/config/dashboard/')
        # Não adiciona usuário ao request

        middleware = AccessControlMiddleware(get_response=Mock())

        # Deve tratar graciosamente
        try:
            has_access, message = middleware.check_config_access(request)
            self.assertFalse(has_access)
        except AttributeError:
            self.fail("Middleware deve tratar request sem usuário")

    def test_login_redirect_handles_missing_session(self):
        """Testa tratamento quando request não tem sessão"""
        request = self.factory.get('/config/dashboard/')
        # Não adiciona sessão ao request

        middleware = LoginRedirectMiddleware(get_response=Mock())

        # Deve tratar graciosamente
        try:
            middleware.save_login_context(request)
        except AttributeError:
            self.fail("Middleware deve tratar request sem sessão")

    def test_security_headers_handles_exceptions(self):
        """Testa que headers de segurança são aplicados mesmo com exceções"""
        request = self.factory.get('/')

        def get_response_with_error(req):
            raise Exception("Simulated error")

        middleware = SecurityHeadersMiddleware(get_response_with_error)

        # Deve propagar exceção mas ainda aplicar headers se possível
        with self.assertRaises(Exception):
            middleware(request)
