

instale o python na maquina



primeiro atualizar o pip pra versão mais atualizar

    -- python.exe -m pip install --upgrade pip

     Instalar os seguintes pacotes Iniciais.

            pip install django
            pip install pillow
            Para criar o arquivo requirements.txt

            pip freeze > requirements.txt


Ambiente Virtual

        Criando ambiente virtual

            -- python -m venv meu_ambiente_virtual

        ativando ambiente virtual

        No Windows:

            -- meu_ambiente_virtual\Scripts\activate
            
        No macOS e Linux:

            -- source meu_ambiente_virtual/bin/activate

        No Microsoft Windows, pode ser necessário ativar o script Activate.ps1, definindo a política de execução para o usuário. Você pode fazer isso executando o seguinte comando do PowerShell:

            -- PS C:> Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

        Como Desativar um Ambiente Virtual

            -- deactivate

        Como Instalar Pacotes em um Ambiente Virtual

            -- pip install nome_do_pacote

        instalando requerimentos quando existirem

            -- pip install requests

        Rodando o ambiente Virtual para começar o projeto 

            -- python -m venv meu_projeto  

        
            ## Windows
            python -m venv .venv
            source .venv/Scripts/activate # Ativar ambiente

            ## Linux 
            ## Caso não tenha virtualenv. "pip install virtualenv"
            virtualenv .venv
            source .venv/bin/activate # Ativar ambiente
        

Django

    instalando o django no Ambiente Virtual 

        -- pip install Django

    Criando o projeto com Django

        -- django-admin startproject core .

    Rodando o servidor

        -- python manage.py runserver

    comando pra gerar secret ket

        -- python -c "import string as s;from secrets import SystemRandom as SR;print(''.join(SR().choices(s.ascii_letters + s.digits + s.punctuation, k=64)));"

    criando super user

        -- python manage.py createsuperuser

        -- python manage.py migrate

        -- python manage.py makemigrations

        -- python manage.py startapp blog

Intalando variaveis de ambiente

    -- pip install python-dotenv

    Criar arquivo na raiz chamado .env (sem extenção)

        dentro dele colocar e preencher as variaveis de ambiente segue abaixo um modelo

            Exemplo:

            ## Não precisa colocar "" aspas
            SECRET_KEY=django-insecure-q(ge$586x7o9n)3w+6d_^t(m!ib&9%_m8&6@=m=sy@^7qf)#*_
            DEBUG=True
            SUPER_USER=ADMIN
            EMAIL=<EMAIL>

            NAME_DB=db.sqlite3
            USER_DB=root
            PASSWORD_DB=
            HOST_DB=localhost
            PORT_DB=3306

            EMAIL_HOST=smtp.office365.com
            EMAIL_HOST_USER=<EMAIL>
            EMAIL_HOST_PASSWORD=sua_senha
            EMAIL_PORT=587 
            EMAIL_USE_TLS=True 
            DEFAULT_FROM_EMAIL=<EMAIL>
            SERVER_EMAIL=DEFAULT_FROM_EMAIL


            Configuração no core/settings.py

            Nota que para chamar uma variavel no arquivo .env basta chamar a biblioteca os.getenv('NAME_DB') e NAME_DB é nome da variavel que está no arquivo.

            # importar a biblioteca
            import os
            import sys
            from dotenv import load_dotenv

            # Adicionar essa tag para que nosso projeto encontre o .env
            load_dotenv(os.path.join(BASE_DIR, ".env"))

            # Diz para Django onde estão nossos aplicativos
            APPS_DIR = str(os.path.join(BASE_DIR,'apps'))
            sys.path.insert(0, APPS_DIR)

            # Chamar as variaveis assim
            SECRET_KEY = os.getenv("SECRET_KEY")

            # DEBUG
            DEBUG = os.getenv('DEBUG')

            # Aplicativos do django
            DJANGO_APPS = [
                'django.contrib.admin',
                'django.contrib.auth',
                'django.contrib.contenttypes',
                'django.contrib.sessions',
                'django.contrib.messages',
                'django.contrib.staticfiles',
            ]
            THIRD_APPS = [
                ...
            ]
            PROJECT_APPS = [
                'apps.base',
                'apps.myapp',
            ]
            INSTALLED_APPS = DJANGO_APPS + THIRD_APPS + PROJECT_APPS

            # Banco de Dados.
            DATABASES = {
            'default': {
                'ENGINE': 'django.db.backends.sqlite3',
                'NAME': os.path.join(BASE_DIR, os.getenv('NAME_DB')),
                        #'USER':os.getenv('USER_DB')
                        #'PASSWORD': os.getenv('PASSWORD_DB')
                        #'HOST':os.getenv('HOST_DB')
                        #'PORT':os.getenv('PORT_DB')

                }
            }

            # Se tiver configuração de email
            EMAIL_HOST = os.getenv('EMAIL_HOST')
            EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER')
            EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD') 
            EMAIL_PORT = os.getenv('EMAIL_PORT') 
            EMAIL_USE_TLS = os.getenv('EMAIL_USE_TLS') 
            DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL')
            SERVER_EMAIL = DEFAULT_FROM_EMAIL
            GIT

                    Comandos basicos GIT


                        Setar usuário
                            git config --global user.name "Yurii Menezes"

                        Setar email
                            git config --global user.email <EMAIL>

                        Criar novo repositório
                            git init

                        Verificar estado dos arquivos/diretórios
                            git status

                        Adicionar todos os arquivos/diretórios
                            git add .

                        Comitar informando mensagem
                            git commit meuarquivo.txt -m "minha mensagem de commit"

                        git push <nome do repositório> <nome da branch>
                        git push origin master
                        git push --all

Criando o projeto


            
        django-admin startproject core .

        “core” é nome do seu projeto e quando colocamos um “.” depois do nome do projeto significa que é para criar os arquivos na raiz da pasta. Assim não cria subpasta do projeto.

        Testar a aplicação

        python manage.py runserver  

        crindo apps no projeto
        -- python manage.py startapp base

