{% extends 'config/base_config.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="h3 mb-1">
                <i class="fas fa-envelope-open-text text-primary me-2"></i>
                {{ title }}
            </h2>
            <p class="text-muted mb-0">Configure servidor SMTP para envio de emails</p>
        </div>
        <div>
            <a href="{% url 'config:email_configs' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Voltar
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- <PERSON>ul<PERSON><PERSON> Principal -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>Configuração SMTP
                    </h5>
                </div>
                <div class="card-body">
                    {% crispy form %}
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Ajuda e Dicas -->
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Ajuda
                    </h6>
                </div>
                <div class="card-body">
                    <div class="accordion" id="helpAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" 
                                        data-bs-toggle="collapse" data-bs-target="#gmailHelp">
                                    <i class="fab fa-google me-2"></i>Gmail
                                </button>
                            </h2>
                            <div id="gmailHelp" class="accordion-collapse collapse" 
                                 data-bs-parent="#helpAccordion">
                                <div class="accordion-body">
                                    <p><strong>Configurações:</strong></p>
                                    <ul>
                                        <li>Servidor: smtp.gmail.com</li>
                                        <li>Porta: 587</li>
                                        <li>TLS: Ativado</li>
                                    </ul>
                                    <p><strong>Senha de App:</strong></p>
                                    <ol>
                                        <li>Ative verificação em 2 etapas</li>
                                        <li>Gere uma senha de app</li>
                                        <li>Use a senha gerada no campo senha</li>
                                    </ol>
                                    <a href="https://support.google.com/accounts/answer/185833" 
                                       target="_blank" class="btn btn-sm btn-outline-primary">
                                        Saiba mais
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" 
                                        data-bs-toggle="collapse" data-bs-target="#outlookHelp">
                                    <i class="fab fa-microsoft me-2"></i>Outlook
                                </button>
                            </h2>
                            <div id="outlookHelp" class="accordion-collapse collapse" 
                                 data-bs-parent="#helpAccordion">
                                <div class="accordion-body">
                                    <p><strong>Outlook.com/Hotmail:</strong></p>
                                    <ul>
                                        <li>Servidor: smtp-mail.outlook.com</li>
                                        <li>Porta: 587</li>
                                        <li>TLS: Ativado</li>
                                        <li>Senha: Sua senha normal</li>
                                    </ul>
                                    <p><strong>Office 365:</strong></p>
                                    <ul>
                                        <li>Servidor: smtp.office365.com</li>
                                        <li>Porta: 587</li>
                                        <li>TLS: Ativado</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" 
                                        data-bs-toggle="collapse" data-bs-target="#sendgridHelp">
                                    <i class="fas fa-paper-plane me-2"></i>SendGrid
                                </button>
                            </h2>
                            <div id="sendgridHelp" class="accordion-collapse collapse" 
                                 data-bs-parent="#helpAccordion">
                                <div class="accordion-body">
                                    <p><strong>Configurações:</strong></p>
                                    <ul>
                                        <li>Servidor: smtp.sendgrid.net</li>
                                        <li>Porta: 587</li>
                                        <li>TLS: Ativado</li>
                                        <li>Usuário: apikey</li>
                                        <li>Senha: Sua API Key</li>
                                    </ul>
                                    <p>SendGrid é um serviço profissional de email com alta entregabilidade.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Status da Configuração (se editando) -->
            {% if config %}
            <div class="card mt-3">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>Estatísticas
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h5 class="text-primary">{{ config.emails_sent_count }}</h5>
                            <small class="text-muted">Emails Enviados</small>
                        </div>
                        <div class="col-6">
                            <h5 class="{% if config.last_test_result.success %}text-success{% else %}text-danger{% endif %}">
                                {% if config.last_tested_at %}
                                    {% if config.last_test_result.success %}✓{% else %}✗{% endif %}
                                {% else %}
                                    -
                                {% endif %}
                            </h5>
                            <small class="text-muted">Último Teste</small>
                        </div>
                    </div>
                    
                    {% if config.last_tested_at %}
                    <hr>
                    <p class="mb-0">
                        <strong>Último teste:</strong><br>
                        <small>{{ config.last_tested_at|date:"d/m/Y H:i" }}</small>
                    </p>
                    {% if config.last_test_result.message %}
                    <p class="mb-0">
                        <strong>Resultado:</strong><br>
                        <small>{{ config.last_test_result.message }}</small>
                    </p>
                    {% endif %}
                    {% endif %}
                    
                    <div class="d-grid mt-3">
                        <a href="{% url 'config:email_config_test' config.pk %}" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-vial me-2"></i>Testar Configuração
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function fillGmailConfig() {
    document.getElementById('id_email_backend').value = 'django.core.mail.backends.smtp.EmailBackend';
    document.getElementById('id_email_host').value = 'smtp.gmail.com';
    document.getElementById('id_email_port').value = '587';
    document.getElementById('id_email_use_tls').checked = true;
    document.getElementById('id_email_use_ssl').checked = false;
    
    // Focus no campo de usuário
    document.getElementById('id_email_host_user').focus();
}

function fillOutlookConfig() {
    document.getElementById('id_email_backend').value = 'django.core.mail.backends.smtp.EmailBackend';
    document.getElementById('id_email_host').value = 'smtp-mail.outlook.com';
    document.getElementById('id_email_port').value = '587';
    document.getElementById('id_email_use_tls').checked = true;
    document.getElementById('id_email_use_ssl').checked = false;
    
    document.getElementById('id_email_host_user').focus();
}

function fillSendGridConfig() {
    document.getElementById('id_email_backend').value = 'django.core.mail.backends.smtp.EmailBackend';
    document.getElementById('id_email_host').value = 'smtp.sendgrid.net';
    document.getElementById('id_email_port').value = '587';
    document.getElementById('id_email_host_user').value = 'apikey';
    document.getElementById('id_email_use_tls').checked = true;
    document.getElementById('id_email_use_ssl').checked = false;
    
    document.getElementById('id_email_host_password').focus();
}

// Validação em tempo real
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    // Sincronizar email do usuário com email remetente
    const emailUser = document.getElementById('id_email_host_user');
    const fromEmail = document.getElementById('id_default_from_email');
    
    if (emailUser && fromEmail) {
        emailUser.addEventListener('blur', function() {
            if (this.value && !fromEmail.value) {
                fromEmail.value = this.value;
            }
        });
    }
    
    // Feedback visual no envio
    if (form && submitBtn) {
        form.addEventListener('submit', function() {
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Salvando...';
            submitBtn.disabled = true;
        });
    }
    
    // Validação de TLS/SSL
    const tlsCheckbox = document.getElementById('id_email_use_tls');
    const sslCheckbox = document.getElementById('id_email_use_ssl');
    
    if (tlsCheckbox && sslCheckbox) {
        tlsCheckbox.addEventListener('change', function() {
            if (this.checked) {
                sslCheckbox.checked = false;
            }
        });
        
        sslCheckbox.addEventListener('change', function() {
            if (this.checked) {
                tlsCheckbox.checked = false;
            }
        });
    }
});
</script>
{% endblock %}
