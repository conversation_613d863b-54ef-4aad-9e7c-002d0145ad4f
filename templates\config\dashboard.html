{% extends "base.html" %}
{% load static %}

{% block title %}Dashboard - Configurações - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
.dashboard-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    margin-bottom: 2rem;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    margin-bottom: 1.5rem;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: scale(1.05);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
}

.stat-card.users { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stat-card.articles { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stat-card.categories { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stat-card.configs { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.activity-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.activity-item:hover {
    background-color: #f8f9fa;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.activity-icon.success { background: #d4edda; color: #155724; }
.activity-icon.primary { background: #d1ecf1; color: #0c5460; }
.activity-icon.warning { background: #fff3cd; color: #856404; }
.activity-icon.danger { background: #f8d7da; color: #721c24; }

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-healthy { background-color: #28a745; }
.status-warning { background-color: #ffc107; }
.status-error { background-color: #dc3545; }

.metric-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.metric-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: #495057;
    margin-bottom: 0.5rem;
}

.metric-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.quick-action {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    text-decoration: none;
    color: #495057;
    transition: all 0.2s ease;
}

.quick-action:hover {
    border-color: #007bff;
    color: #007bff;
    text-decoration: none;
    transform: translateY(-2px);
}

.quick-action i {
    font-size: 2rem;
    margin-bottom: 1rem;
    display: block;
}

@media (max-width: 768px) {
    .stat-number {
        font-size: 2rem;
    }
    
    .quick-actions {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .dashboard-card {
        margin-bottom: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h2 mb-1">
                <i class="fas fa-tachometer-alt me-2"></i>Dashboard de Configurações
            </h1>
            <p class="text-muted mb-0">Visão geral do sistema e configurações</p>
        </div>
        <div>
            <span class="badge bg-success">
                <i class="fas fa-circle me-1"></i>Sistema Online
            </span>
        </div>
    </div>

    <!-- Estatísticas Principais -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="stat-card users">
                <div class="stat-number">{{ total_users }}</div>
                <div class="stat-label">
                    <i class="fas fa-users me-1"></i>Total de Usuários
                </div>
                <div class="mt-2">
                    <small>{{ active_users }} ativos • {{ recent_users }} novos (30d)</small>
                </div>
            </div>
        </div>
        
        {% if articles_available %}
        <div class="col-lg-3 col-md-6">
            <div class="stat-card articles">
                <div class="stat-number">{{ total_articles|default:0 }}</div>
                <div class="stat-label">
                    <i class="fas fa-newspaper me-1"></i>Total de Artigos
                </div>
                <div class="mt-2">
                    <small>{{ published_articles|default:0 }} publicados • {{ recent_articles|default:0 }} novos (7d)</small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="stat-card categories">
                <div class="stat-number">{{ total_categories|default:0 }}</div>
                <div class="stat-label">
                    <i class="fas fa-folder me-1"></i>Categorias
                </div>
                <div class="mt-2">
                    <small>{{ active_categories|default:0 }} ativas</small>
                </div>
            </div>
        </div>
        {% endif %}
        
        <div class="col-lg-3 col-md-6">
            <div class="stat-card configs">
                <div class="stat-number">{{ config_count|default:0 }}</div>
                <div class="stat-label">
                    <i class="fas fa-cog me-1"></i>Configurações
                </div>
                <div class="mt-2">
                    <small>{{ total_groups }} grupos de usuários</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Ações Rápidas -->
    <div class="quick-actions">
        <a href="{% url 'config:system_config' %}" class="quick-action">
            <i class="fas fa-cogs"></i>
            <div>Configurações do Sistema</div>
        </a>
        <a href="{% url 'config:user_management' %}" class="quick-action">
            <i class="fas fa-users-cog"></i>
            <div>Gerenciar Usuários</div>
        </a>
        {% if articles_available %}
        <a href="{% url 'articles:create' %}" class="quick-action">
            <i class="fas fa-plus"></i>
            <div>Novo Artigo</div>
        </a>
        {% endif %}
        <a href="{% url 'config:advanced_config' %}" class="quick-action">
            <i class="fas fa-tools"></i>
            <div>Configurações Avançadas</div>
        </a>
    </div>

    <div class="row">
        <!-- Status do Sistema -->
        <div class="col-lg-6">
            <div class="dashboard-card">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">
                        <i class="fas fa-heartbeat me-2"></i>Status do Sistema
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="d-flex align-items-center">
                                <span class="status-indicator status-{{ system_status.database }}"></span>
                                <span>Banco de Dados</span>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="d-flex align-items-center">
                                <span class="status-indicator status-{{ system_status.cache }}"></span>
                                <span>Cache</span>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="d-flex align-items-center">
                                <span class="status-indicator status-{{ system_status.storage }}"></span>
                                <span>Armazenamento</span>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="d-flex align-items-center">
                                <span class="status-indicator status-{{ system_status.email }}"></span>
                                <span>Email</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Métricas de Performance -->
        <div class="col-lg-6">
            <div class="dashboard-card">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>Performance
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="metric-card">
                                <div class="metric-value">{{ performance_metrics.avg_response_time }}</div>
                                <div class="metric-label">Tempo de Resposta</div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="metric-card">
                                <div class="metric-value">{{ performance_metrics.uptime }}</div>
                                <div class="metric-label">Uptime</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="metric-card">
                                <div class="metric-value">{{ performance_metrics.memory_usage }}</div>
                                <div class="metric-label">Uso de Memória</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="metric-card">
                                <div class="metric-value">{{ performance_metrics.disk_usage }}</div>
                                <div class="metric-label">Uso de Disco</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Atividade Recente -->
        <div class="col-lg-8">
            <div class="dashboard-card">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Atividade Recente
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if activity_feed %}
                        {% for activity in activity_feed %}
                        <div class="activity-item">
                            <div class="activity-icon {{ activity.color }}">
                                <i class="{{ activity.icon }}"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-bold">{{ activity.title }}</div>
                                <div class="text-muted small">{{ activity.description }}</div>
                            </div>
                            <div class="text-muted small">
                                {{ activity.timestamp|timesince }} atrás
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Nenhuma atividade recente</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Configurações Rápidas -->
        <div class="col-lg-4">
            <div class="dashboard-card">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">
                        <i class="fas fa-sliders-h me-2"></i>Configurações Rápidas
                    </h5>
                </div>
                <div class="card-body">
                    {% if configs %}
                        {% for config in configs|slice:":5" %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <div class="fw-bold">{{ config.key }}</div>
                                <div class="text-muted small">{{ config.description|truncatewords:5 }}</div>
                            </div>
                            <div>
                                {% if config.value_type == 'boolean' %}
                                    <span class="badge bg-{% if config.value == 'True' %}success{% else %}secondary{% endif %}">
                                        {% if config.value == 'True' %}Ativo{% else %}Inativo{% endif %}
                                    </span>
                                {% else %}
                                    <span class="badge bg-info">{{ config.value|truncatechars:10 }}</span>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                        
                        {% if configs.count > 5 %}
                        <div class="text-center mt-3">
                            <a href="{% url 'config:system_config' %}" class="btn btn-outline-primary btn-sm">
                                Ver Todas ({{ configs.count }})
                            </a>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-cog fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-2">Nenhuma configuração encontrada</p>
                            <a href="{% url 'config:system_config' %}" class="btn btn-primary btn-sm">
                                Configurar Sistema
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animação de entrada para os cards
    const cards = document.querySelectorAll('.dashboard-card, .stat-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Auto-refresh da página a cada 5 minutos
    setTimeout(() => {
        location.reload();
    }, 300000); // 5 minutos

    // Tooltip para indicadores de status
    const statusIndicators = document.querySelectorAll('.status-indicator');
    statusIndicators.forEach(indicator => {
        const status = indicator.classList.contains('status-healthy') ? 'Saudável' :
                      indicator.classList.contains('status-warning') ? 'Atenção' : 'Erro';
        indicator.title = status;
    });
});
</script>
{% endblock %}
