<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for apps\articles\migrations\0001_initial.py: 100%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>apps\articles\migrations\0001_initial.py</b>:
            <span class="pc_cov">100%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">8 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">8<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">0<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">0<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_10e6b26d1150d373_services_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_6ac3d5cc3c7d2853___init___py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 21:05 -0300
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="com"># Generated by Django 5.2.2 on 2025-06-06 15:06</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="key">import</span> <span class="nam">django</span><span class="op">.</span><span class="nam">core</span><span class="op">.</span><span class="nam">validators</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="key">import</span> <span class="nam">django</span><span class="op">.</span><span class="nam">db</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">deletion</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="key">from</span> <span class="nam">django</span><span class="op">.</span><span class="nam">conf</span> <span class="key">import</span> <span class="nam">settings</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="key">from</span> <span class="nam">django</span><span class="op">.</span><span class="nam">db</span> <span class="key">import</span> <span class="nam">migrations</span><span class="op">,</span> <span class="nam">models</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">class</span> <span class="nam">Migration</span><span class="op">(</span><span class="nam">migrations</span><span class="op">.</span><span class="nam">Migration</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t">    <span class="nam">initial</span> <span class="op">=</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t">    <span class="nam">dependencies</span> <span class="op">=</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">swappable_dependency</span><span class="op">(</span><span class="nam">settings</span><span class="op">.</span><span class="nam">AUTH_USER_MODEL</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t">    <span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">    <span class="nam">operations</span> <span class="op">=</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">CreateModel</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t">            <span class="nam">name</span><span class="op">=</span><span class="str">'Category'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">            <span class="nam">fields</span><span class="op">=</span><span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">                <span class="op">(</span><span class="str">'id'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BigAutoField</span><span class="op">(</span><span class="nam">auto_created</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">primary_key</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">serialize</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'ID'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">                <span class="op">(</span><span class="str">'name'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">help_text</span><span class="op">=</span><span class="str">'Nome da categoria'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">100</span><span class="op">,</span> <span class="nam">unique</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">validators</span><span class="op">=</span><span class="op">[</span><span class="nam">django</span><span class="op">.</span><span class="nam">core</span><span class="op">.</span><span class="nam">validators</span><span class="op">.</span><span class="nam">MinLengthValidator</span><span class="op">(</span><span class="num">2</span><span class="op">)</span><span class="op">]</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'nome'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">                <span class="op">(</span><span class="str">'slug'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">SlugField</span><span class="op">(</span><span class="nam">help_text</span><span class="op">=</span><span class="str">'URL amig&#225;vel da categoria'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">100</span><span class="op">,</span> <span class="nam">unique</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'slug'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">                <span class="op">(</span><span class="str">'description'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">TextField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Descri&#231;&#227;o da categoria'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'descri&#231;&#227;o'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">                <span class="op">(</span><span class="str">'color'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">'#007bff'</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Cor da categoria em hexadecimal (ex: #007bff)'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">7</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'cor'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">                <span class="op">(</span><span class="str">'icon'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Classe do &#237;cone (ex: fas fa-newspaper)'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">50</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'&#237;cone'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">                <span class="op">(</span><span class="str">'is_active'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BooleanField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Se a categoria est&#225; ativa'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'ativo'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">                <span class="op">(</span><span class="str">'order'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">PositiveIntegerField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Ordem de exibi&#231;&#227;o da categoria'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'ordem'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">                <span class="op">(</span><span class="str">'meta_title'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'T&#237;tulo para SEO (m&#225;ximo 60 caracteres)'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">60</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'meta t&#237;tulo'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">                <span class="op">(</span><span class="str">'meta_description'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Descri&#231;&#227;o para SEO (m&#225;ximo 160 caracteres)'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">160</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'meta descri&#231;&#227;o'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">                <span class="op">(</span><span class="str">'created_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">auto_now_add</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'criado em'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">                <span class="op">(</span><span class="str">'updated_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">auto_now</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'atualizado em'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">                <span class="op">(</span><span class="str">'parent'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">ForeignKey</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Categoria pai para criar hierarquia'</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">on_delete</span><span class="op">=</span><span class="nam">django</span><span class="op">.</span><span class="nam">db</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">deletion</span><span class="op">.</span><span class="nam">CASCADE</span><span class="op">,</span> <span class="nam">to</span><span class="op">=</span><span class="str">'articles.category'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'categoria pai'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">            <span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">            <span class="nam">options</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">                <span class="str">'verbose_name'</span><span class="op">:</span> <span class="str">'categoria'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">                <span class="str">'verbose_name_plural'</span><span class="op">:</span> <span class="str">'categorias'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">                <span class="str">'ordering'</span><span class="op">:</span> <span class="op">[</span><span class="str">'order'</span><span class="op">,</span> <span class="str">'name'</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">            <span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">CreateModel</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">            <span class="nam">name</span><span class="op">=</span><span class="str">'Article'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">            <span class="nam">fields</span><span class="op">=</span><span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">                <span class="op">(</span><span class="str">'id'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BigAutoField</span><span class="op">(</span><span class="nam">auto_created</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">primary_key</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">serialize</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'ID'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">                <span class="op">(</span><span class="str">'title'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">help_text</span><span class="op">=</span><span class="str">'T&#237;tulo do artigo'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">200</span><span class="op">,</span> <span class="nam">validators</span><span class="op">=</span><span class="op">[</span><span class="nam">django</span><span class="op">.</span><span class="nam">core</span><span class="op">.</span><span class="nam">validators</span><span class="op">.</span><span class="nam">MinLengthValidator</span><span class="op">(</span><span class="num">5</span><span class="op">)</span><span class="op">]</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'t&#237;tulo'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">                <span class="op">(</span><span class="str">'slug'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">SlugField</span><span class="op">(</span><span class="nam">help_text</span><span class="op">=</span><span class="str">'URL amig&#225;vel do artigo'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">200</span><span class="op">,</span> <span class="nam">unique</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'slug'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">                <span class="op">(</span><span class="str">'excerpt'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">TextField</span><span class="op">(</span><span class="nam">help_text</span><span class="op">=</span><span class="str">'Resumo do artigo para listagens e SEO'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">500</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'resumo'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">                <span class="op">(</span><span class="str">'content'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">TextField</span><span class="op">(</span><span class="nam">help_text</span><span class="op">=</span><span class="str">'Conte&#250;do completo do artigo'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'conte&#250;do'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">                <span class="op">(</span><span class="str">'featured_image'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">ImageField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Imagem principal do artigo'</span><span class="op">,</span> <span class="nam">upload_to</span><span class="op">=</span><span class="str">'articles/images/'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'imagem destacada'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">                <span class="op">(</span><span class="str">'featured_image_alt'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Texto alternativo para acessibilidade'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">200</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'texto alternativo da imagem'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">                <span class="op">(</span><span class="str">'status'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">choices</span><span class="op">=</span><span class="op">[</span><span class="op">(</span><span class="str">'draft'</span><span class="op">,</span> <span class="str">'Rascunho'</span><span class="op">)</span><span class="op">,</span> <span class="op">(</span><span class="str">'published'</span><span class="op">,</span> <span class="str">'Publicado'</span><span class="op">)</span><span class="op">,</span> <span class="op">(</span><span class="str">'archived'</span><span class="op">,</span> <span class="str">'Arquivado'</span><span class="op">)</span><span class="op">,</span> <span class="op">(</span><span class="str">'scheduled'</span><span class="op">,</span> <span class="str">'Agendado'</span><span class="op">)</span><span class="op">]</span><span class="op">,</span> <span class="nam">default</span><span class="op">=</span><span class="str">'draft'</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Status de publica&#231;&#227;o do artigo'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">20</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'status'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">                <span class="op">(</span><span class="str">'is_featured'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BooleanField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Se o artigo deve aparecer em destaque'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'artigo em destaque'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">                <span class="op">(</span><span class="str">'allow_comments'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BooleanField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Se o artigo permite coment&#225;rios'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'permitir coment&#225;rios'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">                <span class="op">(</span><span class="str">'meta_title'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'T&#237;tulo para SEO (m&#225;ximo 60 caracteres)'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">60</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'meta t&#237;tulo'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">                <span class="op">(</span><span class="str">'meta_description'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Descri&#231;&#227;o para SEO (m&#225;ximo 160 caracteres)'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">160</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'meta descri&#231;&#227;o'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">                <span class="op">(</span><span class="str">'meta_keywords'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Palavras-chave separadas por v&#237;rgula'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">255</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'palavras-chave'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">                <span class="op">(</span><span class="str">'created_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">auto_now_add</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'criado em'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">                <span class="op">(</span><span class="str">'updated_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">auto_now</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'atualizado em'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">                <span class="op">(</span><span class="str">'published_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Data e hora de publica&#231;&#227;o'</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'publicado em'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">                <span class="op">(</span><span class="str">'scheduled_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Data e hora para publica&#231;&#227;o autom&#225;tica'</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'agendado para'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">                <span class="op">(</span><span class="str">'view_count'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">PositiveIntegerField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'N&#250;mero de visualiza&#231;&#245;es do artigo'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'visualiza&#231;&#245;es'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">                <span class="op">(</span><span class="str">'reading_time'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">PositiveIntegerField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="num">0</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Tempo estimado de leitura em minutos'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'tempo de leitura'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">                <span class="op">(</span><span class="str">'author'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">ForeignKey</span><span class="op">(</span><span class="nam">help_text</span><span class="op">=</span><span class="str">'Autor principal do artigo'</span><span class="op">,</span> <span class="nam">on_delete</span><span class="op">=</span><span class="nam">django</span><span class="op">.</span><span class="nam">db</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">deletion</span><span class="op">.</span><span class="nam">CASCADE</span><span class="op">,</span> <span class="nam">related_name</span><span class="op">=</span><span class="str">'authored_articles'</span><span class="op">,</span> <span class="nam">to</span><span class="op">=</span><span class="nam">settings</span><span class="op">.</span><span class="nam">AUTH_USER_MODEL</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'autor'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">                <span class="op">(</span><span class="str">'contributors'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">ManyToManyField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Outros colaboradores do artigo'</span><span class="op">,</span> <span class="nam">related_name</span><span class="op">=</span><span class="str">'contributed_articles'</span><span class="op">,</span> <span class="nam">to</span><span class="op">=</span><span class="nam">settings</span><span class="op">.</span><span class="nam">AUTH_USER_MODEL</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'colaboradores'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">                <span class="op">(</span><span class="str">'category'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">ForeignKey</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Categoria principal do artigo'</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">on_delete</span><span class="op">=</span><span class="nam">django</span><span class="op">.</span><span class="nam">db</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">deletion</span><span class="op">.</span><span class="nam">SET_NULL</span><span class="op">,</span> <span class="nam">related_name</span><span class="op">=</span><span class="str">'articles'</span><span class="op">,</span> <span class="nam">to</span><span class="op">=</span><span class="str">'articles.category'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'categoria'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">            <span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">            <span class="nam">options</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">                <span class="str">'verbose_name'</span><span class="op">:</span> <span class="str">'artigo'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">                <span class="str">'verbose_name_plural'</span><span class="op">:</span> <span class="str">'artigos'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">                <span class="str">'ordering'</span><span class="op">:</span> <span class="op">[</span><span class="str">'-published_at'</span><span class="op">,</span> <span class="str">'-created_at'</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">            <span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">CreateModel</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">            <span class="nam">name</span><span class="op">=</span><span class="str">'Comment'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">            <span class="nam">fields</span><span class="op">=</span><span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">                <span class="op">(</span><span class="str">'id'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BigAutoField</span><span class="op">(</span><span class="nam">auto_created</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">primary_key</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">serialize</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'ID'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">                <span class="op">(</span><span class="str">'name'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">help_text</span><span class="op">=</span><span class="str">'Nome do comentarista'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">100</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'nome'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">                <span class="op">(</span><span class="str">'email'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">EmailField</span><span class="op">(</span><span class="nam">help_text</span><span class="op">=</span><span class="str">'Email do comentarista'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">254</span><span class="op">,</span> <span class="nam">validators</span><span class="op">=</span><span class="op">[</span><span class="nam">django</span><span class="op">.</span><span class="nam">core</span><span class="op">.</span><span class="nam">validators</span><span class="op">.</span><span class="nam">EmailValidator</span><span class="op">(</span><span class="op">)</span><span class="op">]</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'email'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">                <span class="op">(</span><span class="str">'website'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">URLField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Website do comentarista (opcional)'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'website'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">                <span class="op">(</span><span class="str">'content'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">TextField</span><span class="op">(</span><span class="nam">help_text</span><span class="op">=</span><span class="str">'Conte&#250;do do coment&#225;rio'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'coment&#225;rio'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">                <span class="op">(</span><span class="str">'is_approved'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BooleanField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Se o coment&#225;rio foi aprovado para exibi&#231;&#227;o'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'aprovado'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">                <span class="op">(</span><span class="str">'is_spam'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BooleanField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Se o coment&#225;rio foi marcado como spam'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'spam'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">                <span class="op">(</span><span class="str">'ip_address'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">GenericIPAddressField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'IP do comentarista'</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'endere&#231;o IP'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">                <span class="op">(</span><span class="str">'user_agent'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">TextField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Informa&#231;&#245;es do navegador'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'user agent'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">                <span class="op">(</span><span class="str">'created_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">auto_now_add</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'criado em'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">                <span class="op">(</span><span class="str">'updated_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">auto_now</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'atualizado em'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">                <span class="op">(</span><span class="str">'approved_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Data e hora da aprova&#231;&#227;o'</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'aprovado em'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">                <span class="op">(</span><span class="str">'article'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">ForeignKey</span><span class="op">(</span><span class="nam">help_text</span><span class="op">=</span><span class="str">'Artigo comentado'</span><span class="op">,</span> <span class="nam">on_delete</span><span class="op">=</span><span class="nam">django</span><span class="op">.</span><span class="nam">db</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">deletion</span><span class="op">.</span><span class="nam">CASCADE</span><span class="op">,</span> <span class="nam">related_name</span><span class="op">=</span><span class="str">'comments'</span><span class="op">,</span> <span class="nam">to</span><span class="op">=</span><span class="str">'articles.article'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'artigo'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">                <span class="op">(</span><span class="str">'parent'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">ForeignKey</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Coment&#225;rio pai para criar thread de respostas'</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">on_delete</span><span class="op">=</span><span class="nam">django</span><span class="op">.</span><span class="nam">db</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">deletion</span><span class="op">.</span><span class="nam">CASCADE</span><span class="op">,</span> <span class="nam">related_name</span><span class="op">=</span><span class="str">'replies'</span><span class="op">,</span> <span class="nam">to</span><span class="op">=</span><span class="str">'articles.comment'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'coment&#225;rio pai'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">                <span class="op">(</span><span class="str">'user'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">ForeignKey</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Usu&#225;rio registrado (se aplic&#225;vel)'</span><span class="op">,</span> <span class="nam">null</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">on_delete</span><span class="op">=</span><span class="nam">django</span><span class="op">.</span><span class="nam">db</span><span class="op">.</span><span class="nam">models</span><span class="op">.</span><span class="nam">deletion</span><span class="op">.</span><span class="nam">CASCADE</span><span class="op">,</span> <span class="nam">related_name</span><span class="op">=</span><span class="str">'comments'</span><span class="op">,</span> <span class="nam">to</span><span class="op">=</span><span class="nam">settings</span><span class="op">.</span><span class="nam">AUTH_USER_MODEL</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'usu&#225;rio'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">            <span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">            <span class="nam">options</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">                <span class="str">'verbose_name'</span><span class="op">:</span> <span class="str">'coment&#225;rio'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">                <span class="str">'verbose_name_plural'</span><span class="op">:</span> <span class="str">'coment&#225;rios'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">                <span class="str">'ordering'</span><span class="op">:</span> <span class="op">[</span><span class="str">'-created_at'</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">            <span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">CreateModel</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">            <span class="nam">name</span><span class="op">=</span><span class="str">'Tag'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">            <span class="nam">fields</span><span class="op">=</span><span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">                <span class="op">(</span><span class="str">'id'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BigAutoField</span><span class="op">(</span><span class="nam">auto_created</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">primary_key</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">serialize</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'ID'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">                <span class="op">(</span><span class="str">'name'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">help_text</span><span class="op">=</span><span class="str">'Nome da tag'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">50</span><span class="op">,</span> <span class="nam">unique</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">validators</span><span class="op">=</span><span class="op">[</span><span class="nam">django</span><span class="op">.</span><span class="nam">core</span><span class="op">.</span><span class="nam">validators</span><span class="op">.</span><span class="nam">MinLengthValidator</span><span class="op">(</span><span class="num">2</span><span class="op">)</span><span class="op">]</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'nome'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">                <span class="op">(</span><span class="str">'slug'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">SlugField</span><span class="op">(</span><span class="nam">help_text</span><span class="op">=</span><span class="str">'URL amig&#225;vel da tag'</span><span class="op">,</span> <span class="nam">unique</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'slug'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">                <span class="op">(</span><span class="str">'description'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">TextField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Descri&#231;&#227;o da tag'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'descri&#231;&#227;o'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">                <span class="op">(</span><span class="str">'color'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="str">'#6c757d'</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Cor da tag em hexadecimal (ex: #6c757d)'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">7</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'cor'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">                <span class="op">(</span><span class="str">'is_featured'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">BooleanField</span><span class="op">(</span><span class="nam">default</span><span class="op">=</span><span class="key">False</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Se a tag deve aparecer em destaque'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'destaque'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">                <span class="op">(</span><span class="str">'meta_title'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'T&#237;tulo para SEO (m&#225;ximo 60 caracteres)'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">60</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'meta t&#237;tulo'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">                <span class="op">(</span><span class="str">'meta_description'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">CharField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Descri&#231;&#227;o para SEO (m&#225;ximo 160 caracteres)'</span><span class="op">,</span> <span class="nam">max_length</span><span class="op">=</span><span class="num">160</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'meta descri&#231;&#227;o'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">                <span class="op">(</span><span class="str">'created_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">auto_now_add</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'criado em'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">                <span class="op">(</span><span class="str">'updated_at'</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">DateTimeField</span><span class="op">(</span><span class="nam">auto_now</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'atualizado em'</span><span class="op">)</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">            <span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">            <span class="nam">options</span><span class="op">=</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">                <span class="str">'verbose_name'</span><span class="op">:</span> <span class="str">'tag'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">                <span class="str">'verbose_name_plural'</span><span class="op">:</span> <span class="str">'tags'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">                <span class="str">'ordering'</span><span class="op">:</span> <span class="op">[</span><span class="str">'name'</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">                <span class="str">'indexes'</span><span class="op">:</span> <span class="op">[</span><span class="nam">models</span><span class="op">.</span><span class="nam">Index</span><span class="op">(</span><span class="nam">fields</span><span class="op">=</span><span class="op">[</span><span class="str">'slug'</span><span class="op">]</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'articles_ta_slug_7a73a7_idx'</span><span class="op">)</span><span class="op">,</span> <span class="nam">models</span><span class="op">.</span><span class="nam">Index</span><span class="op">(</span><span class="nam">fields</span><span class="op">=</span><span class="op">[</span><span class="str">'is_featured'</span><span class="op">]</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'articles_ta_is_feat_c6c5a4_idx'</span><span class="op">)</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">            <span class="op">}</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">AddField</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">            <span class="nam">model_name</span><span class="op">=</span><span class="str">'article'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t">            <span class="nam">name</span><span class="op">=</span><span class="str">'tags'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">            <span class="nam">field</span><span class="op">=</span><span class="nam">models</span><span class="op">.</span><span class="nam">ManyToManyField</span><span class="op">(</span><span class="nam">blank</span><span class="op">=</span><span class="key">True</span><span class="op">,</span> <span class="nam">help_text</span><span class="op">=</span><span class="str">'Tags relacionadas ao artigo'</span><span class="op">,</span> <span class="nam">related_name</span><span class="op">=</span><span class="str">'articles'</span><span class="op">,</span> <span class="nam">to</span><span class="op">=</span><span class="str">'articles.tag'</span><span class="op">,</span> <span class="nam">verbose_name</span><span class="op">=</span><span class="str">'tags'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">AddIndex</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t">            <span class="nam">model_name</span><span class="op">=</span><span class="str">'category'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">            <span class="nam">index</span><span class="op">=</span><span class="nam">models</span><span class="op">.</span><span class="nam">Index</span><span class="op">(</span><span class="nam">fields</span><span class="op">=</span><span class="op">[</span><span class="str">'slug'</span><span class="op">]</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'articles_ca_slug_984c4d_idx'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">AddIndex</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t">            <span class="nam">model_name</span><span class="op">=</span><span class="str">'category'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t">            <span class="nam">index</span><span class="op">=</span><span class="nam">models</span><span class="op">.</span><span class="nam">Index</span><span class="op">(</span><span class="nam">fields</span><span class="op">=</span><span class="op">[</span><span class="str">'is_active'</span><span class="op">,</span> <span class="str">'order'</span><span class="op">]</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'articles_ca_is_acti_8e81fe_idx'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">AddIndex</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t">            <span class="nam">model_name</span><span class="op">=</span><span class="str">'comment'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t">            <span class="nam">index</span><span class="op">=</span><span class="nam">models</span><span class="op">.</span><span class="nam">Index</span><span class="op">(</span><span class="nam">fields</span><span class="op">=</span><span class="op">[</span><span class="str">'article'</span><span class="op">,</span> <span class="str">'is_approved'</span><span class="op">,</span> <span class="str">'-created_at'</span><span class="op">]</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'articles_co_article_fc16e2_idx'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">AddIndex</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t">            <span class="nam">model_name</span><span class="op">=</span><span class="str">'comment'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">            <span class="nam">index</span><span class="op">=</span><span class="nam">models</span><span class="op">.</span><span class="nam">Index</span><span class="op">(</span><span class="nam">fields</span><span class="op">=</span><span class="op">[</span><span class="str">'user'</span><span class="op">,</span> <span class="str">'-created_at'</span><span class="op">]</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'articles_co_user_id_95420e_idx'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">AddIndex</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">            <span class="nam">model_name</span><span class="op">=</span><span class="str">'comment'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t">            <span class="nam">index</span><span class="op">=</span><span class="nam">models</span><span class="op">.</span><span class="nam">Index</span><span class="op">(</span><span class="nam">fields</span><span class="op">=</span><span class="op">[</span><span class="str">'is_approved'</span><span class="op">,</span> <span class="str">'-created_at'</span><span class="op">]</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'articles_co_is_appr_2d7dab_idx'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">AddIndex</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t">            <span class="nam">model_name</span><span class="op">=</span><span class="str">'comment'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t">            <span class="nam">index</span><span class="op">=</span><span class="nam">models</span><span class="op">.</span><span class="nam">Index</span><span class="op">(</span><span class="nam">fields</span><span class="op">=</span><span class="op">[</span><span class="str">'parent'</span><span class="op">,</span> <span class="str">'-created_at'</span><span class="op">]</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'articles_co_parent__881c82_idx'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">AddIndex</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">            <span class="nam">model_name</span><span class="op">=</span><span class="str">'article'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">            <span class="nam">index</span><span class="op">=</span><span class="nam">models</span><span class="op">.</span><span class="nam">Index</span><span class="op">(</span><span class="nam">fields</span><span class="op">=</span><span class="op">[</span><span class="str">'slug'</span><span class="op">]</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'articles_ar_slug_452037_idx'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">AddIndex</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">            <span class="nam">model_name</span><span class="op">=</span><span class="str">'article'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t">            <span class="nam">index</span><span class="op">=</span><span class="nam">models</span><span class="op">.</span><span class="nam">Index</span><span class="op">(</span><span class="nam">fields</span><span class="op">=</span><span class="op">[</span><span class="str">'status'</span><span class="op">,</span> <span class="str">'published_at'</span><span class="op">]</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'articles_ar_status_7759bd_idx'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">AddIndex</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">            <span class="nam">model_name</span><span class="op">=</span><span class="str">'article'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">            <span class="nam">index</span><span class="op">=</span><span class="nam">models</span><span class="op">.</span><span class="nam">Index</span><span class="op">(</span><span class="nam">fields</span><span class="op">=</span><span class="op">[</span><span class="str">'author'</span><span class="op">,</span> <span class="str">'-published_at'</span><span class="op">]</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'articles_ar_author__1f1edd_idx'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">AddIndex</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t161" href="#t161">161</a></span><span class="t">            <span class="nam">model_name</span><span class="op">=</span><span class="str">'article'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t162" href="#t162">162</a></span><span class="t">            <span class="nam">index</span><span class="op">=</span><span class="nam">models</span><span class="op">.</span><span class="nam">Index</span><span class="op">(</span><span class="nam">fields</span><span class="op">=</span><span class="op">[</span><span class="str">'category'</span><span class="op">,</span> <span class="str">'-published_at'</span><span class="op">]</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'articles_ar_categor_82196d_idx'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t163" href="#t163">163</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t164" href="#t164">164</a></span><span class="t">        <span class="nam">migrations</span><span class="op">.</span><span class="nam">AddIndex</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t165" href="#t165">165</a></span><span class="t">            <span class="nam">model_name</span><span class="op">=</span><span class="str">'article'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t166" href="#t166">166</a></span><span class="t">            <span class="nam">index</span><span class="op">=</span><span class="nam">models</span><span class="op">.</span><span class="nam">Index</span><span class="op">(</span><span class="nam">fields</span><span class="op">=</span><span class="op">[</span><span class="str">'is_featured'</span><span class="op">,</span> <span class="str">'-published_at'</span><span class="op">]</span><span class="op">,</span> <span class="nam">name</span><span class="op">=</span><span class="str">'articles_ar_is_feat_3f99ec_idx'</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t167" href="#t167">167</a></span><span class="t">        <span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t168" href="#t168">168</a></span><span class="t">    <span class="op">]</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_10e6b26d1150d373_services_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_6ac3d5cc3c7d2853___init___py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 21:05 -0300
        </p>
    </div>
</footer>
</body>
</html>
