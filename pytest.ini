[tool:pytest]
DJANGO_SETTINGS_MODULE = core.settings
python_files = tests.py test_*.py *_tests.py
python_classes = Test* *Tests
python_functions = test_*
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=apps
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
testpaths = apps
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    models: marks tests for models
    views: marks tests for views
    services: marks tests for services
    repositories: marks tests for repositories
    forms: marks tests for forms
    middleware: marks tests for middleware
    validators: marks tests for validators
