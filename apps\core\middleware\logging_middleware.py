"""
Middleware para logging avançado de requests
"""

import time
import uuid
import logging
from django.utils.deprecation import MiddlewareMixin
from django.urls import resolve, Resolver404

logger = logging.getLogger('havoc.requests')
security_logger = logging.getLogger('havoc.security')
performance_logger = logging.getLogger('havoc.performance')


class RequestLoggingMiddleware(MiddlewareMixin):
    """Middleware para logging detalhado de requests"""
    
    def process_request(self, request):
        """Processa request inicial"""
        # Gera ID único para o request
        request.id = str(uuid.uuid4())[:8]
        request.start_time = time.time()
        
        # Extrai informações do request
        user_id = getattr(request.user, 'id', None) if hasattr(request, 'user') else None
        ip_address = self.get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # Log do início do request
        logger.info(
            f"Request iniciado: {request.method} {request.path}",
            extra={
                'request_id': request.id,
                'user_id': user_id,
                'ip_address': ip_address,
                'user_agent': user_agent,
                'method': request.method,
                'path': request.path,
                'query_params': dict(request.GET),
            }
        )
        
        # Log de segurança para requests suspeitos
        self.check_security_concerns(request, ip_address, user_agent)
    
    def process_response(self, request, response):
        """Processa response final"""
        if not hasattr(request, 'start_time'):
            return response
        
        # Calcula tempo de execução
        execution_time = time.time() - request.start_time
        
        # Extrai informações
        user_id = getattr(request.user, 'id', None) if hasattr(request, 'user') else None
        ip_address = self.get_client_ip(request)
        
        # Determina view name
        view_name = self.get_view_name(request)
        
        # Log do response
        logger.info(
            f"Request finalizado: {request.method} {request.path} - {response.status_code}",
            extra={
                'request_id': getattr(request, 'id', 'unknown'),
                'user_id': user_id,
                'ip_address': ip_address,
                'method': request.method,
                'path': request.path,
                'status_code': response.status_code,
                'execution_time': round(execution_time, 3),
                'view_name': view_name,
                'response_size': len(response.content) if hasattr(response, 'content') else 0,
            }
        )
        
        # Log de performance para requests lentos
        if execution_time > 2.0:  # Mais de 2 segundos
            performance_logger.warning(
                f"Request lento detectado: {execution_time:.3f}s",
                extra={
                    'request_id': getattr(request, 'id', 'unknown'),
                    'execution_time': execution_time,
                    'path': request.path,
                    'view_name': view_name,
                }
            )
        
        return response
    
    def process_exception(self, request, exception):
        """Processa exceções"""
        user_id = getattr(request.user, 'id', None) if hasattr(request, 'user') else None
        ip_address = self.get_client_ip(request)
        execution_time = time.time() - getattr(request, 'start_time', time.time())
        
        logger.error(
            f"Exceção no request: {request.method} {request.path}",
            extra={
                'request_id': getattr(request, 'id', 'unknown'),
                'user_id': user_id,
                'ip_address': ip_address,
                'method': request.method,
                'path': request.path,
                'execution_time': round(execution_time, 3),
                'exception_type': type(exception).__name__,
                'exception_message': str(exception),
            },
            exc_info=True
        )
    
    def get_client_ip(self, request):
        """Extrai IP real do cliente"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def get_view_name(self, request):
        """Extrai nome da view"""
        try:
            resolver_match = resolve(request.path)
            return f"{resolver_match.app_name}:{resolver_match.url_name}" if resolver_match.app_name else resolver_match.url_name
        except Resolver404:
            return 'unknown'
    
    def check_security_concerns(self, request, ip_address, user_agent):
        """Verifica preocupações de segurança"""
        # Lista de user agents suspeitos
        suspicious_agents = [
            'sqlmap', 'nikto', 'nmap', 'masscan', 'zap',
            'burp', 'w3af', 'acunetix', 'nessus'
        ]
        
        # Lista de paths suspeitos
        suspicious_paths = [
            '/admin/', '/wp-admin/', '/phpmyadmin/', '/.env',
            '/config.php', '/wp-config.php', '/etc/passwd'
        ]
        
        # Verifica user agent suspeito
        if any(agent.lower() in user_agent.lower() for agent in suspicious_agents):
            security_logger.warning(
                f"User agent suspeito detectado: {user_agent}",
                extra={
                    'request_id': getattr(request, 'id', 'unknown'),
                    'ip_address': ip_address,
                    'user_agent': user_agent,
                    'path': request.path,
                    'security_event': 'suspicious_user_agent'
                }
            )
        
        # Verifica path suspeito
        if any(path in request.path.lower() for path in suspicious_paths):
            security_logger.warning(
                f"Tentativa de acesso a path suspeito: {request.path}",
                extra={
                    'request_id': getattr(request, 'id', 'unknown'),
                    'ip_address': ip_address,
                    'user_agent': user_agent,
                    'path': request.path,
                    'security_event': 'suspicious_path_access'
                }
            )
        
        # Verifica múltiplos parâmetros (possível SQL injection)
        if len(request.GET) > 10:
            security_logger.warning(
                f"Request com muitos parâmetros GET: {len(request.GET)}",
                extra={
                    'request_id': getattr(request, 'id', 'unknown'),
                    'ip_address': ip_address,
                    'path': request.path,
                    'param_count': len(request.GET),
                    'security_event': 'excessive_parameters'
                }
            )


class DatabaseQueryLoggingMiddleware(MiddlewareMixin):
    """Middleware para logging de queries de database"""
    
    def process_request(self, request):
        """Inicia contagem de queries"""
        from django.db import connection
        request.queries_start = len(connection.queries)
    
    def process_response(self, request, response):
        """Log de queries executadas"""
        if not hasattr(request, 'queries_start'):
            return response
        
        from django.db import connection
        queries_count = len(connection.queries) - request.queries_start
        
        if queries_count > 0:
            execution_time = getattr(request, 'start_time', 0)
            if execution_time:
                execution_time = time.time() - execution_time
            
            performance_logger.info(
                f"Database queries: {queries_count} queries executadas",
                extra={
                    'request_id': getattr(request, 'id', 'unknown'),
                    'path': request.path,
                    'queries_count': queries_count,
                    'execution_time': round(execution_time, 3) if execution_time else None,
                }
            )
            
            # Log de warning para muitas queries
            if queries_count > 20:
                performance_logger.warning(
                    f"Muitas queries detectadas: {queries_count}",
                    extra={
                        'request_id': getattr(request, 'id', 'unknown'),
                        'path': request.path,
                        'queries_count': queries_count,
                        'performance_issue': 'excessive_queries'
                    }
                )
        
        return response


class UserActivityLoggingMiddleware(MiddlewareMixin):
    """Middleware para logging de atividades do usuário"""
    
    def process_response(self, request, response):
        """Log de atividades importantes do usuário"""
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            return response
        
        # Log apenas para requests importantes
        important_methods = ['POST', 'PUT', 'PATCH', 'DELETE']
        if request.method not in important_methods:
            return response
        
        # Log apenas para views específicas
        important_paths = [
            '/accounts/', '/config/', '/admin/',
            '/api/', '/articles/'
        ]
        
        if not any(path in request.path for path in important_paths):
            return response
        
        audit_logger = logging.getLogger('havoc.audit')
        
        audit_logger.info(
            f"Atividade do usuário: {request.method} {request.path}",
            extra={
                'request_id': getattr(request, 'id', 'unknown'),
                'user_id': request.user.id,
                'username': request.user.username,
                'email': request.user.email,
                'ip_address': self.get_client_ip(request),
                'method': request.method,
                'path': request.path,
                'status_code': response.status_code,
                'activity_type': 'user_action'
            }
        )
        
        return response
    
    def get_client_ip(self, request):
        """Extrai IP real do cliente"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
