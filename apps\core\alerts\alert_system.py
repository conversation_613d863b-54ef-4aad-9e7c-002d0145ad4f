"""
Sistema de alertas automáticos para logs de erro
"""

import logging
import smtplib
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from django.conf import settings
from django.core.cache import cache
from django.template.loader import render_to_string
from django.utils import timezone


class AlertLevel:
    """Níveis de alerta"""
    LOW = 'low'
    MEDIUM = 'medium'
    HIGH = 'high'
    CRITICAL = 'critical'


class AlertManager:
    """Gerenciador de alertas automáticos"""
    
    def __init__(self):
        self.logger = logging.getLogger('havoc.alerts')
        self.alert_rules = self._load_alert_rules()
        self.notification_channels = self._load_notification_channels()
    
    def _load_alert_rules(self) -> Dict:
        """Carrega regras de alerta"""
        return {
            'error_rate': {
                'threshold': 10,  # 10 erros em 5 minutos
                'window': 300,    # 5 minutos
                'level': AlertLevel.HIGH,
                'description': 'Taxa de erro elevada detectada'
            },
            'critical_errors': {
                'threshold': 1,   # 1 erro crítico
                'window': 60,     # 1 minuto
                'level': AlertLevel.CRITICAL,
                'description': 'Erro crítico detectado'
            },
            'security_events': {
                'threshold': 5,   # 5 eventos de segurança em 10 minutos
                'window': 600,    # 10 minutos
                'level': AlertLevel.HIGH,
                'description': 'Múltiplos eventos de segurança detectados'
            },
            'slow_requests': {
                'threshold': 20,  # 20 requests lentos em 15 minutos
                'window': 900,    # 15 minutos
                'level': AlertLevel.MEDIUM,
                'description': 'Muitos requests lentos detectados'
            },
            'database_errors': {
                'threshold': 5,   # 5 erros de database em 5 minutos
                'window': 300,    # 5 minutos
                'level': AlertLevel.HIGH,
                'description': 'Múltiplos erros de database detectados'
            }
        }
    
    def _load_notification_channels(self) -> Dict:
        """Carrega canais de notificação"""
        return {
            'email': {
                'enabled': getattr(settings, 'ALERTS_EMAIL_ENABLED', True),
                'recipients': getattr(settings, 'ALERTS_EMAIL_RECIPIENTS', ['<EMAIL>']),
                'smtp_host': getattr(settings, 'EMAIL_HOST', 'localhost'),
                'smtp_port': getattr(settings, 'EMAIL_PORT', 587),
                'smtp_user': getattr(settings, 'EMAIL_HOST_USER', ''),
                'smtp_password': getattr(settings, 'EMAIL_HOST_PASSWORD', ''),
                'use_tls': getattr(settings, 'EMAIL_USE_TLS', True),
            },
            'webhook': {
                'enabled': getattr(settings, 'ALERTS_WEBHOOK_ENABLED', False),
                'url': getattr(settings, 'ALERTS_WEBHOOK_URL', ''),
                'headers': getattr(settings, 'ALERTS_WEBHOOK_HEADERS', {}),
            },
            'slack': {
                'enabled': getattr(settings, 'ALERTS_SLACK_ENABLED', False),
                'webhook_url': getattr(settings, 'ALERTS_SLACK_WEBHOOK_URL', ''),
                'channel': getattr(settings, 'ALERTS_SLACK_CHANNEL', '#alerts'),
            }
        }
    
    def process_log_entry(self, log_record: logging.LogRecord):
        """Processa entrada de log para detectar alertas"""
        try:
            # Extrai informações do log
            log_data = self._extract_log_data(log_record)
            
            # Verifica cada regra de alerta
            for rule_name, rule_config in self.alert_rules.items():
                if self._should_check_rule(rule_name, log_data):
                    self._check_alert_rule(rule_name, rule_config, log_data)
                    
        except Exception as e:
            self.logger.error(f"Erro ao processar log para alertas: {e}")
    
    def _extract_log_data(self, log_record: logging.LogRecord) -> Dict:
        """Extrai dados relevantes do log"""
        return {
            'level': log_record.levelname,
            'message': log_record.getMessage(),
            'module': log_record.module,
            'function': log_record.funcName,
            'timestamp': datetime.fromtimestamp(log_record.created),
            'user_id': getattr(log_record, 'user_id', None),
            'request_id': getattr(log_record, 'request_id', None),
            'ip_address': getattr(log_record, 'ip_address', None),
            'execution_time': getattr(log_record, 'execution_time', None),
            'security_event': getattr(log_record, 'security_event', None),
            'performance_issue': getattr(log_record, 'performance_issue', None),
        }
    
    def _should_check_rule(self, rule_name: str, log_data: Dict) -> bool:
        """Determina se deve verificar uma regra específica"""
        rule_checks = {
            'error_rate': log_data['level'] in ['ERROR', 'CRITICAL'],
            'critical_errors': log_data['level'] == 'CRITICAL',
            'security_events': log_data.get('security_event') is not None,
            'slow_requests': log_data.get('performance_issue') == 'slow_operation',
            'database_errors': 'database' in log_data['message'].lower() and log_data['level'] == 'ERROR'
        }
        
        return rule_checks.get(rule_name, False)
    
    def _check_alert_rule(self, rule_name: str, rule_config: Dict, log_data: Dict):
        """Verifica se uma regra de alerta foi violada"""
        cache_key = f"alert_rule_{rule_name}"
        window_start = timezone.now() - timedelta(seconds=rule_config['window'])
        
        # Obtém eventos recentes do cache
        recent_events = cache.get(cache_key, [])
        
        # Remove eventos antigos
        recent_events = [
            event for event in recent_events 
            if event['timestamp'] > window_start.timestamp()
        ]
        
        # Adiciona evento atual
        recent_events.append({
            'timestamp': log_data['timestamp'].timestamp(),
            'data': log_data
        })
        
        # Atualiza cache
        cache.set(cache_key, recent_events, rule_config['window'] + 60)
        
        # Verifica se threshold foi atingido
        if len(recent_events) >= rule_config['threshold']:
            self._trigger_alert(rule_name, rule_config, recent_events)
    
    def _trigger_alert(self, rule_name: str, rule_config: Dict, events: List[Dict]):
        """Dispara um alerta"""
        # Verifica se já foi enviado alerta recentemente (evita spam)
        cooldown_key = f"alert_cooldown_{rule_name}"
        if cache.get(cooldown_key):
            return
        
        # Define cooldown (15 minutos)
        cache.set(cooldown_key, True, 900)
        
        alert_data = {
            'rule_name': rule_name,
            'level': rule_config['level'],
            'description': rule_config['description'],
            'threshold': rule_config['threshold'],
            'window': rule_config['window'],
            'event_count': len(events),
            'events': events[-5:],  # Últimos 5 eventos
            'timestamp': timezone.now(),
        }
        
        self.logger.warning(
            f"ALERTA DISPARADO: {rule_config['description']}",
            extra={
                'alert_rule': rule_name,
                'alert_level': rule_config['level'],
                'event_count': len(events),
                'threshold': rule_config['threshold']
            }
        )
        
        # Envia notificações
        self._send_notifications(alert_data)
    
    def _send_notifications(self, alert_data: Dict):
        """Envia notificações através dos canais configurados"""
        for channel_name, channel_config in self.notification_channels.items():
            if channel_config.get('enabled', False):
                try:
                    if channel_name == 'email':
                        self._send_email_alert(alert_data, channel_config)
                    elif channel_name == 'webhook':
                        self._send_webhook_alert(alert_data, channel_config)
                    elif channel_name == 'slack':
                        self._send_slack_alert(alert_data, channel_config)
                        
                except Exception as e:
                    self.logger.error(f"Erro ao enviar alerta via {channel_name}: {e}")
    
    def _send_email_alert(self, alert_data: Dict, config: Dict):
        """Envia alerta por email"""
        subject = f"🚨 ALERTA HAVOC - {alert_data['level'].upper()}: {alert_data['description']}"
        
        # Renderiza template do email
        html_content = self._render_email_template(alert_data)
        text_content = self._render_text_alert(alert_data)
        
        # Cria mensagem
        msg = MIMEMultipart('alternative')
        msg['Subject'] = subject
        msg['From'] = config.get('smtp_user', '<EMAIL>')
        msg['To'] = ', '.join(config['recipients'])

        # Adiciona conteúdo
        msg.attach(MIMEText(text_content, 'plain'))
        msg.attach(MIMEText(html_content, 'html'))
        
        # Envia email
        with smtplib.SMTP(config['smtp_host'], config['smtp_port']) as server:
            if config.get('use_tls'):
                server.starttls()
            if config.get('smtp_user') and config.get('smtp_password'):
                server.login(config['smtp_user'], config['smtp_password'])
            server.send_message(msg)
        
        self.logger.info(f"Alerta enviado por email para {len(config['recipients'])} destinatários")
    
    def _send_webhook_alert(self, alert_data: Dict, config: Dict):
        """Envia alerta via webhook"""
        import requests
        
        payload = {
            'alert_type': 'havoc_system_alert',
            'level': alert_data['level'],
            'rule': alert_data['rule_name'],
            'description': alert_data['description'],
            'event_count': alert_data['event_count'],
            'threshold': alert_data['threshold'],
            'timestamp': alert_data['timestamp'].isoformat(),
            'events': alert_data['events']
        }
        
        headers = {
            'Content-Type': 'application/json',
            **config.get('headers', {})
        }
        
        response = requests.post(
            config['url'],
            json=payload,
            headers=headers,
            timeout=10
        )
        
        response.raise_for_status()
        self.logger.info(f"Alerta enviado via webhook: {response.status_code}")
    
    def _send_slack_alert(self, alert_data: Dict, config: Dict):
        """Envia alerta para Slack"""
        import requests
        
        # Emoji baseado no nível
        emoji_map = {
            AlertLevel.LOW: '🟡',
            AlertLevel.MEDIUM: '🟠',
            AlertLevel.HIGH: '🔴',
            AlertLevel.CRITICAL: '🚨'
        }
        
        emoji = emoji_map.get(alert_data['level'], '⚠️')
        
        payload = {
            'channel': config['channel'],
            'username': 'Havoc Alerts',
            'icon_emoji': ':warning:',
            'text': f"{emoji} *ALERTA HAVOC - {alert_data['level'].upper()}*",
            'attachments': [
                {
                    'color': 'danger' if alert_data['level'] in [AlertLevel.HIGH, AlertLevel.CRITICAL] else 'warning',
                    'fields': [
                        {
                            'title': 'Descrição',
                            'value': alert_data['description'],
                            'short': False
                        },
                        {
                            'title': 'Eventos',
                            'value': f"{alert_data['event_count']} eventos em {alert_data['window']}s",
                            'short': True
                        },
                        {
                            'title': 'Threshold',
                            'value': str(alert_data['threshold']),
                            'short': True
                        }
                    ],
                    'ts': alert_data['timestamp'].timestamp()
                }
            ]
        }
        
        response = requests.post(
            config['webhook_url'],
            json=payload,
            timeout=10
        )
        
        response.raise_for_status()
        self.logger.info(f"Alerta enviado para Slack: {response.status_code}")
    
    def _render_email_template(self, alert_data: Dict) -> str:
        """Renderiza template HTML do email"""
        template_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Alerta Havoc</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; }}
                .alert {{ border-left: 4px solid #dc3545; padding: 15px; margin: 10px 0; }}
                .alert.critical {{ border-color: #dc3545; background-color: #f8d7da; }}
                .alert.high {{ border-color: #fd7e14; background-color: #fff3cd; }}
                .alert.medium {{ border-color: #ffc107; background-color: #fff3cd; }}
                .alert.low {{ border-color: #28a745; background-color: #d4edda; }}
                .header {{ color: #721c24; font-size: 18px; font-weight: bold; }}
                .details {{ margin: 15px 0; }}
                .event {{ background-color: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 4px; }}
            </style>
        </head>
        <body>
            <div class="alert {alert_data['level']}">
                <div class="header">🚨 ALERTA HAVOC - {alert_data['level'].upper()}</div>
                <div class="details">
                    <p><strong>Descrição:</strong> {alert_data['description']}</p>
                    <p><strong>Regra:</strong> {alert_data['rule_name']}</p>
                    <p><strong>Eventos:</strong> {alert_data['event_count']} eventos detectados</p>
                    <p><strong>Threshold:</strong> {alert_data['threshold']} eventos em {alert_data['window']} segundos</p>
                    <p><strong>Timestamp:</strong> {alert_data['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
                
                <h3>Últimos Eventos:</h3>
                {"".join([f'<div class="event"><strong>{datetime.fromtimestamp(event["timestamp"]).strftime("%H:%M:%S")}:</strong> {event["data"]["message"][:100]}...</div>' for event in alert_data['events']])}
            </div>
        </body>
        </html>
        """
        return template_content
    
    def _render_text_alert(self, alert_data: Dict) -> str:
        """Renderiza versão texto do alerta"""
        return f"""
🚨 ALERTA HAVOC - {alert_data['level'].upper()}

Descrição: {alert_data['description']}
Regra: {alert_data['rule_name']}
Eventos: {alert_data['event_count']} eventos detectados
Threshold: {alert_data['threshold']} eventos em {alert_data['window']} segundos
Timestamp: {alert_data['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}

Últimos Eventos:
{chr(10).join([f"- {datetime.fromtimestamp(event['timestamp']).strftime('%H:%M:%S')}: {event['data']['message'][:100]}..." for event in alert_data['events']])}

---
Sistema de Alertas Havoc
        """


# Instância global do gerenciador de alertas
alert_manager = AlertManager()


class AlertHandler(logging.Handler):
    """Handler de logging que processa alertas automáticos"""
    
    def __init__(self):
        super().__init__()
        self.alert_manager = alert_manager
    
    def emit(self, record):
        """Processa log record para alertas"""
        try:
            # Só processa logs de nível WARNING ou superior
            if record.levelno >= logging.WARNING:
                self.alert_manager.process_log_entry(record)
        except Exception:
            # Não deve falhar o logging principal
            pass
