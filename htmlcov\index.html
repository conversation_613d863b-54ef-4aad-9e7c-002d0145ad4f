<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">41%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 21:05 -0300
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1___init___py.html">apps\accounts\__init__.py</a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_admin_py.html">apps\accounts\admin.py</a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_apps_py.html">apps\accounts\apps.py</a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_96c8905fe34cb2c1___init___py.html">apps\accounts\factories\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_96c8905fe34cb2c1_service_factory_py.html">apps\accounts\factories\service_factory.py</a></td>
                <td>23</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="13 23">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e___init___py.html">apps\accounts\forms\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_authentication_py.html">apps\accounts\forms\authentication.py</a></td>
                <td>86</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="24 86">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_password_reset_py.html">apps\accounts\forms\password_reset.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_profile_forms_py.html">apps\accounts\forms\profile_forms.py</a></td>
                <td>190</td>
                <td>145</td>
                <td>0</td>
                <td class="right" data-ratio="45 190">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f13b1157adba62e_registration_py.html">apps\accounts\forms\registration.py</a></td>
                <td>52</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="26 52">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ee7bd68557b5fff3___init___py.html">apps\accounts\handlers\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ee7bd68557b5fff3_user_handlers_py.html">apps\accounts\handlers\user_handlers.py</a></td>
                <td>25</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="17 25">68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7190ae31b87f647e___init___py.html">apps\accounts\interfaces\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7190ae31b87f647e_notifications_py.html">apps\accounts\interfaces\notifications.py</a></td>
                <td>11</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="8 11">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7190ae31b87f647e_repositories_py.html">apps\accounts\interfaces\repositories.py</a></td>
                <td>27</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="20 27">74%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7190ae31b87f647e_services_py.html">apps\accounts\interfaces\services.py</a></td>
                <td>22</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="17 22">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e66b20f0cc2b9be7___init___py.html">apps\accounts\management\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7a12aab24e24085f___init___py.html">apps\accounts\management\commands\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7a12aab24e24085f_create_test_users_py.html">apps\accounts\management\commands\create_test_users.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7a12aab24e24085f_test_email_py.html">apps\accounts\management\commands\test_email.py</a></td>
                <td>84</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="0 84">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_middleware_py.html">apps\accounts\middleware.py</a></td>
                <td>118</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="34 118">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e835377efbddd70_0001_initial_py.html">apps\accounts\migrations\0001_initial.py</a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e835377efbddd70_0002_user_avatar_user_bio_user_birth_date_user_location_and_more_py.html">apps\accounts\migrations\0002_user_avatar_user_bio_user_birth_date_user_location_and_more.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e835377efbddd70___init___py.html">apps\accounts\migrations\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_880a307ae6abc71f___init___py.html">apps\accounts\mixins\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_880a307ae6abc71f_service_mixins_py.html">apps\accounts\mixins\service_mixins.py</a></td>
                <td>18</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="9 18">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_424a1052f51881f8___init___py.html">apps\accounts\models\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_424a1052f51881f8_user_py.html">apps\accounts\models\user.py</a></td>
                <td>73</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="64 73">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_424a1052f51881f8_verification_py.html">apps\accounts\models\verification.py</a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6b96ac88d8d43145___init___py.html">apps\accounts\notifications\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6b96ac88d8d43145_email_notification_py.html">apps\accounts\notifications\email_notification.py</a></td>
                <td>32</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="9 32">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b9b6fc10c689f92___init___py.html">apps\accounts\repositories\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b9b6fc10c689f92_user_repository_py.html">apps\accounts\repositories\user_repository.py</a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3b9b6fc10c689f92_verification_repository_py.html">apps\accounts\repositories\verification_repository.py</a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f42f61afc382a2f___init___py.html">apps\accounts\services\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f42f61afc382a2f_auth_service_py.html">apps\accounts\services\auth_service.py</a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f42f61afc382a2f_email_service_py.html">apps\accounts\services\email_service.py</a></td>
                <td>122</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="18 122">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f42f61afc382a2f_image_service_py.html">apps\accounts\services\image_service.py</a></td>
                <td>66</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="37 66">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f42f61afc382a2f_password_service_py.html">apps\accounts\services\password_service.py</a></td>
                <td>36</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="9 36">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f42f61afc382a2f_registration_service_py.html">apps\accounts\services\registration_service.py</a></td>
                <td>45</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="40 45">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_signals_py.html">apps\accounts\signals.py</a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_tests_py.html">apps\accounts\tests.py</a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230___init___py.html">apps\accounts\tests\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_forms_py.html">apps\accounts\tests\test_forms.py</a></td>
                <td>128</td>
                <td>128</td>
                <td>0</td>
                <td class="right" data-ratio="0 128">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_middleware_py.html">apps\accounts\tests\test_middleware.py</a></td>
                <td>138</td>
                <td>138</td>
                <td>0</td>
                <td class="right" data-ratio="0 138">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_models_py.html">apps\accounts\tests\test_models.py</a></td>
                <td>108</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="108 108">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_repositories_py.html">apps\accounts\tests\test_repositories.py</a></td>
                <td>92</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="92 92">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_services_py.html">apps\accounts\tests\test_services.py</a></td>
                <td>121</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="121 121">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_validators_py.html">apps\accounts\tests\test_validators.py</a></td>
                <td>126</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="104 126">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4cd509e0a38bf230_test_views_py.html">apps\accounts\tests\test_views.py</a></td>
                <td>114</td>
                <td>114</td>
                <td>0</td>
                <td class="right" data-ratio="0 114">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_urls_py.html">apps\accounts\urls.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0daf9d4b3925bce1_utils_py.html">apps\accounts\utils.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af5f57e6b6e54959___init___py.html">apps\accounts\validators\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_af5f57e6b6e54959_user_validators_py.html">apps\accounts\validators\user_validators.py</a></td>
                <td>67</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="67 67">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6___init___py.html">apps\accounts\views\__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_authentication_py.html">apps\accounts\views\authentication.py</a></td>
                <td>56</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="18 56">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_email_test_py.html">apps\accounts\views\email_test.py</a></td>
                <td>125</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="26 125">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_password_reset_py.html">apps\accounts\views\password_reset.py</a></td>
                <td>60</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="18 60">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_profile_py.html">apps\accounts\views\profile.py</a></td>
                <td>117</td>
                <td>89</td>
                <td>0</td>
                <td class="right" data-ratio="28 117">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ae12c07b376a20c6_registration_py.html">apps\accounts\views\registration.py</a></td>
                <td>86</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="24 86">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b6255d6c5a7dc68___init___py.html">apps\articles\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b6255d6c5a7dc68_admin_py.html">apps\articles\admin.py</a></td>
                <td>47</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="40 47">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b6255d6c5a7dc68_apps_py.html">apps\articles\apps.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_10e6b26d1150d373___init___py.html">apps\articles\interfaces\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_10e6b26d1150d373_repositories_py.html">apps\articles\interfaces\repositories.py</a></td>
                <td>115</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="79 115">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_10e6b26d1150d373_services_py.html">apps\articles\interfaces\services.py</a></td>
                <td>75</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="53 75">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ac3d5cc3c7d2853_0001_initial_py.html">apps\articles\migrations\0001_initial.py</a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ac3d5cc3c7d2853___init___py.html">apps\articles\migrations\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b6255d6c5a7dc68_models_py.html">apps\articles\models.py</a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d9000b597388c2b4___init___py.html">apps\articles\models\__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d9000b597388c2b4_article_py.html">apps\articles\models\article.py</a></td>
                <td>73</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="50 73">68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d9000b597388c2b4_category_py.html">apps\articles\models\category.py</a></td>
                <td>50</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="34 50">68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d9000b597388c2b4_comment_py.html">apps\articles\models\comment.py</a></td>
                <td>57</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="37 57">65%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d9000b597388c2b4_tag_py.html">apps\articles\models\tag.py</a></td>
                <td>35</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="27 35">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6831235e64db73da___init___py.html">apps\articles\repositories\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6831235e64db73da_article_repository_py.html">apps\articles\repositories\article_repository.py</a></td>
                <td>97</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="24 97">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_99cb94331c41d741___init___py.html">apps\articles\services\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_99cb94331c41d741_article_service_py.html">apps\articles\services\article_service.py</a></td>
                <td>86</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="27 86">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b6255d6c5a7dc68_tests_py.html">apps\articles\tests.py</a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b6255d6c5a7dc68_urls_py.html">apps\articles\urls.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9b6255d6c5a7dc68_views_py.html">apps\articles\views.py</a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ea92ad1c62fa4397___init___py.html">apps\articles\views\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ea92ad1c62fa4397_article_views_py.html">apps\articles\views\article_views.py</a></td>
                <td>43</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="13 43">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3___init___py.html">apps\config\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_admin_py.html">apps\config\admin.py</a></td>
                <td>38</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="32 38">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_apps_py.html">apps\config\apps.py</a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a___init___py.html">apps\config\forms\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_advanced_config_forms_py.html">apps\config\forms\advanced_config_forms.py</a></td>
                <td>187</td>
                <td>130</td>
                <td>0</td>
                <td class="right" data-ratio="57 187">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_multi_config_forms_py.html">apps\config\forms\multi_config_forms.py</a></td>
                <td>74</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="24 74">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e131a04fbcf9a69a_user_forms_py.html">apps\config\forms\user_forms.py</a></td>
                <td>98</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="48 98">49%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2807aa7e64bc1821___init___py.html">apps\config\interfaces\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2807aa7e64bc1821_repositories_py.html">apps\config\interfaces\repositories.py</a></td>
                <td>113</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="79 113">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2807aa7e64bc1821_services_py.html">apps\config\interfaces\services.py</a></td>
                <td>67</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="48 67">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0017935c50f3bc1c___init___py.html">apps\config\management\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c595fba4bf79988f___init___py.html">apps\config\management\commands\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c595fba4bf79988f_create_admin_user_py.html">apps\config\management\commands\create_admin_user.py</a></td>
                <td>74</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="0 74">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c595fba4bf79988f_migrate_configs_py.html">apps\config\management\commands\migrate_configs.py</a></td>
                <td>86</td>
                <td>86</td>
                <td>0</td>
                <td class="right" data-ratio="0 86">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c595fba4bf79988f_setup_permissions_py.html">apps\config\management\commands\setup_permissions.py</a></td>
                <td>49</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4df18df340a057a7_0001_initial_py.html">apps\config\migrations\0001_initial.py</a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4df18df340a057a7_0002_databaseconfiguration_emailconfiguration_py.html">apps\config\migrations\0002_databaseconfiguration_emailconfiguration.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4df18df340a057a7_0003_alter_emailconfiguration_email_host_password_and_more_py.html">apps\config\migrations\0003_alter_emailconfiguration_email_host_password_and_more.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4df18df340a057a7_0004_alter_emailconfiguration_created_by_and_more_py.html">apps\config\migrations\0004_alter_emailconfiguration_created_by_and_more.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4df18df340a057a7___init___py.html">apps\config\migrations\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_mixins_py.html">apps\config\mixins.py</a></td>
                <td>111</td>
                <td>87</td>
                <td>0</td>
                <td class="right" data-ratio="24 111">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_models_py.html">apps\config\models.py</a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_038d050f2a364532___init___py.html">apps\config\models\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_038d050f2a364532_configuration_models_py.html">apps\config\models\configuration_models.py</a></td>
                <td>163</td>
                <td>93</td>
                <td>0</td>
                <td class="right" data-ratio="70 163">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_038d050f2a364532_system_configuration_py.html">apps\config\models\system_configuration.py</a></td>
                <td>26</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="20 26">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_038d050f2a364532_user_activity_log_py.html">apps\config\models\user_activity_log.py</a></td>
                <td>21</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="19 21">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b825969784292050___init___py.html">apps\config\repositories\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b825969784292050_config_repository_py.html">apps\config\repositories\config_repository.py</a></td>
                <td>68</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="20 68">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b825969784292050_permission_repository_py.html">apps\config\repositories\permission_repository.py</a></td>
                <td>86</td>
                <td>86</td>
                <td>0</td>
                <td class="right" data-ratio="0 86">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b825969784292050_user_repository_py.html">apps\config\repositories\user_repository.py</a></td>
                <td>68</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="16 68">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f472e13a5c526310___init___py.html">apps\config\services\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f472e13a5c526310_permission_management_service_py.html">apps\config\services\permission_management_service.py</a></td>
                <td>70</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="0 70">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f472e13a5c526310_system_config_service_py.html">apps\config\services\system_config_service.py</a></td>
                <td>67</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="22 67">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f472e13a5c526310_user_management_service_py.html">apps\config\services\user_management_service.py</a></td>
                <td>62</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="20 62">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_signals_py.html">apps\config\signals.py</a></td>
                <td>20</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="17 20">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_89375c84e16dd47a___init___py.html">apps\config\templatetags\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_89375c84e16dd47a_config_extras_py.html">apps\config\templatetags\config_extras.py</a></td>
                <td>49</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="18 49">37%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_tests_py.html">apps\config\tests.py</a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_urls_py.html">apps\config\urls.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ad224057aff2f7a3_views_py.html">apps\config\views.py</a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1___init___py.html">apps\config\views\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_advanced_config_views_py.html">apps\config\views\advanced_config_views.py</a></td>
                <td>182</td>
                <td>151</td>
                <td>0</td>
                <td class="right" data-ratio="31 182">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_dashboard_py.html">apps\config\views\dashboard.py</a></td>
                <td>19</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="11 19">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_multi_config_views_py.html">apps\config\views\multi_config_views.py</a></td>
                <td>212</td>
                <td>160</td>
                <td>0</td>
                <td class="right" data-ratio="52 212">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_system_config_views_py.html">apps\config\views\system_config_views.py</a></td>
                <td>70</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="17 70">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7862fbf3146bb8a1_user_views_py.html">apps\config\views\user_views.py</a></td>
                <td>142</td>
                <td>111</td>
                <td>0</td>
                <td class="right" data-ratio="31 142">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e775d4d1883b20c___init___py.html">apps\pages\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e775d4d1883b20c_admin_py.html">apps\pages\admin.py</a></td>
                <td>29</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="23 29">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e775d4d1883b20c_apps_py.html">apps\pages\apps.py</a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e337c540cc623d9___init___py.html">apps\pages\forms\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e337c540cc623d9_contact_form_py.html">apps\pages\forms\contact_form.py</a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e337c540cc623d9_contact_forms_py.html">apps\pages\forms\contact_forms.py</a></td>
                <td>61</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="25 61">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_73977dc7d293fed5___init___py.html">apps\pages\interfaces\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_73977dc7d293fed5_repositories_py.html">apps\pages\interfaces\repositories.py</a></td>
                <td>79</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="55 79">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_73977dc7d293fed5_services_py.html">apps\pages\interfaces\services.py</a></td>
                <td>72</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="51 72">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2481a92531cb87aa_0001_initial_py.html">apps\pages\migrations\0001_initial.py</a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2481a92531cb87aa___init___py.html">apps\pages\migrations\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_293efa6a78a0dee6___init___py.html">apps\pages\models\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_293efa6a78a0dee6_navigation_py.html">apps\pages\models\navigation.py</a></td>
                <td>45</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="26 45">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_293efa6a78a0dee6_page_py.html">apps\pages\models\page.py</a></td>
                <td>66</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="45 66">68%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_293efa6a78a0dee6_seo_py.html">apps\pages\models\seo.py</a></td>
                <td>48</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="32 48">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7837dd24d49bbe44___init___py.html">apps\pages\repositories\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7837dd24d49bbe44_navigation_repository_py.html">apps\pages\repositories\navigation_repository.py</a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7837dd24d49bbe44_page_repository_py.html">apps\pages\repositories\page_repository.py</a></td>
                <td>101</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="24 101">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7837dd24d49bbe44_seo_repository_py.html">apps\pages\repositories\seo_repository.py</a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_368ff6a066478aeb___init___py.html">apps\pages\services\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_368ff6a066478aeb_navigation_service_py.html">apps\pages\services\navigation_service.py</a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_368ff6a066478aeb_page_service_py.html">apps\pages\services\page_service.py</a></td>
                <td>92</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="29 92">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_368ff6a066478aeb_seo_service_py.html">apps\pages\services\seo_service.py</a></td>
                <td>54</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="0 54">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e775d4d1883b20c_signals_py.html">apps\pages\signals.py</a></td>
                <td>38</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="17 38">45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e775d4d1883b20c_tests_py.html">apps\pages\tests.py</a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e775d4d1883b20c_urls_py.html">apps\pages\urls.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8e775d4d1883b20c_views_py.html">apps\pages\views.py</a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9e5a0612d4355adf___init___py.html">apps\pages\views\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9e5a0612d4355adf_home_py.html">apps\pages\views\home.py</a></td>
                <td>22</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="7 22">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9e5a0612d4355adf_page_views_py.html">apps\pages\views\page_views.py</a></td>
                <td>52</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="15 52">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9e5a0612d4355adf_static_pages_py.html">apps\pages\views\static_pages.py</a></td>
                <td>44</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="19 44">43%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>6482</td>
                <td>3795</td>
                <td>0</td>
                <td class="right" data-ratio="2687 6482">41%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-06 21:05 -0300
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_9e5a0612d4355adf_static_pages_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_0daf9d4b3925bce1___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
